#!/usr/bin/env python3
"""
覆盖率模块综合验证脚本
运行后端和前端的所有验证测试，生成综合报告
"""

import asyncio
import subprocess
import sys
import json
import os
from datetime import datetime
from pathlib import Path

class CoverageModuleVerification:
    """覆盖率模块综合验证类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.verification_results = {}
        
    def print_header(self, title: str):
        """打印标题"""
        print("\n" + "=" * 80)
        print(f"🔍 {title}")
        print("=" * 80)
    
    def print_step(self, step: str):
        """打印步骤"""
        print(f"\n📋 {step}")
        print("-" * 60)
    
    async def check_prerequisites(self) -> bool:
        """检查前置条件"""
        self.print_step("检查前置条件")
        
        # 检查Python环境
        try:
            python_version = sys.version_info
            if python_version.major >= 3 and python_version.minor >= 8:
                print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
            else:
                print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}.{python_version.micro}")
                return False
        except Exception as e:
            print(f"❌ Python环境检查失败: {e}")
            return False
        
        # 检查Node.js环境
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js版本: {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装或不可用")
                return False
        except Exception as e:
            print(f"❌ Node.js环境检查失败: {e}")
            return False
        
        # 检查项目文件结构
        required_files = [
            self.backend_dir / "test_coverage_verification.py",
            self.frontend_dir / "test_frontend_verification.js",
            self.backend_dir / "main.py",
            self.frontend_dir / "package.json"
        ]
        
        for file_path in required_files:
            if file_path.exists():
                print(f"✅ 文件存在: {file_path.name}")
            else:
                print(f"❌ 文件缺失: {file_path}")
                return False
        
        return True
    
    async def start_backend_server(self) -> bool:
        """启动后端服务"""
        self.print_step("启动后端服务")
        
        try:
            # 检查后端服务是否已经运行
            import aiohttp
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.get("http://localhost:8000/health", timeout=5) as response:
                        if response.status == 200:
                            print("✅ 后端服务已在运行")
                            return True
                except:
                    pass
            
            print("🚀 启动后端服务...")
            
            # 切换到后端目录并启动服务
            backend_process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=self.backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务启动
            await asyncio.sleep(5)
            
            # 检查服务是否启动成功
            async with aiohttp.ClientSession() as session:
                for attempt in range(10):
                    try:
                        async with session.get("http://localhost:8000/health", timeout=2) as response:
                            if response.status == 200:
                                print("✅ 后端服务启动成功")
                                return True
                    except:
                        await asyncio.sleep(1)
            
            print("❌ 后端服务启动失败")
            return False
            
        except Exception as e:
            print(f"❌ 启动后端服务异常: {e}")
            return False
    
    async def start_frontend_server(self) -> bool:
        """启动前端服务"""
        self.print_step("检查前端服务")
        
        try:
            # 检查前端服务是否已经运行
            import aiohttp
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.get("http://localhost:5173", timeout=5) as response:
                        if response.status == 200:
                            print("✅ 前端服务已在运行")
                            return True
                except:
                    pass
            
            print("⚠️  前端服务未运行")
            print("请在另一个终端中运行以下命令启动前端服务:")
            print(f"cd {self.frontend_dir}")
            print("npm run dev")
            print("\n等待前端服务启动后按回车继续...")
            input()
            
            # 再次检查前端服务
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.get("http://localhost:5173", timeout=5) as response:
                        if response.status == 200:
                            print("✅ 前端服务已启动")
                            return True
                except:
                    pass
            
            print("❌ 前端服务仍未可用")
            return False
            
        except Exception as e:
            print(f"❌ 检查前端服务异常: {e}")
            return False
    
    async def run_backend_tests(self) -> dict:
        """运行后端测试"""
        self.print_step("运行后端API测试")
        
        try:
            # 运行后端验证脚本
            result = subprocess.run(
                [sys.executable, "test_coverage_verification.py"],
                cwd=self.backend_dir,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            print("后端测试输出:")
            print(result.stdout)
            
            if result.stderr:
                print("后端测试错误:")
                print(result.stderr)
            
            # 查找生成的报告文件
            report_files = list(self.backend_dir.glob("coverage_verification_report_*.json"))
            if report_files:
                latest_report = max(report_files, key=os.path.getctime)
                with open(latest_report, 'r', encoding='utf-8') as f:
                    backend_report = json.load(f)
                
                print(f"✅ 后端测试完成，成功率: {backend_report['summary']['success_rate']}%")
                return backend_report
            else:
                print("❌ 未找到后端测试报告")
                return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
                
        except subprocess.TimeoutExpired:
            print("❌ 后端测试超时")
            return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
        except Exception as e:
            print(f"❌ 后端测试异常: {e}")
            return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
    
    async def run_frontend_tests(self) -> dict:
        """运行前端测试"""
        self.print_step("运行前端UI测试")
        
        try:
            # 检查是否安装了puppeteer
            result = subprocess.run(
                ['npm', 'list', 'puppeteer'],
                cwd=self.frontend_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                print("📦 安装puppeteer依赖...")
                install_result = subprocess.run(
                    ['npm', 'install', 'puppeteer'],
                    cwd=self.frontend_dir,
                    capture_output=True,
                    text=True
                )
                
                if install_result.returncode != 0:
                    print("❌ puppeteer安装失败")
                    return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
            
            # 运行前端验证脚本
            result = subprocess.run(
                ['node', 'test_frontend_verification.js'],
                cwd=self.frontend_dir,
                capture_output=True,
                text=True,
                timeout=180
            )
            
            print("前端测试输出:")
            print(result.stdout)
            
            if result.stderr:
                print("前端测试错误:")
                print(result.stderr)
            
            # 查找生成的报告文件
            report_files = list(self.frontend_dir.glob("frontend_verification_report_*.json"))
            if report_files:
                latest_report = max(report_files, key=os.path.getctime)
                with open(latest_report, 'r', encoding='utf-8') as f:
                    frontend_report = json.load(f)
                
                print(f"✅ 前端测试完成，成功率: {frontend_report['summary']['success_rate']}%")
                return frontend_report
            else:
                print("❌ 未找到前端测试报告")
                return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
                
        except subprocess.TimeoutExpired:
            print("❌ 前端测试超时")
            return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
        except Exception as e:
            print(f"❌ 前端测试异常: {e}")
            return {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "passed_tests": 0, "failed_tests": 0}}
    
    def generate_comprehensive_report(self, backend_report: dict, frontend_report: dict) -> dict:
        """生成综合报告"""
        self.print_step("生成综合验证报告")
        
        # 计算综合指标
        total_tests = backend_report['summary']['total_tests'] + frontend_report['summary']['total_tests']
        total_passed = backend_report['summary']['passed_tests'] + frontend_report['summary']['passed_tests']
        total_failed = backend_report['summary']['failed_tests'] + frontend_report['summary']['failed_tests']
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        comprehensive_report = {
            "verification_summary": {
                "overall_success_rate": round(overall_success_rate, 2),
                "total_tests": total_tests,
                "total_passed": total_passed,
                "total_failed": total_failed,
                "backend_success_rate": backend_report['summary']['success_rate'],
                "frontend_success_rate": frontend_report['summary']['success_rate'],
                "verification_time": datetime.now().isoformat()
            },
            "backend_report": backend_report,
            "frontend_report": frontend_report,
            "recommendations": [],
            "next_steps": []
        }
        
        # 生成建议和下一步计划
        if overall_success_rate >= 90:
            comprehensive_report["recommendations"].append("🎉 覆盖率模块功能验证优秀！可以进入下一阶段开发。")
            comprehensive_report["next_steps"].append("开始第3周个性化仪表板模块开发")
        elif overall_success_rate >= 80:
            comprehensive_report["recommendations"].append("✅ 覆盖率模块功能基本正常，建议修复少量问题后继续。")
            comprehensive_report["next_steps"].append("修复发现的问题")
            comprehensive_report["next_steps"].append("开始第3周个性化仪表板模块开发")
        elif overall_success_rate >= 60:
            comprehensive_report["recommendations"].append("⚠️  覆盖率模块存在一些问题，建议优先修复后再继续开发。")
            comprehensive_report["next_steps"].append("重点修复失败的测试用例")
            comprehensive_report["next_steps"].append("重新运行验证测试")
        else:
            comprehensive_report["recommendations"].append("❌ 覆盖率模块存在严重问题，需要全面检查和修复。")
            comprehensive_report["next_steps"].append("全面检查后端API实现")
            comprehensive_report["next_steps"].append("全面检查前端组件实现")
            comprehensive_report["next_steps"].append("修复所有问题后重新验证")
        
        # 具体问题分析
        if backend_report['summary']['success_rate'] < 80:
            comprehensive_report["recommendations"].append("🔧 后端API存在问题，重点检查数据模型和接口实现。")
        
        if frontend_report['summary']['success_rate'] < 80:
            comprehensive_report["recommendations"].append("🎨 前端界面存在问题，重点检查组件渲染和交互功能。")
        
        return comprehensive_report
    
    async def run_verification(self):
        """运行完整的验证流程"""
        self.print_header("覆盖率模块功能验证")
        
        # 1. 检查前置条件
        if not await self.check_prerequisites():
            print("❌ 前置条件检查失败，无法继续验证")
            return False
        
        # 2. 启动后端服务
        if not await self.start_backend_server():
            print("❌ 后端服务启动失败，无法继续验证")
            return False
        
        # 3. 检查前端服务
        if not await self.start_frontend_server():
            print("❌ 前端服务不可用，跳过前端测试")
            frontend_available = False
        else:
            frontend_available = True
        
        # 4. 运行后端测试
        backend_report = await self.run_backend_tests()
        
        # 5. 运行前端测试（如果前端服务可用）
        if frontend_available:
            frontend_report = await self.run_frontend_tests()
        else:
            frontend_report = {"summary": {"success_rate": 0, "total_tests": 0, "passed_tests": 0, "failed_tests": 0}}
        
        # 6. 生成综合报告
        comprehensive_report = self.generate_comprehensive_report(backend_report, frontend_report)
        
        # 7. 显示结果
        self.print_header("验证结果总结")
        
        print(f"📊 综合成功率: {comprehensive_report['verification_summary']['overall_success_rate']}%")
        print(f"📈 后端测试成功率: {comprehensive_report['verification_summary']['backend_success_rate']}%")
        print(f"🎨 前端测试成功率: {comprehensive_report['verification_summary']['frontend_success_rate']}%")
        print(f"🔢 总测试数: {comprehensive_report['verification_summary']['total_tests']}")
        print(f"✅ 通过测试: {comprehensive_report['verification_summary']['total_passed']}")
        print(f"❌ 失败测试: {comprehensive_report['verification_summary']['total_failed']}")
        
        print(f"\n💡 建议:")
        for recommendation in comprehensive_report['recommendations']:
            print(f"   {recommendation}")
        
        print(f"\n📋 下一步:")
        for step in comprehensive_report['next_steps']:
            print(f"   • {step}")
        
        # 8. 保存综合报告
        report_filename = f"coverage_module_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 综合验证报告已保存到: {report_filename}")
        
        return comprehensive_report['verification_summary']['overall_success_rate'] >= 80

async def main():
    """主函数"""
    verifier = CoverageModuleVerification()
    
    try:
        success = await verifier.run_verification()
        return success
    except KeyboardInterrupt:
        print("\n⚠️  验证被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 验证过程异常: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        sys.exit(1)
