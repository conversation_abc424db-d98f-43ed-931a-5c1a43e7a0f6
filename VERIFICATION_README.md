# 覆盖率模块功能验证指南

本文档说明如何验证第2周开发的测试覆盖率统计模块功能。

## 📋 验证内容

### 后端API验证
- ✅ 服务器健康检查
- ✅ 覆盖率API端点功能测试
- ✅ 数据准确性验证
- ✅ 图表数据格式验证
- ✅ API参数处理测试
- ✅ 错误处理测试

### 前端UI验证
- ✅ 页面加载测试
- ✅ 统计卡片渲染测试
- ✅ 图表组件渲染测试
- ✅ 交互功能测试
- ✅ 响应式设计测试
- ✅ 错误处理测试
- ✅ 性能指标测试

## 🚀 快速开始

### 方法1：自动化验证（推荐）

运行自动化验证脚本，会自动启动服务并运行所有测试：

```bash
# 给脚本执行权限
chmod +x start_verification.sh

# 运行自动化验证
./start_verification.sh
```

### 方法2：手动验证

#### 1. 启动后端服务

```bash
cd backend

# 创建虚拟环境（如果没有）
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
python main.py
```

后端服务将在 http://localhost:8000 启动

#### 2. 启动前端服务

在新的终端窗口中：

```bash
cd frontend

# 安装依赖（如果没有）
npm install

# 启动前端开发服务器
npm run dev
```

前端服务将在 http://localhost:5173 启动

#### 3. 运行后端验证

在新的终端窗口中：

```bash
cd backend
source venv/bin/activate  # 激活虚拟环境
python test_coverage_verification.py
```

#### 4. 运行前端验证

```bash
cd frontend

# 安装puppeteer（如果没有）
npm install puppeteer

# 运行前端验证
node test_frontend_verification.js
```

#### 5. 运行综合验证

```bash
# 在项目根目录
python3 verify_coverage_module.py
```

## 📊 验证报告

验证完成后会生成以下报告文件：

- `coverage_verification_report_*.json` - 后端API验证报告
- `frontend_verification_report_*.json` - 前端UI验证报告
- `coverage_module_verification_*.json` - 综合验证报告

## 🔍 手动测试

除了自动化验证，你也可以手动访问以下页面进行测试：

### 前端页面
- 主页: http://localhost:5173
- 覆盖率管理: http://localhost:5173/coverage-management

### 后端API
- 健康检查: http://localhost:8000/health
- API文档: http://localhost:8000/docs
- 覆盖率列表: http://localhost:8000/api/coverage/
- 覆盖率统计: http://localhost:8000/api/coverage/stats
- 覆盖率趋势: http://localhost:8000/api/coverage/trends
- 覆盖率分布: http://localhost:8000/api/coverage/distribution

## 🛠️ 故障排除

### 常见问题

#### 1. 后端服务启动失败
```bash
# 检查Python版本
python3 --version

# 检查依赖安装
pip list

# 检查端口占用
lsof -i :8000  # Linux/Mac
netstat -ano | findstr :8000  # Windows
```

#### 2. 前端服务启动失败
```bash
# 检查Node.js版本
node --version
npm --version

# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install

# 检查端口占用
lsof -i :5173  # Linux/Mac
netstat -ano | findstr :5173  # Windows
```

#### 3. 数据库连接问题
```bash
# 检查数据库文件
ls -la backend/quality_dashboard.db

# 重新初始化数据库
cd backend
python -c "from database import init_db; init_db()"
```

#### 4. 前端验证需要puppeteer
```bash
# 安装puppeteer
cd frontend
npm install puppeteer

# 如果安装失败，尝试设置镜像
npm config set puppeteer_download_host=https://npm.taobao.org/mirrors
npm install puppeteer
```

### 环境要求

- **Python**: 3.8+
- **Node.js**: 16+
- **npm**: 8+
- **操作系统**: Linux, macOS, Windows

### 依赖检查

运行以下命令检查环境：

```bash
# 检查Python
python3 --version
pip --version

# 检查Node.js
node --version
npm --version

# 检查项目文件
ls -la backend/main.py
ls -la frontend/package.json
```

## 📈 验证标准

### 成功标准
- 后端API测试成功率 ≥ 80%
- 前端UI测试成功率 ≥ 80%
- 综合验证成功率 ≥ 80%

### 验证项目
- [x] 所有API端点正常响应
- [x] 数据格式正确
- [x] 图表正常渲染
- [x] 交互功能正常
- [x] 错误处理正确
- [x] 响应式设计正常
- [x] 性能指标合格

## 🎯 下一步

验证通过后，可以继续进行：

1. **第3周开发**: 个性化仪表板模块
2. **功能优化**: 根据验证结果优化现有功能
3. **用户测试**: 邀请用户进行实际使用测试

## 📞 支持

如果在验证过程中遇到问题，请：

1. 查看生成的验证报告
2. 检查控制台错误信息
3. 确认环境配置正确
4. 参考故障排除指南

验证成功后，覆盖率模块就可以投入使用了！🎉
