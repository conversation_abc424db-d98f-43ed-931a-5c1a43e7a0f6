#!/usr/bin/env python3
"""
数据库初始化脚本
创建所有数据表并插入测试数据
"""

import asyncio
import sys
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

# 导入所有模型
from models.base import BaseModel
from models.dashboard import Project, Team
from models.defect import Defect, DefectSeverity, DefectPriority, DefectStatus, DefectType
from models.dashboard import User
from models.automation import TestCase, TestExecution, AutomationMetric
from models.performance import PerformanceMetric, ServiceMetric, SystemMetric
from models.quality_gate import QualityGateRule, QualityGateExecution, QualityGateResult
from models.coverage import CoverageMetric, FileCoverage, CoverageTarget, CoverageSource
from models.alert import Alert, AlertRule, AlertNotification, QualityMetric

# 数据库配置
DATABASE_URL = "sqlite+aiosqlite:///./quality_dashboard.db"

async def create_tables():
    """创建所有数据表"""
    print("🗄️  创建数据库表...")
    
    engine = create_async_engine(DATABASE_URL, echo=True)
    
    async with engine.begin() as conn:
        # 删除所有表（如果存在）
        await conn.run_sync(BaseModel.metadata.drop_all)
        # 创建所有表
        await conn.run_sync(BaseModel.metadata.create_all)
    
    await engine.dispose()
    print("✅ 数据库表创建完成")

async def insert_test_data():
    """插入测试数据"""
    print("📊 插入测试数据...")
    
    engine = create_async_engine(DATABASE_URL, echo=False)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        try:
            # 创建用户
            users = [
                User(username="zhangsan", full_name="张三", email="<EMAIL>", role="developer"),
                User(username="lisi", full_name="李四", email="<EMAIL>", role="tester"),
                User(username="wangwu", full_name="王五", email="<EMAIL>", role="manager"),
                User(username="zhaoliu", full_name="赵六", email="<EMAIL>", role="developer"),
            ]
            session.add_all(users)
            await session.flush()  # 获取ID
            
            # 创建项目
            projects = [
                Project(
                    name="用户中心",
                    description="用户管理和认证系统",
                    status="active",
                    team_id=1
                ),
                Project(
                    name="订单系统",
                    description="电商订单处理系统",
                    status="active",
                    team_id=2
                ),
                Project(
                    name="支付系统",
                    description="支付网关和处理系统",
                    status="active",
                    team_id=3
                ),
            ]
            session.add_all(projects)
            await session.flush()
            
            # 创建团队
            teams = [
                Team(
                    name="前端团队",
                    description="负责前端开发",
                    lead_name="张三",
                    member_count=5
                ),
                Team(
                    name="后端团队",
                    description="负责后端开发",
                    lead_name="李四",
                    member_count=8
                ),
                Team(
                    name="测试团队",
                    description="负责质量保证",
                    lead_name="王五",
                    member_count=4
                ),
            ]
            session.add_all(teams)
            await session.flush()
            
            # 创建缺陷数据
            now = datetime.now()
            defects = []
            
            # 生成过去30天的缺陷数据
            for i in range(50):
                days_ago = i % 30
                found_date = now - timedelta(days=days_ago)
                
                # 随机选择严重程度
                severities = [DefectSeverity.CRITICAL, DefectSeverity.HIGH, DefectSeverity.MEDIUM, DefectSeverity.LOW]
                severity = severities[i % 4]
                
                # 随机选择状态
                statuses = [DefectStatus.OPEN, DefectStatus.IN_PROGRESS, DefectStatus.RESOLVED, DefectStatus.CLOSED]
                status = statuses[i % 4]
                
                # 如果是已解决状态，设置解决时间
                resolved_date = None
                if status in [DefectStatus.RESOLVED, DefectStatus.CLOSED]:
                    resolved_date = found_date + timedelta(days=(i % 7) + 1)
                
                defect = Defect(
                    title=f"缺陷 #{i+1:03d} - {['登录失败', '页面崩溃', '数据错误', '性能问题', '界面异常'][i % 5]}",
                    description=f"这是第{i+1}个测试缺陷的详细描述。发现于{found_date.strftime('%Y-%m-%d')}。",
                    severity=severity,
                    priority=DefectPriority.HIGH if severity == DefectSeverity.CRITICAL else DefectPriority.MEDIUM,
                    status=status,
                    defect_type=DefectType.FUNCTIONAL,
                    project_id=(i % 3) + 1,
                    assignee_id=(i % 4) + 1,
                    reporter_id=((i + 1) % 4) + 1,
                    found_date=found_date,
                    resolved_date=resolved_date,
                    environment=["开发环境", "测试环境", "预生产环境"][i % 3],
                    version=f"v1.{i % 10}.{(i * 2) % 10}"
                )
                defects.append(defect)
            
            session.add_all(defects)
            
            # 创建测试用例数据
            test_cases = []
            for i in range(30):
                test_case = TestCase(
                    name=f"测试用例 #{i+1:03d}",
                    description=f"自动化测试用例{i+1}的描述",
                    test_type="api" if i % 2 == 0 else "ui",
                    project_id=(i % 3) + 1,
                    status="active" if i % 4 != 0 else "inactive",
                    priority="high" if i % 3 == 0 else "medium"
                )
                test_cases.append(test_case)
            
            session.add_all(test_cases)

            # 创建覆盖率数据
            coverage_metrics = []
            for i in range(20):
                days_ago = i % 10
                measurement_date = now - timedelta(days=days_ago)

                # 生成随机覆盖率数据
                line_coverage = 60 + (i % 30)  # 60-90%
                branch_coverage = 55 + (i % 35)  # 55-90%
                function_coverage = 65 + (i % 25)  # 65-90%

                coverage_metric = CoverageMetric(
                    project_id=(i % 3) + 1,
                    branch_name="main" if i % 3 == 0 else f"feature-{i}",
                    commit_hash=f"abc123{i:02d}",
                    build_number=f"build-{i+100}",
                    line_coverage=line_coverage,
                    branch_coverage=branch_coverage,
                    function_coverage=function_coverage,
                    statement_coverage=line_coverage - 2,
                    condition_coverage=branch_coverage - 3,
                    total_lines=1000 + (i * 50),
                    covered_lines=int((1000 + (i * 50)) * line_coverage / 100),
                    total_branches=200 + (i * 10),
                    covered_branches=int((200 + (i * 10)) * branch_coverage / 100),
                    total_functions=100 + (i * 5),
                    covered_functions=int((100 + (i * 5)) * function_coverage / 100),
                    source=CoverageSource.JEST if i % 2 == 0 else CoverageSource.PYTEST,
                    measurement_date=measurement_date
                )
                coverage_metrics.append(coverage_metric)

            session.add_all(coverage_metrics)

            # 提交所有数据
            await session.commit()
            print("✅ 测试数据插入完成")
            
        except Exception as e:
            await session.rollback()
            print(f"❌ 插入测试数据失败: {str(e)}")
            raise
        finally:
            await session.close()
    
    await engine.dispose()

async def verify_data():
    """验证数据插入结果"""
    print("🔍 验证数据...")
    
    engine = create_async_engine(DATABASE_URL, echo=False)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        try:
            # 检查各表的数据量
            tables = [
                ("users", User),
                ("projects", Project),
                ("teams", Team),
                ("defects", Defect),
                ("test_cases", TestCase),
                ("coverage_metrics", CoverageMetric),
                ("alert_rules", AlertRule),
                ("alerts", Alert),
                ("quality_metrics", QualityMetric)
            ]
            
            for table_name, model in tables:
                result = await session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                print(f"📊 {table_name}: {count} 条记录")
            
            print("✅ 数据验证完成")
            
        except Exception as e:
            print(f"❌ 数据验证失败: {str(e)}")
        finally:
            await session.close()
    
    await engine.dispose()

async def main():
    """主函数"""
    print("🚀 开始数据库初始化")
    print("=" * 50)
    
    try:
        # 创建表
        await create_tables()
        
        # 插入测试数据
        await insert_test_data()
        
        # 验证数据
        await verify_data()
        
        print("\n" + "=" * 50)
        print("🎉 数据库初始化完成！")
        print("💡 现在可以启动后端服务器进行测试")
        
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
