#!/usr/bin/env python3
"""
覆盖率模块数据库初始化脚本
创建表结构并插入模拟数据
"""

import asyncio
import sys
from datetime import datetime, timedelta
import random
from sqlalchemy.ext.asyncio import AsyncSession
from database import engine, Base, AsyncSessionLocal
from models.dashboard import Project
from models.coverage import CoverageMetric, FileCoverage, CoverageTarget

async def create_tables():
    """创建所有表"""
    print("🔧 创建数据库表...")
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("✅ 数据库表创建完成")

async def create_sample_projects(db: AsyncSession):
    """创建示例项目"""
    print("📁 创建示例项目...")
    
    from models.dashboard import ProjectStatus

    projects = [
        {
            "name": "质量大盘前端",
            "description": "质量大盘系统前端应用，基于Vue.js开发",
            "status": ProjectStatus.ACTIVE
        },
        {
            "name": "质量大盘后端",
            "description": "质量大盘系统后端API，基于FastAPI开发",
            "status": ProjectStatus.ACTIVE
        },
        {
            "name": "自动化测试框架",
            "description": "端到端自动化测试框架，支持多种测试场景",
            "status": ProjectStatus.ACTIVE
        },
        {
            "name": "性能监控系统",
            "description": "应用性能监控和分析系统",
            "status": ProjectStatus.ACTIVE
        },
        {
            "name": "数据分析平台",
            "description": "大数据分析和可视化平台",
            "status": ProjectStatus.ACTIVE
        }
    ]
    
    created_projects = []
    for project_data in projects:
        project = Project(**project_data)
        db.add(project)
        created_projects.append(project)
    
    await db.commit()
    
    # 刷新以获取ID
    for project in created_projects:
        await db.refresh(project)
    
    print(f"✅ 创建了 {len(created_projects)} 个示例项目")
    return created_projects

async def create_coverage_targets(db: AsyncSession, projects):
    """创建覆盖率目标"""
    print("🎯 创建覆盖率目标...")
    
    targets = []
    for project in projects:
        target = CoverageTarget(
            project_id=project.id,
            target_line_coverage=80.0,
            target_branch_coverage=75.0,
            target_function_coverage=85.0,
            warning_threshold=70.0,
            critical_threshold=60.0,
            is_active=True
        )
        db.add(target)
        targets.append(target)
    
    await db.commit()
    print(f"✅ 创建了 {len(targets)} 个覆盖率目标")
    return targets

async def create_coverage_metrics(db: AsyncSession, projects):
    """创建覆盖率指标数据"""
    print("📊 创建覆盖率指标数据...")
    
    sources = ["Jest", "JaCoCo", "SonarQube", "Pytest", "Coverage.py"]
    branches = ["main", "develop", "feature/coverage", "release/v1.0"]
    
    metrics = []
    
    # 为每个项目创建多条覆盖率记录
    for project in projects:
        # 根据项目名称选择合适的数据源
        if "前端" in project.name:
            project_sources = ["Jest", "SonarQube"]
        elif "后端" in project.name or "框架" in project.name or "平台" in project.name:
            project_sources = ["Pytest", "Coverage.py", "SonarQube"]
        elif "监控" in project.name:
            project_sources = ["JaCoCo", "SonarQube"]
        else:
            project_sources = ["SonarQube"]
        
        # 生成过去30天的数据
        for i in range(15):  # 每个项目15条记录
            measurement_date = datetime.now() - timedelta(days=random.randint(0, 30))
            
            # 生成覆盖率数据，确保有一定的合理性
            base_coverage = random.uniform(60, 95)
            line_coverage = round(base_coverage + random.uniform(-5, 5), 1)
            branch_coverage = round(base_coverage + random.uniform(-10, 10), 1)
            function_coverage = round(base_coverage + random.uniform(-5, 15), 1)
            
            # 确保覆盖率在0-100范围内
            line_coverage = max(0, min(100, line_coverage))
            branch_coverage = max(0, min(100, branch_coverage))
            function_coverage = max(0, min(100, function_coverage))
            
            # 生成行数数据
            total_lines = random.randint(1000, 10000)
            covered_lines = int(total_lines * line_coverage / 100)
            
            total_branches = random.randint(200, 2000)
            covered_branches = int(total_branches * branch_coverage / 100)
            
            total_functions = random.randint(50, 500)
            covered_functions = int(total_functions * function_coverage / 100)
            
            metric = CoverageMetric(
                project_id=project.id,
                branch_name=random.choice(branches),
                commit_hash=f"abc{random.randint(1000, 9999)}def{random.randint(1000, 9999)}",
                build_number=f"build-{random.randint(100, 999)}",
                line_coverage=line_coverage,
                branch_coverage=branch_coverage,
                function_coverage=function_coverage,
                statement_coverage=round(line_coverage + random.uniform(-2, 2), 1),
                condition_coverage=round(branch_coverage + random.uniform(-3, 3), 1),
                total_lines=total_lines,
                covered_lines=covered_lines,
                total_branches=total_branches,
                covered_branches=covered_branches,
                total_functions=total_functions,
                covered_functions=covered_functions,
                source=random.choice(project_sources),
                measurement_date=measurement_date,
                report_url=f"https://coverage.example.com/{project.name.lower().replace(' ', '-')}/{random.randint(1000, 9999)}",
                raw_data={"tool_version": "1.0.0", "execution_time": random.randint(30, 300)}
            )
            
            db.add(metric)
            metrics.append(metric)
    
    await db.commit()
    
    # 刷新以获取ID
    for metric in metrics:
        await db.refresh(metric)
    
    print(f"✅ 创建了 {len(metrics)} 条覆盖率指标记录")
    return metrics

async def create_file_coverage(db: AsyncSession, metrics):
    """创建文件覆盖率数据"""
    print("📄 创建文件覆盖率数据...")
    
    # 文件模板
    file_templates = {
        "JavaScript": [
            {"path": "/src/components/charts/CoverageTrendChart.vue", "package": "components.charts"},
            {"path": "/src/views/CoverageManagement.vue", "package": "views"},
            {"path": "/src/stores/coverage.js", "package": "stores"},
            {"path": "/src/utils/chartConfig.js", "package": "utils"},
            {"path": "/src/services/api.js", "package": "services"},
        ],
        "Python": [
            {"path": "/backend/api/coverage.py", "package": "api"},
            {"path": "/backend/models/coverage.py", "package": "models"},
            {"path": "/backend/services/coverage_integration.py", "package": "services"},
            {"path": "/backend/database.py", "package": "core"},
            {"path": "/backend/main.py", "package": "core"},
        ],
        "Java": [
            {"path": "/src/main/java/com/example/service/CoverageService.java", "package": "com.example.service"},
            {"path": "/src/main/java/com/example/controller/CoverageController.java", "package": "com.example.controller"},
            {"path": "/src/main/java/com/example/model/Coverage.java", "package": "com.example.model"},
            {"path": "/src/main/java/com/example/util/CoverageUtil.java", "package": "com.example.util"},
        ]
    }
    
    file_coverages = []
    
    # 为每个覆盖率指标创建文件覆盖率数据
    for metric in metrics[:30]:  # 只为前30个指标创建文件数据，避免数据过多
        # 获取项目信息
        project_query = await db.get(Project, metric.project_id)
        if not project_query:
            continue

        # 根据项目名称判断语言类型
        if "前端" in project_query.name:
            project_language = "JavaScript"
        elif "监控" in project_query.name:
            project_language = "Java"
        else:
            project_language = "Python"

        templates = file_templates.get(project_language, file_templates["Python"])
        
        # 为每个模板创建文件覆盖率记录
        for template in templates:
            total_lines = random.randint(50, 500)
            coverage_rate = random.uniform(50, 100)
            covered_lines = int(total_lines * coverage_rate / 100)
            uncovered_lines = total_lines - covered_lines
            
            # 生成未覆盖行号
            if uncovered_lines > 0:
                uncovered_line_numbers = []
                for _ in range(min(uncovered_lines, 10)):  # 最多显示10个未覆盖行号段
                    start_line = random.randint(1, total_lines - 5)
                    end_line = start_line + random.randint(1, 5)
                    uncovered_line_numbers.append(f"{start_line}-{end_line}")
                uncovered_lines_str = ", ".join(uncovered_line_numbers)
            else:
                uncovered_lines_str = ""
            
            file_coverage = FileCoverage(
                coverage_metric_id=metric.id,
                file_path=template["path"],
                file_name=template["path"].split("/")[-1],
                package_name=template["package"],
                line_coverage=round(coverage_rate, 1),
                branch_coverage=round(coverage_rate + random.uniform(-10, 10), 1),
                function_coverage=round(coverage_rate + random.uniform(-5, 15), 1),
                total_lines=total_lines,
                covered_lines=covered_lines,
                uncovered_lines=uncovered_lines_str
            )
            
            db.add(file_coverage)
            file_coverages.append(file_coverage)
    
    await db.commit()
    print(f"✅ 创建了 {len(file_coverages)} 条文件覆盖率记录")
    return file_coverages

async def main():
    """主函数"""
    print("🚀 开始初始化覆盖率模块数据...")
    print("=" * 60)
    
    try:
        # 1. 创建表结构
        await create_tables()
        
        # 2. 创建数据库会话
        async with AsyncSessionLocal() as db:
            # 3. 创建示例项目
            projects = await create_sample_projects(db)
            
            # 4. 创建覆盖率目标
            await create_coverage_targets(db, projects)
            
            # 5. 创建覆盖率指标数据
            metrics = await create_coverage_metrics(db, projects)
            
            # 6. 创建文件覆盖率数据
            await create_file_coverage(db, metrics)
        
        print("\n" + "=" * 60)
        print("🎉 覆盖率模块数据初始化完成！")
        print("\n📊 数据统计:")
        print(f"   项目数: {len(projects)}")
        print(f"   覆盖率指标记录: {len(metrics)}")
        print(f"   文件覆盖率记录: 约 {len(metrics) * 5}")
        print("\n🌐 现在可以启动服务并访问:")
        print("   后端API: http://localhost:8000/docs")
        print("   前端页面: http://localhost:5173/coverage-management")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 数据初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  初始化被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 初始化异常: {e}")
        sys.exit(1)
