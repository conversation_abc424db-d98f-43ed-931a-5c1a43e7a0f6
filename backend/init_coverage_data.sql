-- 覆盖率模块数据库初始化SQL脚本

-- 创建项目表（如果不存在）
CREATE TABLE IF NOT EXISTS projects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    team_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建覆盖率目标表
CREATE TABLE IF NOT EXISTS coverage_targets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    target_line_coverage FLOAT DEFAULT 80.0,
    target_branch_coverage FLOAT DEFAULT 75.0,
    target_function_coverage FLOAT DEFAULT 85.0,
    warning_threshold FLOAT DEFAULT 70.0,
    critical_threshold FLOAT DEFAULT 60.0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (project_id) REFERENCES projects (id)
);

-- 创建覆盖率指标表
CREATE TABLE IF NOT EXISTS coverage_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id INTEGER NOT NULL,
    branch_name VARCHAR(100) DEFAULT 'main',
    commit_hash VARCHAR(100),
    build_number VARCHAR(100),
    line_coverage FLOAT,
    branch_coverage FLOAT,
    function_coverage FLOAT,
    statement_coverage FLOAT,
    condition_coverage FLOAT,
    total_lines INTEGER,
    covered_lines INTEGER,
    total_branches INTEGER,
    covered_branches INTEGER,
    total_functions INTEGER,
    covered_functions INTEGER,
    source VARCHAR(50),
    measurement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    report_url TEXT,
    raw_data TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects (id)
);

-- 创建文件覆盖率表
CREATE TABLE IF NOT EXISTS file_coverage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    coverage_metric_id INTEGER NOT NULL,
    file_path TEXT NOT NULL,
    file_name VARCHAR(255),
    package_name VARCHAR(255),
    line_coverage FLOAT,
    branch_coverage FLOAT,
    function_coverage FLOAT,
    total_lines INTEGER,
    covered_lines INTEGER,
    uncovered_lines TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (coverage_metric_id) REFERENCES coverage_metrics (id)
);

-- 插入示例项目
INSERT OR IGNORE INTO projects (name, description, status) VALUES
('质量大盘前端', '质量大盘系统前端应用，基于Vue.js开发', 'active'),
('质量大盘后端', '质量大盘系统后端API，基于FastAPI开发', 'active'),
('自动化测试框架', '端到端自动化测试框架，支持多种测试场景', 'active'),
('性能监控系统', '应用性能监控和分析系统', 'active'),
('数据分析平台', '大数据分析和可视化平台', 'active');

-- 插入覆盖率目标
INSERT OR IGNORE INTO coverage_targets (project_id, target_line_coverage, target_branch_coverage, target_function_coverage, warning_threshold, critical_threshold, is_active)
SELECT id, 80.0, 75.0, 85.0, 70.0, 60.0, 1 FROM projects;

-- 插入覆盖率指标数据
-- 质量大盘前端项目的覆盖率数据
INSERT INTO coverage_metrics (project_id, branch_name, commit_hash, build_number, line_coverage, branch_coverage, function_coverage, statement_coverage, condition_coverage, total_lines, covered_lines, total_branches, covered_branches, total_functions, covered_functions, source, measurement_date, report_url, raw_data) VALUES
(1, 'main', 'abc1234def5678', 'build-101', 85.2, 78.5, 92.1, 84.8, 76.2, 2500, 2130, 450, 353, 120, 110, 'Jest', datetime('now', '-1 day'), 'https://coverage.example.com/frontend/1001', '{"tool_version": "1.0.0"}'),
(1, 'main', 'abc2345def6789', 'build-102', 87.1, 80.3, 94.2, 86.7, 78.9, 2520, 2195, 460, 369, 125, 118, 'Jest', datetime('now', '-2 days'), 'https://coverage.example.com/frontend/1002', '{"tool_version": "1.0.0"}'),
(1, 'develop', 'abc3456def7890', 'build-103', 82.8, 75.6, 89.4, 82.1, 74.3, 2480, 2054, 440, 333, 118, 105, 'Jest', datetime('now', '-3 days'), 'https://coverage.example.com/frontend/1003', '{"tool_version": "1.0.0"}'),
(1, 'main', 'abc4567def8901', 'build-104', 88.5, 82.1, 95.3, 87.9, 80.7, 2550, 2256, 470, 386, 128, 122, 'SonarQube', datetime('now', '-4 days'), 'https://coverage.example.com/frontend/1004', '{"tool_version": "1.0.0"}'),
(1, 'develop', 'abc5678def9012', 'build-105', 84.3, 77.8, 91.6, 83.9, 76.1, 2490, 2099, 450, 350, 122, 112, 'Jest', datetime('now', '-5 days'), 'https://coverage.example.com/frontend/1005', '{"tool_version": "1.0.0"}');

-- 质量大盘后端项目的覆盖率数据
INSERT INTO coverage_metrics (project_id, branch_name, commit_hash, build_number, line_coverage, branch_coverage, function_coverage, statement_coverage, condition_coverage, total_lines, covered_lines, total_branches, covered_branches, total_functions, covered_functions, source, measurement_date, report_url, raw_data) VALUES
(2, 'main', 'def1234abc5678', 'build-201', 91.2, 85.7, 96.3, 90.8, 84.2, 3200, 2918, 580, 497, 180, 173, 'Pytest', datetime('now', '-1 day'), 'https://coverage.example.com/backend/2001', '{"tool_version": "1.0.0"}'),
(2, 'main', 'def2345abc6789', 'build-202', 92.8, 87.4, 97.1, 92.3, 86.1, 3250, 3016, 590, 516, 185, 180, 'Coverage.py', datetime('now', '-2 days'), 'https://coverage.example.com/backend/2002', '{"tool_version": "1.0.0"}'),
(2, 'develop', 'def3456abc7890', 'build-203', 89.5, 83.2, 94.8, 88.9, 81.7, 3180, 2846, 570, 474, 175, 166, 'Pytest', datetime('now', '-3 days'), 'https://coverage.example.com/backend/2003', '{"tool_version": "1.0.0"}'),
(2, 'main', 'def4567abc8901', 'build-204', 93.1, 88.6, 98.2, 92.7, 87.3, 3280, 3054, 600, 532, 190, 187, 'SonarQube', datetime('now', '-4 days'), 'https://coverage.example.com/backend/2004', '{"tool_version": "1.0.0"}'),
(2, 'develop', 'def5678abc9012', 'build-205', 90.7, 84.9, 95.6, 90.1, 83.4, 3220, 2921, 580, 492, 182, 174, 'Pytest', datetime('now', '-5 days'), 'https://coverage.example.com/backend/2005', '{"tool_version": "1.0.0"}');

-- 自动化测试框架项目的覆盖率数据
INSERT INTO coverage_metrics (project_id, branch_name, commit_hash, build_number, line_coverage, branch_coverage, function_coverage, statement_coverage, condition_coverage, total_lines, covered_lines, total_branches, covered_branches, total_functions, covered_functions, source, measurement_date, report_url, raw_data) VALUES
(3, 'main', 'ghi1234jkl5678', 'build-301', 78.5, 72.3, 85.7, 77.9, 70.8, 1800, 1413, 320, 231, 95, 81, 'Pytest', datetime('now', '-1 day'), 'https://coverage.example.com/automation/3001', '{"tool_version": "1.0.0"}'),
(3, 'main', 'ghi2345jkl6789', 'build-302', 80.2, 74.6, 87.3, 79.7, 73.1, 1820, 1460, 330, 246, 98, 86, 'Coverage.py', datetime('now', '-2 days'), 'https://coverage.example.com/automation/3002', '{"tool_version": "1.0.0"}'),
(3, 'develop', 'ghi3456jkl7890', 'build-303', 76.8, 70.1, 83.9, 76.2, 68.7, 1780, 1367, 310, 217, 92, 77, 'Pytest', datetime('now', '-3 days'), 'https://coverage.example.com/automation/3003', '{"tool_version": "1.0.0"}'),
(3, 'main', 'ghi4567jkl8901', 'build-304', 81.7, 76.2, 88.9, 81.1, 74.8, 1850, 1511, 340, 259, 100, 89, 'SonarQube', datetime('now', '-4 days'), 'https://coverage.example.com/automation/3004', '{"tool_version": "1.0.0"}'),
(3, 'develop', 'ghi5678jkl9012', 'build-305', 79.3, 73.5, 86.1, 78.7, 72.2, 1790, 1420, 325, 239, 96, 83, 'Pytest', datetime('now', '-5 days'), 'https://coverage.example.com/automation/3005', '{"tool_version": "1.0.0"}');

-- 性能监控系统项目的覆盖率数据
INSERT INTO coverage_metrics (project_id, branch_name, commit_hash, build_number, line_coverage, branch_coverage, function_coverage, statement_coverage, condition_coverage, total_lines, covered_lines, total_branches, covered_branches, total_functions, covered_functions, source, measurement_date, report_url, raw_data) VALUES
(4, 'main', 'mno1234pqr5678', 'build-401', 86.4, 79.8, 92.7, 85.9, 78.3, 4200, 3629, 750, 599, 220, 204, 'JaCoCo', datetime('now', '-1 day'), 'https://coverage.example.com/monitor/4001', '{"tool_version": "1.0.0"}'),
(4, 'main', 'mno2345pqr6789', 'build-402', 88.1, 81.5, 94.2, 87.6, 80.1, 4250, 3744, 760, 620, 225, 212, 'JaCoCo', datetime('now', '-2 days'), 'https://coverage.example.com/monitor/4002', '{"tool_version": "1.0.0"}'),
(4, 'develop', 'mno3456pqr7890', 'build-403', 84.7, 77.9, 90.8, 84.1, 76.4, 4180, 3540, 740, 576, 215, 195, 'JaCoCo', datetime('now', '-3 days'), 'https://coverage.example.com/monitor/4003', '{"tool_version": "1.0.0"}'),
(4, 'main', 'mno4567pqr8901', 'build-404', 89.3, 83.2, 95.6, 88.8, 81.7, 4300, 3840, 780, 649, 230, 220, 'SonarQube', datetime('now', '-4 days'), 'https://coverage.example.com/monitor/4004', '{"tool_version": "1.0.0"}'),
(4, 'develop', 'mno5678pqr9012', 'build-405', 87.2, 80.6, 93.4, 86.7, 79.1, 4220, 3680, 765, 617, 225, 210, 'JaCoCo', datetime('now', '-5 days'), 'https://coverage.example.com/monitor/4005', '{"tool_version": "1.0.0"}');

-- 数据分析平台项目的覆盖率数据
INSERT INTO coverage_metrics (project_id, branch_name, commit_hash, build_number, line_coverage, branch_coverage, function_coverage, statement_coverage, condition_coverage, total_lines, covered_lines, total_branches, covered_branches, total_functions, covered_functions, source, measurement_date, report_url, raw_data) VALUES
(5, 'main', 'stu1234vwx5678', 'build-501', 82.6, 76.4, 89.1, 82.1, 75.2, 3800, 3139, 680, 520, 200, 178, 'Pytest', datetime('now', '-1 day'), 'https://coverage.example.com/analytics/5001', '{"tool_version": "1.0.0"}'),
(5, 'main', 'stu2345vwx6789', 'build-502', 84.3, 78.7, 90.8, 83.8, 77.4, 3850, 3245, 690, 543, 205, 186, 'Coverage.py', datetime('now', '-2 days'), 'https://coverage.example.com/analytics/5002', '{"tool_version": "1.0.0"}'),
(5, 'develop', 'stu3456vwx7890', 'build-503', 80.9, 74.2, 87.5, 80.3, 72.9, 3750, 3034, 670, 497, 195, 171, 'Pytest', datetime('now', '-3 days'), 'https://coverage.example.com/analytics/5003', '{"tool_version": "1.0.0"}'),
(5, 'main', 'stu4567vwx8901', 'build-504', 85.7, 80.1, 92.3, 85.2, 78.8, 3900, 3342, 700, 561, 210, 194, 'SonarQube', datetime('now', '-4 days'), 'https://coverage.example.com/analytics/5004', '{"tool_version": "1.0.0"}'),
(5, 'develop', 'stu5678vwx9012', 'build-505', 83.4, 77.6, 90.2, 82.9, 76.3, 3820, 3186, 685, 532, 202, 182, 'Pytest', datetime('now', '-5 days'), 'https://coverage.example.com/analytics/5005', '{"tool_version": "1.0.0"}');

-- 插入文件覆盖率数据（示例）
INSERT INTO file_coverage (coverage_metric_id, file_path, file_name, package_name, line_coverage, branch_coverage, function_coverage, total_lines, covered_lines, uncovered_lines) VALUES
(1, '/src/components/charts/CoverageTrendChart.vue', 'CoverageTrendChart.vue', 'components.charts', 92.5, 88.2, 95.0, 120, 111, '15-18, 45-47'),
(1, '/src/views/CoverageManagement.vue', 'CoverageManagement.vue', 'views', 87.3, 82.1, 90.5, 180, 157, '25-30, 78-82, 155-160'),
(1, '/src/stores/coverage.js', 'coverage.js', 'stores', 94.2, 91.7, 97.8, 85, 80, '12-15'),
(2, '/backend/api/coverage.py', 'coverage.py', 'api', 96.8, 94.2, 98.5, 150, 145, '23-25'),
(2, '/backend/models/coverage.py', 'coverage.py', 'models', 98.5, 97.1, 100.0, 65, 64, '45'),
(2, '/backend/services/coverage_integration.py', 'coverage_integration.py', 'services', 89.7, 85.3, 92.1, 200, 179, '45-52, 123-128, 187-192');

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_coverage_metrics_project_id ON coverage_metrics(project_id);
CREATE INDEX IF NOT EXISTS idx_coverage_metrics_measurement_date ON coverage_metrics(measurement_date);
CREATE INDEX IF NOT EXISTS idx_coverage_metrics_branch_name ON coverage_metrics(branch_name);
CREATE INDEX IF NOT EXISTS idx_file_coverage_metric_id ON file_coverage(coverage_metric_id);

-- 显示插入的数据统计
SELECT 'Projects' as table_name, COUNT(*) as count FROM projects
UNION ALL
SELECT 'Coverage Targets' as table_name, COUNT(*) as count FROM coverage_targets
UNION ALL
SELECT 'Coverage Metrics' as table_name, COUNT(*) as count FROM coverage_metrics
UNION ALL
SELECT 'File Coverage' as table_name, COUNT(*) as count FROM file_coverage;
