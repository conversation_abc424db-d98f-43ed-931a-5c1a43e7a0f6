#!/usr/bin/env python3
"""
缺陷管理模块测试运行器
统一运行单元测试、集成测试、性能测试和用户验收测试
"""

import asyncio
import subprocess
import sys
import os
import time
from datetime import datetime

class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = None
    
    def print_header(self, title):
        """打印测试标题"""
        print("\n" + "=" * 60)
        print(f"🧪 {title}")
        print("=" * 60)
    
    def print_section(self, title):
        """打印测试章节"""
        print(f"\n📋 {title}")
        print("-" * 40)
    
    def run_pytest(self, test_file, test_name):
        """运行pytest测试"""
        self.print_section(f"运行 {test_name}")
        
        try:
            # 检查测试文件是否存在
            if not os.path.exists(test_file):
                print(f"❌ 测试文件不存在: {test_file}")
                self.test_results[test_name] = {"passed": False, "error": "文件不存在"}
                return False
            
            # 运行pytest
            cmd = [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"]
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(os.path.abspath(__file__)))
            
            if result.returncode == 0:
                print(f"✅ {test_name} 通过")
                self.test_results[test_name] = {"passed": True, "output": result.stdout}
                return True
            else:
                print(f"❌ {test_name} 失败")
                print(f"错误输出:\n{result.stderr}")
                self.test_results[test_name] = {"passed": False, "error": result.stderr}
                return False
                
        except Exception as e:
            print(f"❌ 运行 {test_name} 时发生异常: {str(e)}")
            self.test_results[test_name] = {"passed": False, "error": str(e)}
            return False
    
    async def run_async_test(self, test_file, test_name):
        """运行异步测试"""
        self.print_section(f"运行 {test_name}")
        
        try:
            # 检查测试文件是否存在
            if not os.path.exists(test_file):
                print(f"❌ 测试文件不存在: {test_file}")
                self.test_results[test_name] = {"passed": False, "error": "文件不存在"}
                return False
            
            # 动态导入并运行测试
            spec = __import__(os.path.splitext(os.path.basename(test_file))[0])
            
            if hasattr(spec, 'main'):
                result = await spec.main()
                if result == 0:
                    print(f"✅ {test_name} 通过")
                    self.test_results[test_name] = {"passed": True}
                    return True
                else:
                    print(f"❌ {test_name} 失败")
                    self.test_results[test_name] = {"passed": False, "error": "测试返回非零退出码"}
                    return False
            else:
                print(f"❌ {test_name} 没有main函数")
                self.test_results[test_name] = {"passed": False, "error": "没有main函数"}
                return False
                
        except Exception as e:
            print(f"❌ 运行 {test_name} 时发生异常: {str(e)}")
            self.test_results[test_name] = {"passed": False, "error": str(e)}
            return False
    
    def check_server_status(self):
        """检查服务器状态"""
        self.print_section("检查服务器状态")
        
        try:
            import requests
            response = requests.get("http://localhost:8000/", timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务器运行正常")
                return True
            else:
                print(f"⚠️  后端服务器响应异常: HTTP {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到后端服务器 (http://localhost:8000)")
            print("💡 请确保后端服务器正在运行:")
            print("   cd backend && python main.py")
            return False
        except Exception as e:
            print(f"❌ 检查服务器状态时发生异常: {str(e)}")
            return False
    
    def install_dependencies(self):
        """安装测试依赖"""
        self.print_section("安装测试依赖")
        
        dependencies = [
            "pytest",
            "pytest-asyncio", 
            "httpx",
            "aiohttp",
            "psutil",
            "requests"
        ]
        
        for dep in dependencies:
            try:
                __import__(dep.replace("-", "_"))
                print(f"✅ {dep} 已安装")
            except ImportError:
                print(f"📦 安装 {dep}...")
                try:
                    subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                                 check=True, capture_output=True)
                    print(f"✅ {dep} 安装成功")
                except subprocess.CalledProcessError as e:
                    print(f"❌ {dep} 安装失败: {e}")
                    return False
        
        return True
    
    def generate_test_report(self):
        """生成测试报告"""
        self.print_header("测试报告")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result["passed"])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   通过率: {passed_tests/total_tests*100:.1f}%")
        
        if self.start_time:
            duration = time.time() - self.start_time
            print(f"   耗时: {duration:.1f}秒")
        
        print(f"\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["passed"] else "❌ FAIL"
            print(f"   {status} {test_name}")
            if not result["passed"] and "error" in result:
                print(f"      错误: {result['error'][:100]}...")
        
        print(f"\n🎯 总结:")
        if passed_tests == total_tests:
            print("🎉 所有测试通过！缺陷管理模块第1周任务完成")
            print("✅ 可以继续进行第2周任务：测试覆盖率统计模块")
        elif passed_tests >= total_tests * 0.8:
            print("⚠️  大部分测试通过，但仍有部分问题需要修复")
        else:
            print("❌ 测试失败较多，需要重点修复问题")
        
        return passed_tests == total_tests
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.start_time = time.time()
        self.print_header("缺陷管理模块测试套件")
        
        print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 1. 安装依赖
        if not self.install_dependencies():
            print("❌ 依赖安装失败，无法继续测试")
            return False
        
        # 2. 检查服务器状态
        server_ok = self.check_server_status()
        
        # 3. 运行单元测试
        self.run_pytest("tests/test_defect_api.py", "单元测试")
        
        # 4. 运行集成测试（需要服务器运行）
        if server_ok:
            # 由于异步测试导入问题，我们使用subprocess运行
            self.print_section("运行 集成测试")
            try:
                cmd = [sys.executable, "tests/test_integration.py"]
                result = subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
                if result.returncode == 0:
                    print("✅ 集成测试 通过")
                    self.test_results["集成测试"] = {"passed": True}
                else:
                    print("❌ 集成测试 失败")
                    self.test_results["集成测试"] = {"passed": False, "error": "测试返回非零退出码"}
            except Exception as e:
                print(f"❌ 运行集成测试时发生异常: {str(e)}")
                self.test_results["集成测试"] = {"passed": False, "error": str(e)}
        else:
            print("⚠️  跳过集成测试（服务器未运行）")
            self.test_results["集成测试"] = {"passed": False, "error": "服务器未运行"}
        
        # 5. 运行性能测试（需要服务器运行）
        if server_ok:
            self.print_section("运行 性能测试")
            try:
                cmd = [sys.executable, "tests/test_performance.py"]
                result = subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
                if result.returncode == 0:
                    print("✅ 性能测试 通过")
                    self.test_results["性能测试"] = {"passed": True}
                else:
                    print("❌ 性能测试 失败")
                    self.test_results["性能测试"] = {"passed": False, "error": "测试返回非零退出码"}
            except Exception as e:
                print(f"❌ 运行性能测试时发生异常: {str(e)}")
                self.test_results["性能测试"] = {"passed": False, "error": str(e)}
        else:
            print("⚠️  跳过性能测试（服务器未运行）")
            self.test_results["性能测试"] = {"passed": False, "error": "服务器未运行"}
        
        # 6. 运行用户验收测试（需要服务器运行）
        if server_ok:
            self.print_section("运行 用户验收测试")
            try:
                cmd = [sys.executable, "tests/test_user_acceptance.py"]
                result = subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
                if result.returncode == 0:
                    print("✅ 用户验收测试 通过")
                    self.test_results["用户验收测试"] = {"passed": True}
                else:
                    print("❌ 用户验收测试 失败")
                    self.test_results["用户验收测试"] = {"passed": False, "error": "测试返回非零退出码"}
            except Exception as e:
                print(f"❌ 运行用户验收测试时发生异常: {str(e)}")
                self.test_results["用户验收测试"] = {"passed": False, "error": str(e)}
        else:
            print("⚠️  跳过用户验收测试（服务器未运行）")
            self.test_results["用户验收测试"] = {"passed": False, "error": "服务器未运行"}
        
        # 7. 生成报告
        return self.generate_test_report()

async def main():
    """主函数"""
    runner = TestRunner()
    success = await runner.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
