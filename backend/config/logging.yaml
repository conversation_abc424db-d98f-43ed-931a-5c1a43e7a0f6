# 质量大盘后端日志配置文件
# 支持开发和生产环境的不同日志级别和输出方式

version: 1
disable_existing_loggers: false

# 日志格式定义
formatters:
  # 详细格式 - 用于文件日志
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  # 简洁格式 - 用于控制台输出
  simple:
    format: '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    datefmt: '%H:%M:%S'
  
  # JSON格式 - 用于结构化日志
  json:
    format: '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'

# 日志处理器定义
handlers:
  # 控制台处理器
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: simple
    stream: ext://sys.stdout
  
  # 应用日志文件处理器
  app_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: logs/app.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  # 错误日志文件处理器
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    maxBytes: 10485760  # 10MB
    backupCount: 5
    encoding: utf8
  
  # 调试日志文件处理器
  debug_file:
    class: logging.handlers.RotatingFileHandler
    level: DEBUG
    formatter: detailed
    filename: logs/debug.log
    maxBytes: 10485760  # 10MB
    backupCount: 3
    encoding: utf8
  
  # 访问日志文件处理器（用于API访问记录）
  access_file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: logs/access.log
    maxBytes: 10485760  # 10MB
    backupCount: 10
    encoding: utf8

# 日志记录器定义
loggers:
  # FastAPI应用日志
  quality_dashboard:
    level: INFO
    handlers: [console, app_file, error_file]
    propagate: false
  
  # API访问日志
  quality_dashboard.access:
    level: INFO
    handlers: [access_file]
    propagate: false
  
  # 数据库日志
  quality_dashboard.database:
    level: INFO
    handlers: [console, app_file]
    propagate: false
  
  # 服务层日志
  quality_dashboard.services:
    level: INFO
    handlers: [console, app_file]
    propagate: false
  
  # FastAPI框架日志
  fastapi:
    level: INFO
    handlers: [console, app_file]
    propagate: false
  
  # Uvicorn服务器日志
  uvicorn:
    level: INFO
    handlers: [console, app_file]
    propagate: false
  
  # Uvicorn访问日志
  uvicorn.access:
    level: INFO
    handlers: [access_file]
    propagate: false
  
  # SQLAlchemy日志
  sqlalchemy:
    level: WARNING
    handlers: [console, app_file]
    propagate: false
  
  # SQLAlchemy引擎日志（SQL语句）
  sqlalchemy.engine:
    level: WARNING
    handlers: [debug_file]
    propagate: false

# 根日志记录器
root:
  level: INFO
  handlers: [console, app_file, error_file]

# 开发环境配置覆盖
# 可以通过环境变量 LOG_LEVEL 来控制日志级别
# 例如：export LOG_LEVEL=DEBUG
