# 日志文件目录

此目录用于存储质量大盘后端应用的日志文件。

## 日志文件说明

### 应用日志文件
- `app.log` - 应用主日志文件，记录INFO级别及以上的日志
- `error.log` - 错误日志文件，仅记录ERROR和CRITICAL级别的日志
- `debug.log` - 调试日志文件，记录DEBUG级别及以上的所有日志

### 访问日志文件
- `access.log` - API访问日志，记录所有HTTP请求的详细信息

## 日志轮转策略

### 按大小轮转
- 应用日志文件：当文件大小超过10MB时自动轮转，保留5个备份文件
- 错误日志文件：当文件大小超过10MB时自动轮转，保留5个备份文件
- 调试日志文件：当文件大小超过10MB时自动轮转，保留3个备份文件

### 按时间轮转
- 访问日志文件：每天午夜自动轮转，保留30天的备份文件

## 日志级别说明

- `DEBUG` - 调试信息，仅在开发环境使用
- `INFO` - 一般信息，记录应用的正常运行状态
- `WARNING` - 警告信息，记录可能的问题但不影响正常运行
- `ERROR` - 错误信息，记录应用运行中的错误
- `CRITICAL` - 严重错误，记录可能导致应用崩溃的严重问题

## 环境变量控制

可以通过以下环境变量控制日志行为：

- `LOG_LEVEL` - 设置日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- `ENVIRONMENT` - 设置运行环境（development, production）

## 注意事项

1. 生产环境建议设置日志级别为INFO或WARNING，避免产生过多日志
2. 定期清理旧的日志文件，避免占用过多磁盘空间
3. 敏感信息不应记录在日志中
4. 日志文件会自动创建，无需手动创建
