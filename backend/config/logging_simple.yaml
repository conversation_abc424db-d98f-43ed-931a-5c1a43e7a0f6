# 简化版日志配置文件
# 用于解决复杂配置的兼容性问题

version: 1
disable_existing_loggers: false

# 日志格式定义
formatters:
  # 详细格式 - 用于文件日志
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
    datefmt: '%Y-%m-%d %H:%M:%S'
  
  # 简洁格式 - 用于控制台输出
  simple:
    format: '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    datefmt: '%H:%M:%S'

# 日志处理器定义
handlers:
  # 控制台处理器
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: simple
    stream: ext://sys.stdout
  
  # 应用日志文件处理器
  app_file:
    class: logging.FileHandler
    level: INFO
    formatter: detailed
    filename: logs/app.log
    encoding: utf8
  
  # 错误日志文件处理器
  error_file:
    class: logging.FileHandler
    level: ERROR
    formatter: detailed
    filename: logs/error.log
    encoding: utf8

# 日志记录器定义
loggers:
  # FastAPI应用日志
  quality_dashboard:
    level: INFO
    handlers: [console, app_file, error_file]
    propagate: false
  
  # FastAPI框架日志
  fastapi:
    level: INFO
    handlers: [console, app_file]
    propagate: false
  
  # Uvicorn服务器日志
  uvicorn:
    level: INFO
    handlers: [console, app_file]
    propagate: false

# 根日志记录器
root:
  level: INFO
  handlers: [console, app_file, error_file]
