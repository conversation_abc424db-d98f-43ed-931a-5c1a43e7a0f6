"""
Uvicorn 服务器配置
"""

import os
from typing import Dict, Any
from pathlib import Path

from .settings import Settings

settings = Settings()


class UvicornConfig:
    """Uvicorn 配置类"""
    
    def __init__(self):
        self.settings = settings
        self.is_production = not settings.DEBUG
        
    def get_config(self) -> Dict[str, Any]:
        """获取 Uvicorn 配置"""
        base_config = {
            "app": "backend.main:app",
            "host": "0.0.0.0",
            "port": int(os.getenv("PORT", "8000")),
            "reload": not self.is_production,
            "log_config": self._get_log_config(),
            "access_log": True,
            "server_header": False,  # 隐藏服务器信息
            "date_header": True,
        }

        # 只添加uvicorn支持的配置项
        if self.is_production:
            # 生产环境：单进程但优化配置
            base_config.update({
                "workers": 1,  # uvicorn单进程
                "backlog": 2048,
                "limit_concurrency": 1000,
                "limit_max_requests": 1000,
            })
        else:
            # 开发环境：热重载配置
            base_config.update({
                "reload": True,
                "reload_dirs": ["backend"],
                "reload_includes": ["*.py"],
                "reload_excludes": ["*.pyc", "__pycache__", ".git"],
            })

        return base_config
    

    
    def _get_worker_count(self) -> int:
        """计算工作进程数（主要用于显示信息）"""
        # 对于uvicorn，我们使用单进程模式
        # 多进程需要使用gunicorn + uvicorn workers
        return 1
    
    def _get_log_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        log_level = "INFO" if self.is_production else "DEBUG"
        
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S",
                },
                "access": {
                    "format": "%(asctime)s - %(levelname)s - %(client_addr)s - \"%(request_line)s\" %(status_code)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S",
                },
                "json": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S",
                },
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
                "access": {
                    "formatter": "access",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stdout",
                },
                "file": {
                    "formatter": "default",
                    "class": "logging.handlers.RotatingFileHandler",
                    "filename": "logs/quality_dashboard.log",
                    "maxBytes": 10485760,  # 10MB
                    "backupCount": 5,
                },
            },
            "loggers": {
                "uvicorn": {
                    "handlers": ["default", "file"] if self.is_production else ["default"],
                    "level": log_level,
                    "propagate": False,
                },
                "uvicorn.access": {
                    "handlers": ["access", "file"] if self.is_production else ["access"],
                    "level": log_level,
                    "propagate": False,
                },
                "quality_dashboard": {
                    "handlers": ["default", "file"] if self.is_production else ["default"],
                    "level": log_level,
                    "propagate": False,
                },
                "sqlalchemy.engine": {
                    "handlers": ["default"],
                    "level": "WARNING" if self.is_production else "INFO",
                    "propagate": False,
                },
            },
            "root": {
                "level": log_level,
                "handlers": ["default"],
            },
        }


def get_uvicorn_config() -> Dict[str, Any]:
    """获取 Uvicorn 配置"""
    config = UvicornConfig()
    return config.get_config()


def create_log_directory():
    """创建日志目录"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)


# 启动时创建日志目录
create_log_directory()
