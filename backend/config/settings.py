"""
应用配置设置
"""
import os
from typing import Optional


class Settings:
    """应用配置类"""

    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "*********************************************************/quality_dashboard")

    # Redis配置
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://:redis_f4tEn5@118.195.188.5:38762/3")
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "redis_f4tEn5")
    REDIS_DB: int = int(os.getenv("REDIS_DB", "3"))

    # 缓存配置
    CACHE_TTL: dict = {
        "dashboard_overview": 300,      # 5分钟
        "dashboard_trends": 1800,       # 30分钟
        "project_stats": 600,           # 10分钟
        "team_comparison": 900,         # 15分钟
        "defect_trends": 1200,          # 20分钟
        "coverage_stats": 1800,         # 30分钟
        "performance_metrics": 300,     # 5分钟
        "quality_gate_stats": 600       # 10分钟
    }

    # 邮件配置
    SMTP_SERVER: str = os.getenv("SMTP_SERVER", "smtp.gmail.com")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME: str = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD: str = os.getenv("SMTP_PASSWORD", "")
    FROM_EMAIL: str = os.getenv("FROM_EMAIL", "")

    # 应用配置
    APP_NAME: str = "质量大盘"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # 前端URL配置
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")

    # API配置
    API_PREFIX: str = "/api"

    # 性能配置
    MAX_PAGE_SIZE: int = 100
    DEFAULT_PAGE_SIZE: int = 20
    API_TIMEOUT: int = 30

    # 第三方集成配置
    JENKINS_URL: str = os.getenv("JENKINS_URL", "")
    JENKINS_USERNAME: str = os.getenv("JENKINS_USERNAME", "")
    JENKINS_TOKEN: str = os.getenv("JENKINS_TOKEN", "")

    JIRA_URL: str = os.getenv("JIRA_URL", "")
    JIRA_USERNAME: str = os.getenv("JIRA_USERNAME", "")
    JIRA_TOKEN: str = os.getenv("JIRA_TOKEN", "")

    SONARQUBE_URL: str = os.getenv("SONARQUBE_URL", "")
    SONARQUBE_TOKEN: str = os.getenv("SONARQUBE_TOKEN", "")
    
    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # Redis配置（如果使用）
    REDIS_URL: Optional[str] = os.getenv("REDIS_URL")
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_DIR: str = os.getenv("LOG_DIR", "./logs")
    
    # 文件上传配置
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")
    
    # 报告配置
    REPORT_TEMP_DIR: str = os.getenv("REPORT_TEMP_DIR", "./temp/reports")
    REPORT_MAX_SIZE: int = int(os.getenv("REPORT_MAX_SIZE", "104857600"))  # 100MB
    
    # 预警配置
    ALERT_CHECK_INTERVAL: int = int(os.getenv("ALERT_CHECK_INTERVAL", "300"))  # 5分钟
    MAX_ALERTS_PER_RULE: int = int(os.getenv("MAX_ALERTS_PER_RULE", "10"))
    
    # 第三方集成配置
    JENKINS_URL: Optional[str] = os.getenv("JENKINS_URL")
    JENKINS_USERNAME: Optional[str] = os.getenv("JENKINS_USERNAME")
    JENKINS_TOKEN: Optional[str] = os.getenv("JENKINS_TOKEN")
    
    JIRA_URL: Optional[str] = os.getenv("JIRA_URL")
    JIRA_USERNAME: Optional[str] = os.getenv("JIRA_USERNAME")
    JIRA_TOKEN: Optional[str] = os.getenv("JIRA_TOKEN")
    
    SONARQUBE_URL: Optional[str] = os.getenv("SONARQUBE_URL")
    SONARQUBE_TOKEN: Optional[str] = os.getenv("SONARQUBE_TOKEN")


# 创建全局设置实例
settings = Settings()
