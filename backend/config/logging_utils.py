"""
日志配置工具模块
提供日志初始化和配置功能
"""

import os
import sys
import logging
import logging.config
import yaml
from pathlib import Path
from typing import Optional


def setup_logging(
    config_path: Optional[str] = None,
    log_level: Optional[str] = None,
    log_dir: Optional[str] = None
) -> None:
    """
    初始化日志配置
    
    Args:
        config_path: 日志配置文件路径，默认为 config/logging.yaml
        log_level: 日志级别，可通过环境变量 LOG_LEVEL 设置
        log_dir: 日志文件目录，默认为 logs
    """
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    # 设置默认配置文件路径
    if config_path is None:
        # 优先尝试简化版配置
        simple_config = project_root / "config" / "logging_simple.yaml"
        full_config = project_root / "config" / "logging.yaml"

        if simple_config.exists():
            config_path = simple_config
        elif full_config.exists():
            config_path = full_config
        else:
            config_path = full_config  # 使用默认路径，即使不存在
    else:
        config_path = Path(config_path)
    
    # 设置日志目录
    if log_dir is None:
        log_dir = project_root / "logs"
    else:
        log_dir = Path(log_dir)
    
    # 创建日志目录
    log_dir.mkdir(exist_ok=True)
    
    # 从环境变量获取日志级别
    if log_level is None:
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    try:
        # 读取日志配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 更新日志文件路径为绝对路径
        for handler_name, handler_config in config.get('handlers', {}).items():
            if 'filename' in handler_config:
                filename = handler_config['filename']
                if not os.path.isabs(filename):
                    handler_config['filename'] = str(log_dir / filename)
        
        # 根据环境变量调整日志级别
        if log_level in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            # 更新根日志记录器级别
            config['root']['level'] = log_level
            
            # 更新主要日志记录器级别
            for logger_name in ['quality_dashboard', 'fastapi', 'uvicorn']:
                if logger_name in config.get('loggers', {}):
                    config['loggers'][logger_name]['level'] = log_level
            
            # 在开发环境下启用调试日志
            if log_level == 'DEBUG':
                # 为控制台处理器也设置DEBUG级别
                if 'console' in config.get('handlers', {}):
                    config['handlers']['console']['level'] = 'DEBUG'
                
                # 启用SQLAlchemy SQL语句日志
                if 'sqlalchemy.engine' in config.get('loggers', {}):
                    config['loggers']['sqlalchemy.engine']['level'] = 'INFO'
        
        # 应用日志配置
        logging.config.dictConfig(config)
        
        # 获取应用日志记录器
        logger = logging.getLogger('quality_dashboard')
        logger.info(f"日志系统初始化完成 - 级别: {log_level}, 日志目录: {log_dir}")
        
    except FileNotFoundError:
        # 如果配置文件不存在，使用基本配置
        logging.basicConfig(
            level=getattr(logging, log_level, logging.INFO),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(log_dir / 'app.log', encoding='utf-8')
            ]
        )
        logger = logging.getLogger('quality_dashboard')
        logger.warning(f"日志配置文件未找到: {config_path}，使用基本配置")
        
    except Exception as e:
        # 配置失败时使用基本配置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[logging.StreamHandler(sys.stdout)]
        )
        logger = logging.getLogger('quality_dashboard')
        logger.error(f"日志配置失败: {e}，使用基本配置")
        import traceback
        logger.debug(f"详细错误信息: {traceback.format_exc()}")


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(f'quality_dashboard.{name}')


def log_api_access(
    method: str,
    path: str,
    status_code: int,
    response_time: float,
    client_ip: str = None,
    user_agent: str = None
) -> None:
    """
    记录API访问日志
    
    Args:
        method: HTTP方法
        path: 请求路径
        status_code: 响应状态码
        response_time: 响应时间（毫秒）
        client_ip: 客户端IP
        user_agent: 用户代理
    """
    access_logger = logging.getLogger('quality_dashboard.access')
    
    log_data = {
        'method': method,
        'path': path,
        'status_code': status_code,
        'response_time_ms': round(response_time, 2),
        'client_ip': client_ip,
        'user_agent': user_agent
    }
    
    access_logger.info(
        f"{method} {path} - {status_code} - {response_time:.2f}ms",
        extra=log_data
    )


def setup_development_logging():
    """设置开发环境日志配置"""
    setup_logging(log_level='DEBUG')


def setup_production_logging():
    """设置生产环境日志配置"""
    setup_logging(log_level='INFO')


# 环境检测和自动配置
def auto_setup_logging():
    """根据环境自动设置日志配置"""
    env = os.getenv('ENVIRONMENT', 'development').lower()
    
    if env in ['production', 'prod']:
        setup_production_logging()
    else:
        setup_development_logging()


if __name__ == "__main__":
    # 测试日志配置
    setup_logging()
    
    logger = get_logger('test')
    logger.debug("这是调试信息")
    logger.info("这是信息日志")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    
    # 测试API访问日志
    log_api_access('GET', '/api/test', 200, 45.67, '127.0.0.1', 'TestAgent/1.0')
