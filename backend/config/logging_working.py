"""
工作版本的日志配置
直接使用Python代码配置日志，避免YAML配置问题
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path


def setup_working_logging(log_level: str = "INFO"):
    """
    设置工作版本的日志配置
    
    Args:
        log_level: 日志级别
    """
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    log_dir = project_root / "logs"
    
    # 创建日志目录
    log_dir.mkdir(exist_ok=True)
    
    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    # 创建格式化器
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(name)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(simple_formatter)
    
    # 创建应用日志文件处理器
    app_log_file = log_dir / "app.log"
    try:
        app_file_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        app_file_handler.setLevel(level)
        app_file_handler.setFormatter(detailed_formatter)
    except Exception as e:
        print(f"无法创建应用日志文件处理器: {e}")
        app_file_handler = None
    
    # 创建错误日志文件处理器
    error_log_file = log_dir / "error.log"
    try:
        error_file_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_file_handler.setLevel(logging.ERROR)
        error_file_handler.setFormatter(detailed_formatter)
    except Exception as e:
        print(f"无法创建错误日志文件处理器: {e}")
        error_file_handler = None
    
    # 创建访问日志文件处理器
    access_log_file = log_dir / "access.log"
    try:
        access_file_handler = logging.handlers.RotatingFileHandler(
            access_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        access_file_handler.setLevel(logging.INFO)
        access_file_handler.setFormatter(detailed_formatter)
    except Exception as e:
        print(f"无法创建访问日志文件处理器: {e}")
        access_file_handler = None
    
    # 配置根日志记录器
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    if app_file_handler:
        root_logger.addHandler(app_file_handler)
    if error_file_handler:
        root_logger.addHandler(error_file_handler)
    
    # 配置应用日志记录器
    app_logger = logging.getLogger('quality_dashboard')
    app_logger.setLevel(level)
    app_logger.propagate = False
    app_logger.addHandler(console_handler)
    if app_file_handler:
        app_logger.addHandler(app_file_handler)
    if error_file_handler:
        app_logger.addHandler(error_file_handler)
    
    # 配置访问日志记录器
    access_logger = logging.getLogger('quality_dashboard.access')
    access_logger.setLevel(logging.INFO)
    access_logger.propagate = False
    if access_file_handler:
        access_logger.addHandler(access_file_handler)
    else:
        access_logger.addHandler(console_handler)
    
    # 配置FastAPI相关日志记录器
    for logger_name in ['fastapi', 'uvicorn', 'uvicorn.access']:
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
        logger.propagate = False
        logger.addHandler(console_handler)
        if app_file_handler:
            logger.addHandler(app_file_handler)
    
    # 配置SQLAlchemy日志记录器
    sqlalchemy_logger = logging.getLogger('sqlalchemy')
    sqlalchemy_logger.setLevel(logging.WARNING)
    sqlalchemy_logger.propagate = False
    sqlalchemy_logger.addHandler(console_handler)
    if app_file_handler:
        sqlalchemy_logger.addHandler(app_file_handler)
    
    # 记录初始化成功
    logger = logging.getLogger('quality_dashboard')
    logger.info(f"日志系统初始化完成 - 级别: {log_level}, 日志目录: {log_dir}")
    
    return True


def get_working_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(f'quality_dashboard.{name}')


def log_working_api_access(
    method: str,
    path: str,
    status_code: int,
    response_time: float,
    client_ip: str = None,
    user_agent: str = None
) -> None:
    """
    记录API访问日志
    
    Args:
        method: HTTP方法
        path: 请求路径
        status_code: 响应状态码
        response_time: 响应时间（毫秒）
        client_ip: 客户端IP
        user_agent: 用户代理
    """
    access_logger = logging.getLogger('quality_dashboard.access')
    
    access_logger.info(
        f"{method} {path} - {status_code} - {response_time:.2f}ms - "
        f"IP: {client_ip or 'unknown'} - UA: {user_agent or 'unknown'}"
    )


if __name__ == "__main__":
    # 测试工作版本的日志配置
    print("测试工作版本的日志配置...")
    
    setup_working_logging("INFO")
    
    logger = get_working_logger('test')
    logger.info("这是信息日志")
    logger.warning("这是警告日志")
    logger.error("这是错误日志")
    
    # 测试API访问日志
    log_working_api_access('GET', '/api/test', 200, 45.67, '127.0.0.1', 'TestAgent/1.0')
    
    print("测试完成，请检查日志文件")
