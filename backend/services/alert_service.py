"""
质量预警服务
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload

from models.alert import Alert, AlertRule, AlertLevel, AlertType, AlertStatus, QualityMetric
from models.defect import Defect
from models.coverage import CoverageMetric
from models.dashboard import Project
from services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class QualityAlertService:
    """质量预警服务"""

    def __init__(self, db: AsyncSession):
        self.db = db
        self.notification_service = NotificationService()
        
        # 默认预警规则配置
        self.default_rules = {
            'defect_spike': {
                'threshold': 0.5,  # 50%增长
                'window_minutes': 10080,  # 7天
                'level': AlertLevel.WARNING
            },
            'coverage_drop': {
                'threshold': 0.1,  # 10%下降
                'window_minutes': 4320,  # 3天
                'level': AlertLevel.CRITICAL
            },
            'quality_decline': {
                'threshold': 0.2,  # 20%下降
                'window_minutes': 20160,  # 14天
                'level': AlertLevel.WARNING
            }
        }

    async def check_all_alerts(self) -> List[Dict[str, Any]]:
        """检查所有预警规则"""
        logger.info("开始检查所有预警规则")
        alerts = []

        try:
            # 获取所有启用的预警规则
            rules_query = select(AlertRule).where(
                AlertRule.is_enabled == True
            ).options(selectinload(AlertRule.project))
            
            result = await self.db.execute(rules_query)
            rules = result.scalars().all()

            for rule in rules:
                try:
                    rule_alerts = await self._check_rule(rule)
                    alerts.extend(rule_alerts)
                except Exception as e:
                    logger.error(f"检查规则 {rule.name} 时出错: {e}")

            logger.info(f"预警检查完成，生成 {len(alerts)} 个预警")
            return alerts

        except Exception as e:
            logger.error(f"预警检查过程中出错: {e}")
            return []

    async def _check_rule(self, rule: AlertRule) -> List[Dict[str, Any]]:
        """检查单个预警规则"""
        if rule.alert_type == AlertType.DEFECT_SPIKE.value:
            return await self._check_defect_spike(rule)
        elif rule.alert_type == AlertType.COVERAGE_DROP.value:
            return await self._check_coverage_drop(rule)
        elif rule.alert_type == AlertType.QUALITY_DECLINE.value:
            return await self._check_quality_decline(rule)
        else:
            logger.warning(f"未知的预警类型: {rule.alert_type}")
            return []

    async def _check_defect_spike(self, rule: AlertRule) -> List[Dict[str, Any]]:
        """检查缺陷激增"""
        window_minutes = rule.window_minutes
        threshold = rule.threshold_value
        project_id = rule.project_id

        # 计算时间窗口
        end_time = datetime.now()
        current_start = end_time - timedelta(minutes=window_minutes)
        previous_start = current_start - timedelta(minutes=window_minutes)

        # 当前周期缺陷数
        current_query = select(func.count(Defect.id)).where(
            and_(
                Defect.project_id == project_id,
                Defect.found_date >= current_start,
                Defect.found_date <= end_time
            )
        )
        current_result = await self.db.execute(current_query)
        current_count = current_result.scalar() or 0

        # 上个周期缺陷数
        previous_query = select(func.count(Defect.id)).where(
            and_(
                Defect.project_id == project_id,
                Defect.found_date >= previous_start,
                Defect.found_date < current_start
            )
        )
        previous_result = await self.db.execute(previous_query)
        previous_count = previous_result.scalar() or 0

        # 计算增长率
        if previous_count > 0:
            growth_rate = (current_count - previous_count) / previous_count

            if self._evaluate_threshold(growth_rate, rule.threshold_operator, threshold):
                alert_data = {
                    'rule_id': rule.id,
                    'title': f'项目 {rule.project.name} 缺陷数量激增',
                    'description': f'最近{window_minutes//1440}天缺陷数量增长{growth_rate:.1%}，超过阈值{threshold:.1%}',
                    'alert_type': rule.alert_type,
                    'level': rule.level,
                    'project_id': project_id,
                    'current_value': current_count,
                    'previous_value': previous_count,
                    'threshold_value': threshold,
                    'change_rate': growth_rate,
                    'suggested_actions': [
                        '检查最近代码变更',
                        '增加测试覆盖率',
                        '进行代码审查',
                        '分析缺陷根因'
                    ]
                }
                
                # 创建预警记录
                alert = await self._create_alert(alert_data)
                return [alert]

        return []

    async def _check_coverage_drop(self, rule: AlertRule) -> List[Dict[str, Any]]:
        """检查覆盖率下降"""
        window_minutes = rule.window_minutes
        threshold = rule.threshold_value
        project_id = rule.project_id

        # 获取最近的覆盖率数据
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=window_minutes * 2)

        coverage_query = select(
            CoverageMetric.line_coverage,
            CoverageMetric.measurement_date
        ).where(
            and_(
                CoverageMetric.project_id == project_id,
                CoverageMetric.measurement_date >= start_time
            )
        ).order_by(desc(CoverageMetric.measurement_date)).limit(2)

        result = await self.db.execute(coverage_query)
        coverage_data = result.fetchall()

        if len(coverage_data) >= 2:
            latest_coverage = coverage_data[0].line_coverage
            previous_coverage = coverage_data[1].line_coverage

            if previous_coverage > 0:
                drop_rate = (previous_coverage - latest_coverage) / previous_coverage

                if self._evaluate_threshold(drop_rate, rule.threshold_operator, threshold):
                    alert_data = {
                        'rule_id': rule.id,
                        'title': f'项目 {rule.project.name} 测试覆盖率下降',
                        'description': f'测试覆盖率从{previous_coverage:.1%}下降到{latest_coverage:.1%}，下降{drop_rate:.1%}',
                        'alert_type': rule.alert_type,
                        'level': rule.level,
                        'project_id': project_id,
                        'current_value': latest_coverage,
                        'previous_value': previous_coverage,
                        'threshold_value': threshold,
                        'change_rate': drop_rate,
                        'suggested_actions': [
                            '增加单元测试',
                            '检查测试用例有效性',
                            '进行测试用例补充',
                            '分析覆盖率下降原因'
                        ]
                    }
                    
                    alert = await self._create_alert(alert_data)
                    return [alert]

        return []

    def _evaluate_threshold(self, value: float, operator: str, threshold: float) -> bool:
        """评估阈值条件"""
        if operator == '>':
            return value > threshold
        elif operator == '>=':
            return value >= threshold
        elif operator == '<':
            return value < threshold
        elif operator == '<=':
            return value <= threshold
        elif operator == '==':
            return abs(value - threshold) < 0.001
        else:
            logger.warning(f"未知的阈值操作符: {operator}")
            return False

    async def _create_alert(self, alert_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建预警记录"""
        try:
            # 检查是否已存在相同的活跃预警
            existing_query = select(Alert).where(
                and_(
                    Alert.rule_id == alert_data['rule_id'],
                    Alert.project_id == alert_data['project_id'],
                    Alert.status == AlertStatus.ACTIVE.value,
                    Alert.created_at >= datetime.now() - timedelta(hours=24)
                )
            )
            
            result = await self.db.execute(existing_query)
            existing_alert = result.scalar_one_or_none()
            
            if existing_alert:
                logger.info(f"预警已存在，跳过创建: {alert_data['title']}")
                return self._alert_to_dict(existing_alert)

            # 创建新预警
            alert = Alert(
                title=alert_data['title'],
                description=alert_data['description'],
                alert_type=alert_data['alert_type'],
                level=alert_data['level'],
                rule_id=alert_data['rule_id'],
                project_id=alert_data['project_id'],
                current_value=alert_data['current_value'],
                previous_value=alert_data['previous_value'],
                threshold_value=alert_data['threshold_value'],
                change_rate=alert_data['change_rate'],
                suggested_actions=alert_data['suggested_actions']
            )

            self.db.add(alert)
            await self.db.commit()
            await self.db.refresh(alert)

            logger.info(f"创建预警成功: {alert.title}")

            # 发送通知
            await self._send_alert_notification(alert)

            return self._alert_to_dict(alert)

        except Exception as e:
            logger.error(f"创建预警失败: {e}")
            await self.db.rollback()
            raise

    def _alert_to_dict(self, alert: Alert) -> Dict[str, Any]:
        """将预警对象转换为字典"""
        return {
            'id': alert.id,
            'title': alert.title,
            'description': alert.description,
            'alert_type': alert.alert_type,
            'level': alert.level,
            'status': alert.status,
            'project_id': alert.project_id,
            'current_value': alert.current_value,
            'previous_value': alert.previous_value,
            'threshold_value': alert.threshold_value,
            'change_rate': alert.change_rate,
            'suggested_actions': alert.suggested_actions,
            'created_at': alert.created_at.isoformat() if alert.created_at else None
        }

    async def _send_alert_notification(self, alert: Alert):
        """发送预警通知"""
        try:
            # 获取规则的通知配置
            rule_query = select(AlertRule).where(AlertRule.id == alert.rule_id)
            result = await self.db.execute(rule_query)
            rule = result.scalar_one_or_none()
            
            if rule and rule.notification_config:
                await self.notification_service.send_alert_notification(alert, rule.notification_config)
                
        except Exception as e:
            logger.error(f"发送预警通知失败: {e}")

    async def _check_quality_decline(self, rule: AlertRule) -> List[Dict[str, Any]]:
        """检查质量下降"""
        window_minutes = rule.window_minutes
        threshold = rule.threshold_value
        project_id = rule.project_id

        # 计算质量综合得分
        end_time = datetime.now()
        current_start = end_time - timedelta(minutes=window_minutes)
        previous_start = current_start - timedelta(minutes=window_minutes)

        # 获取当前和之前的质量指标
        current_score = await self._calculate_quality_score(project_id, current_start, end_time)
        previous_score = await self._calculate_quality_score(project_id, previous_start, current_start)

        if previous_score > 0:
            decline_rate = (previous_score - current_score) / previous_score

            if self._evaluate_threshold(decline_rate, rule.threshold_operator, threshold):
                alert_data = {
                    'rule_id': rule.id,
                    'title': f'项目 {rule.project.name} 质量指标下降',
                    'description': f'质量综合得分从{previous_score:.1f}下降到{current_score:.1f}，下降{decline_rate:.1%}',
                    'alert_type': rule.alert_type,
                    'level': rule.level,
                    'project_id': project_id,
                    'current_value': current_score,
                    'previous_value': previous_score,
                    'threshold_value': threshold,
                    'change_rate': decline_rate,
                    'suggested_actions': [
                        '分析质量下降原因',
                        '加强代码审查',
                        '提升测试覆盖率',
                        '优化开发流程'
                    ]
                }

                alert = await self._create_alert(alert_data)
                return [alert]

        return []

    async def _calculate_quality_score(self, project_id: int, start_time: datetime, end_time: datetime) -> float:
        """计算质量综合得分"""
        # 获取覆盖率得分 (40%权重)
        coverage_query = select(func.avg(CoverageMetric.line_coverage)).where(
            and_(
                CoverageMetric.project_id == project_id,
                CoverageMetric.measurement_date >= start_time,
                CoverageMetric.measurement_date <= end_time
            )
        )
        coverage_result = await self.db.execute(coverage_query)
        coverage_score = (coverage_result.scalar() or 0) * 0.4

        # 获取缺陷得分 (40%权重) - 缺陷越少得分越高
        defect_query = select(func.count(Defect.id)).where(
            and_(
                Defect.project_id == project_id,
                Defect.found_date >= start_time,
                Defect.found_date <= end_time
            )
        )
        defect_result = await self.db.execute(defect_query)
        defect_count = defect_result.scalar() or 0
        defect_score = max(0, (100 - defect_count * 2)) * 0.4  # 每个缺陷扣2分

        # 测试通过率得分 (20%权重) - 这里简化处理
        test_score = 80 * 0.2  # 假设测试通过率80%

        return coverage_score + defect_score + test_score
