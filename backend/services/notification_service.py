"""
通知服务
"""
import asyncio
import aiohttp
import smtplib
import logging
from typing import Dict, Any, List
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from models.alert import Alert, AlertNotification
from config import settings

logger = logging.getLogger(__name__)


class NotificationService:
    """通知服务"""

    def __init__(self):
        self.email_config = {
            'smtp_server': getattr(settings, 'SMTP_SERVER', 'smtp.gmail.com'),
            'smtp_port': getattr(settings, 'SMTP_PORT', 587),
            'smtp_username': getattr(settings, 'SMTP_USERNAME', ''),
            'smtp_password': getattr(settings, 'SMTP_PASSWORD', ''),
            'from_email': getattr(settings, 'FROM_EMAIL', '')
        }

    async def send_alert_notification(self, alert: Alert, notification_config: Dict[str, Any]):
        """发送预警通知"""
        try:
            notification_types = notification_config.get('types', ['email'])
            
            for notification_type in notification_types:
                if notification_type == 'email':
                    await self._send_email_notification(alert, notification_config)
                elif notification_type == 'webhook':
                    await self._send_webhook_notification(alert, notification_config)
                elif notification_type == 'dingtalk':
                    await self._send_dingtalk_notification(alert, notification_config)
                else:
                    logger.warning(f"不支持的通知类型: {notification_type}")

        except Exception as e:
            logger.error(f"发送预警通知失败: {e}")

    async def _send_email_notification(self, alert: Alert, config: Dict[str, Any]):
        """发送邮件通知"""
        try:
            email_config = config.get('email', {})
            recipients = email_config.get('recipients', [])
            
            if not recipients:
                logger.warning("邮件通知配置中没有收件人")
                return

            subject = f"[质量预警] {alert.title}"
            content = self._generate_email_content(alert)

            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject

            # 添加邮件内容
            msg.attach(MIMEText(content, 'html', 'utf-8'))

            # 发送邮件
            with smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port']) as server:
                server.starttls()
                server.login(self.email_config['smtp_username'], self.email_config['smtp_password'])
                server.send_message(msg)

            logger.info(f"邮件通知发送成功: {alert.title}")

        except Exception as e:
            logger.error(f"发送邮件通知失败: {e}")

    async def _send_webhook_notification(self, alert: Alert, config: Dict[str, Any]):
        """发送Webhook通知"""
        try:
            webhook_config = config.get('webhook', {})
            url = webhook_config.get('url')
            
            if not url:
                logger.warning("Webhook通知配置中没有URL")
                return

            payload = {
                'alert_id': alert.id,
                'title': alert.title,
                'description': alert.description,
                'level': alert.level,
                'alert_type': alert.alert_type,
                'project_id': alert.project_id,
                'current_value': alert.current_value,
                'previous_value': alert.previous_value,
                'change_rate': alert.change_rate,
                'suggested_actions': alert.suggested_actions,
                'created_at': alert.created_at.isoformat() if alert.created_at else None
            }

            headers = webhook_config.get('headers', {})
            headers.setdefault('Content-Type', 'application/json')

            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, headers=headers) as response:
                    if response.status == 200:
                        logger.info(f"Webhook通知发送成功: {alert.title}")
                    else:
                        logger.error(f"Webhook通知发送失败: {response.status}")

        except Exception as e:
            logger.error(f"发送Webhook通知失败: {e}")

    async def _send_dingtalk_notification(self, alert: Alert, config: Dict[str, Any]):
        """发送钉钉通知"""
        try:
            dingtalk_config = config.get('dingtalk', {})
            webhook_url = dingtalk_config.get('webhook_url')
            
            if not webhook_url:
                logger.warning("钉钉通知配置中没有Webhook URL")
                return

            # 构建钉钉消息
            level_colors = {
                'info': '#1890ff',
                'warning': '#faad14',
                'critical': '#ff4d4f'
            }

            color = level_colors.get(alert.level, '#1890ff')
            
            message = {
                "msgtype": "actionCard",
                "actionCard": {
                    "title": f"质量预警: {alert.title}",
                    "text": self._generate_dingtalk_content(alert),
                    "hideAvatar": "0",
                    "btnOrientation": "0",
                    "btns": [
                        {
                            "title": "查看详情",
                            "actionURL": f"{settings.FRONTEND_URL}/alerts/{alert.id}"
                        }
                    ]
                }
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=message) as response:
                    if response.status == 200:
                        logger.info(f"钉钉通知发送成功: {alert.title}")
                    else:
                        logger.error(f"钉钉通知发送失败: {response.status}")

        except Exception as e:
            logger.error(f"发送钉钉通知失败: {e}")

    def _generate_email_content(self, alert: Alert) -> str:
        """生成邮件内容"""
        level_colors = {
            'info': '#1890ff',
            'warning': '#faad14',
            'critical': '#ff4d4f'
        }
        
        color = level_colors.get(alert.level, '#1890ff')
        
        content = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: {color}; color: white; padding: 15px; border-radius: 5px 5px 0 0;">
                    <h2 style="margin: 0;">质量预警通知</h2>
                </div>
                
                <div style="background: #f9f9f9; padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 5px 5px;">
                    <h3 style="color: {color}; margin-top: 0;">{alert.title}</h3>
                    
                    <p><strong>预警级别:</strong> <span style="color: {color}; text-transform: uppercase;">{alert.level}</span></p>
                    <p><strong>预警类型:</strong> {alert.alert_type}</p>
                    <p><strong>描述:</strong> {alert.description}</p>
                    
                    <div style="background: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <h4 style="margin-top: 0;">数据详情</h4>
                        <p><strong>当前值:</strong> {alert.current_value}</p>
                        <p><strong>之前值:</strong> {alert.previous_value}</p>
                        <p><strong>变化率:</strong> {alert.change_rate:.2%}</p>
                        <p><strong>阈值:</strong> {alert.threshold_value}</p>
                    </div>
                    
                    <div style="background: white; padding: 15px; border-radius: 5px; margin: 15px 0;">
                        <h4 style="margin-top: 0;">建议操作</h4>
                        <ul>
        """
        
        for action in alert.suggested_actions or []:
            content += f"<li>{action}</li>"
            
        content += f"""
                        </ul>
                    </div>
                    
                    <p style="margin-bottom: 0;"><strong>触发时间:</strong> {alert.created_at.strftime('%Y-%m-%d %H:%M:%S') if alert.created_at else 'N/A'}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return content

    def _generate_dingtalk_content(self, alert: Alert) -> str:
        """生成钉钉消息内容"""
        content = f"""
### {alert.title}

**预警级别:** {alert.level.upper()}  
**预警类型:** {alert.alert_type}  
**描述:** {alert.description}

#### 数据详情
- **当前值:** {alert.current_value}
- **之前值:** {alert.previous_value}
- **变化率:** {alert.change_rate:.2%}
- **阈值:** {alert.threshold_value}

#### 建议操作
"""
        
        for action in alert.suggested_actions or []:
            content += f"- {action}\n"
            
        content += f"\n**触发时间:** {alert.created_at.strftime('%Y-%m-%d %H:%M:%S') if alert.created_at else 'N/A'}"
        
        return content

    async def send_test_notification(self, notification_type: str, config: Dict[str, Any]) -> bool:
        """发送测试通知"""
        try:
            # 创建测试预警对象
            test_alert = Alert(
                title="测试预警通知",
                description="这是一个测试预警通知，用于验证通知配置是否正确。",
                alert_type="test",
                level="info",
                current_value=100,
                previous_value=80,
                threshold_value=90,
                change_rate=0.25,
                suggested_actions=["这是一个测试建议"],
                created_at=datetime.now()
            )

            if notification_type == 'email':
                await self._send_email_notification(test_alert, {'email': config})
            elif notification_type == 'webhook':
                await self._send_webhook_notification(test_alert, {'webhook': config})
            elif notification_type == 'dingtalk':
                await self._send_dingtalk_notification(test_alert, {'dingtalk': config})
            else:
                return False

            return True

        except Exception as e:
            logger.error(f"发送测试通知失败: {e}")
            return False
