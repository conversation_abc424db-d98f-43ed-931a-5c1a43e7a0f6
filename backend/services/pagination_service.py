"""
分页优化服务
"""

from typing import Any, Dict, List, Optional, Tuple, Type
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, asc
from sqlalchemy.orm import selectinload
from pydantic import BaseModel
from config.settings import Settings

settings = Settings()


class PaginationParams(BaseModel):
    """分页参数模型"""
    page: int = 1
    page_size: int = settings.DEFAULT_PAGE_SIZE
    sort_by: str = "id"
    sort_order: str = "desc"  # desc 或 asc
    search: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None


class PaginationResult(BaseModel):
    """分页结果模型"""
    items: List[Dict[str, Any]]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool
    next_cursor: Optional[str] = None
    prev_cursor: Optional[str] = None


class PaginationService:
    """分页优化服务类"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.max_page_size = settings.MAX_PAGE_SIZE
    
    def _validate_params(self, params: PaginationParams) -> PaginationParams:
        """验证和调整分页参数"""
        # 限制页面大小
        if params.page_size > self.max_page_size:
            params.page_size = self.max_page_size
        
        # 确保页码至少为1
        if params.page < 1:
            params.page = 1
        
        return params
    
    async def paginate_query(
        self,
        model: Type,
        params: PaginationParams,
        base_query: Optional[Any] = None,
        search_fields: Optional[List[str]] = None,
        relationships: Optional[List[str]] = None
    ) -> PaginationResult:
        """
        通用分页查询方法
        
        Args:
            model: SQLAlchemy模型类
            params: 分页参数
            base_query: 基础查询（可选）
            search_fields: 搜索字段列表
            relationships: 需要预加载的关联关系
        """
        params = self._validate_params(params)
        
        # 构建基础查询
        if base_query is None:
            query = select(model)
        else:
            query = base_query
        
        # 添加搜索条件
        if params.search and search_fields:
            search_conditions = []
            for field in search_fields:
                if hasattr(model, field):
                    field_attr = getattr(model, field)
                    search_conditions.append(field_attr.ilike(f"%{params.search}%"))
            
            if search_conditions:
                from sqlalchemy import or_
                query = query.where(or_(*search_conditions))
        
        # 添加过滤条件
        if params.filters:
            for field, value in params.filters.items():
                if hasattr(model, field) and value is not None:
                    field_attr = getattr(model, field)
                    if isinstance(value, list):
                        query = query.where(field_attr.in_(value))
                    else:
                        query = query.where(field_attr == value)
        
        # 添加预加载关联关系
        if relationships:
            for rel in relationships:
                if hasattr(model, rel):
                    query = query.options(selectinload(getattr(model, rel)))
        
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 添加排序
        if hasattr(model, params.sort_by):
            sort_field = getattr(model, params.sort_by)
            if params.sort_order.lower() == "desc":
                query = query.order_by(desc(sort_field))
            else:
                query = query.order_by(asc(sort_field))
        
        # 添加分页
        offset = (params.page - 1) * params.page_size
        query = query.offset(offset).limit(params.page_size)
        
        # 执行查询
        result = await self.db.execute(query)
        items = result.scalars().all()
        
        # 转换为字典格式
        items_dict = []
        for item in items:
            item_dict = {}
            for column in item.__table__.columns:
                value = getattr(item, column.name)
                # 处理特殊类型
                if hasattr(value, 'isoformat'):  # datetime类型
                    item_dict[column.name] = value.isoformat()
                elif hasattr(value, 'value'):  # enum类型
                    item_dict[column.name] = value.value
                else:
                    item_dict[column.name] = value
            items_dict.append(item_dict)
        
        # 计算分页信息
        total_pages = (total + params.page_size - 1) // params.page_size
        has_next = params.page < total_pages
        has_prev = params.page > 1
        
        return PaginationResult(
            items=items_dict,
            total=total,
            page=params.page,
            page_size=params.page_size,
            total_pages=total_pages,
            has_next=has_next,
            has_prev=has_prev
        )
    
    async def cursor_paginate(
        self,
        model: Type,
        cursor_field: str,
        cursor_value: Optional[Any] = None,
        page_size: int = None,
        direction: str = "next"
    ) -> Tuple[List[Dict], Optional[str], Optional[str]]:
        """
        基于游标的分页（适用于大数据集）
        
        Args:
            model: SQLAlchemy模型类
            cursor_field: 游标字段名
            cursor_value: 游标值
            page_size: 页面大小
            direction: 分页方向 ("next" 或 "prev")
        """
        if page_size is None:
            page_size = settings.DEFAULT_PAGE_SIZE
        
        if page_size > self.max_page_size:
            page_size = self.max_page_size
        
        query = select(model)
        
        # 添加游标条件
        if cursor_value is not None and hasattr(model, cursor_field):
            cursor_attr = getattr(model, cursor_field)
            if direction == "next":
                query = query.where(cursor_attr > cursor_value)
                query = query.order_by(asc(cursor_attr))
            else:  # prev
                query = query.where(cursor_attr < cursor_value)
                query = query.order_by(desc(cursor_attr))
        else:
            # 默认排序
            if hasattr(model, cursor_field):
                cursor_attr = getattr(model, cursor_field)
                query = query.order_by(desc(cursor_attr))
        
        # 获取比请求多一条记录，用于判断是否有下一页
        query = query.limit(page_size + 1)
        
        result = await self.db.execute(query)
        items = result.scalars().all()
        
        # 判断是否有更多数据
        has_more = len(items) > page_size
        if has_more:
            items = items[:page_size]
        
        # 转换为字典格式
        items_dict = []
        for item in items:
            item_dict = {}
            for column in item.__table__.columns:
                value = getattr(item, column.name)
                if hasattr(value, 'isoformat'):
                    item_dict[column.name] = value.isoformat()
                elif hasattr(value, 'value'):
                    item_dict[column.name] = value.value
                else:
                    item_dict[column.name] = value
            items_dict.append(item_dict)
        
        # 生成游标
        next_cursor = None
        prev_cursor = None
        
        if items_dict:
            if direction == "next" and has_more:
                next_cursor = str(items_dict[-1][cursor_field])
            if direction == "prev" and has_more:
                prev_cursor = str(items_dict[0][cursor_field])
            
            # 如果是第一页，设置下一页游标
            if cursor_value is None and has_more:
                next_cursor = str(items_dict[-1][cursor_field])
        
        return items_dict, next_cursor, prev_cursor
    
    async def get_aggregated_stats(
        self,
        model: Type,
        group_by_field: str,
        aggregate_fields: Dict[str, str],
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取聚合统计数据
        
        Args:
            model: SQLAlchemy模型类
            group_by_field: 分组字段
            aggregate_fields: 聚合字段配置 {"field_name": "function"}
            filters: 过滤条件
        """
        # 构建聚合查询
        select_fields = [getattr(model, group_by_field)]
        
        for field_name, func_name in aggregate_fields.items():
            if hasattr(model, field_name):
                field_attr = getattr(model, field_name)
                if func_name == "count":
                    select_fields.append(func.count(field_attr).label(f"{field_name}_{func_name}"))
                elif func_name == "sum":
                    select_fields.append(func.sum(field_attr).label(f"{field_name}_{func_name}"))
                elif func_name == "avg":
                    select_fields.append(func.avg(field_attr).label(f"{field_name}_{func_name}"))
                elif func_name == "max":
                    select_fields.append(func.max(field_attr).label(f"{field_name}_{func_name}"))
                elif func_name == "min":
                    select_fields.append(func.min(field_attr).label(f"{field_name}_{func_name}"))
        
        query = select(*select_fields)
        
        # 添加过滤条件
        if filters:
            for field, value in filters.items():
                if hasattr(model, field) and value is not None:
                    field_attr = getattr(model, field)
                    query = query.where(field_attr == value)
        
        # 添加分组
        query = query.group_by(getattr(model, group_by_field))
        
        result = await self.db.execute(query)
        rows = result.fetchall()
        
        # 转换为字典格式
        stats = []
        for row in rows:
            row_dict = {}
            for i, column in enumerate(result.keys()):
                value = row[i]
                if hasattr(value, 'isoformat'):
                    row_dict[column] = value.isoformat()
                elif hasattr(value, 'value'):
                    row_dict[column] = value.value
                else:
                    row_dict[column] = value
            stats.append(row_dict)
        
        return stats
