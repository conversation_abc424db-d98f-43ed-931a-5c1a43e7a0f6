"""
数据同步服务
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
from services.integrations.jira_integration import sync_jira_defects
from services.integrations.sonarqube_integration import sync_sonarqube_metrics
from services.integrations.jenkins_integration import sync_jenkins_builds
from services.cache_service import cache_service

logger = logging.getLogger(__name__)


class SyncStatus(Enum):
    """同步状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SyncType(Enum):
    """同步类型枚举"""
    SCHEDULED = "scheduled"
    MANUAL = "manual"
    WEBHOOK = "webhook"


@dataclass
class SyncTask:
    """同步任务数据类"""
    id: str
    name: str
    source: str
    target: str
    sync_function: Callable
    schedule_interval: int  # 秒
    last_sync: Optional[datetime] = None
    next_sync: Optional[datetime] = None
    status: SyncStatus = SyncStatus.PENDING
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SyncResult:
    """同步结果数据类"""
    task_id: str
    status: SyncStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    records_processed: int = 0
    records_success: int = 0
    records_failed: int = 0
    error_message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class DataSyncService:
    """数据同步服务"""
    
    def __init__(self):
        self.tasks: Dict[str, SyncTask] = {}
        self.sync_history: List[SyncResult] = []
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.scheduler_task: Optional[asyncio.Task] = None
        self.is_running = False
        
        # 初始化默认同步任务
        self._initialize_default_tasks()
    
    def _initialize_default_tasks(self):
        """初始化默认同步任务"""
        # JIRA缺陷同步任务
        self.add_task(SyncTask(
            id="jira_defects_sync",
            name="JIRA缺陷数据同步",
            source="JIRA",
            target="质量大盘",
            sync_function=self._sync_jira_defects,
            schedule_interval=900,  # 15分钟
            config={"project_keys": ["QD", "TEST"]}
        ))
        
        # SonarQube指标同步任务
        self.add_task(SyncTask(
            id="sonarqube_metrics_sync",
            name="SonarQube代码质量指标同步",
            source="SonarQube",
            target="质量大盘",
            sync_function=self._sync_sonarqube_metrics,
            schedule_interval=3600,  # 1小时
            config={"project_keys": ["quality-dashboard", "test-project"]}
        ))
        
        # Jenkins构建同步任务
        self.add_task(SyncTask(
            id="jenkins_builds_sync",
            name="Jenkins构建数据同步",
            source="Jenkins",
            target="质量大盘",
            sync_function=self._sync_jenkins_builds,
            schedule_interval=1800,  # 30分钟
            config={"job_names": ["quality-dashboard-build", "test-job"]}
        ))
    
    def add_task(self, task: SyncTask):
        """添加同步任务"""
        self.tasks[task.id] = task
        if task.enabled and task.next_sync is None:
            task.next_sync = datetime.now() + timedelta(seconds=task.schedule_interval)
        logger.info(f"添加同步任务: {task.name}")
    
    def remove_task(self, task_id: str) -> bool:
        """移除同步任务"""
        if task_id in self.tasks:
            # 如果任务正在运行，先取消
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                del self.running_tasks[task_id]
            
            del self.tasks[task_id]
            logger.info(f"移除同步任务: {task_id}")
            return True
        return False
    
    def enable_task(self, task_id: str) -> bool:
        """启用同步任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.enabled = True
            if task.next_sync is None:
                task.next_sync = datetime.now() + timedelta(seconds=task.schedule_interval)
            logger.info(f"启用同步任务: {task.name}")
            return True
        return False
    
    def disable_task(self, task_id: str) -> bool:
        """禁用同步任务"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            task.enabled = False
            task.next_sync = None
            
            # 如果任务正在运行，取消执行
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                del self.running_tasks[task_id]
            
            logger.info(f"禁用同步任务: {task.name}")
            return True
        return False
    
    async def execute_task(self, task_id: str, sync_type: SyncType = SyncType.MANUAL) -> SyncResult:
        """执行同步任务"""
        if task_id not in self.tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task = self.tasks[task_id]
        
        # 检查任务是否已在运行
        if task_id in self.running_tasks:
            raise RuntimeError(f"任务正在运行: {task.name}")
        
        # 创建同步结果
        result = SyncResult(
            task_id=task_id,
            status=SyncStatus.RUNNING,
            start_time=datetime.now()
        )
        
        try:
            logger.info(f"开始执行同步任务: {task.name}")
            task.status = SyncStatus.RUNNING
            
            # 执行同步函数
            sync_data = await task.sync_function(task.config)
            
            # 更新结果
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            result.data = sync_data
            
            if sync_data.get("success", False):
                result.status = SyncStatus.SUCCESS
                task.status = SyncStatus.SUCCESS
                task.retry_count = 0
                task.error_message = None
                
                # 统计处理记录数
                if "data" in sync_data:
                    if isinstance(sync_data["data"], dict):
                        result.records_processed = len(sync_data["data"].get("items", []))
                        result.records_success = result.records_processed
                    elif isinstance(sync_data["data"], list):
                        result.records_processed = len(sync_data["data"])
                        result.records_success = result.records_processed
                
                logger.info(f"同步任务执行成功: {task.name}")
            else:
                result.status = SyncStatus.FAILED
                task.status = SyncStatus.FAILED
                result.error_message = sync_data.get("error", "未知错误")
                task.error_message = result.error_message
                logger.error(f"同步任务执行失败: {task.name}, 错误: {result.error_message}")
            
        except Exception as e:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            result.status = SyncStatus.FAILED
            result.error_message = str(e)
            
            task.status = SyncStatus.FAILED
            task.error_message = str(e)
            task.retry_count += 1
            
            logger.error(f"同步任务执行异常: {task.name}, 错误: {e}")
        
        finally:
            # 更新任务状态
            task.last_sync = result.start_time
            if task.enabled and sync_type == SyncType.SCHEDULED:
                task.next_sync = datetime.now() + timedelta(seconds=task.schedule_interval)
            
            # 从运行任务中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
            
            # 保存同步历史
            self.sync_history.append(result)
            
            # 保持历史记录数量限制
            if len(self.sync_history) > 1000:
                self.sync_history = self.sync_history[-1000:]
        
        return result
    
    async def _sync_jira_defects(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """同步JIRA缺陷数据"""
        project_keys = config.get("project_keys", [])
        all_results = []
        
        for project_key in project_keys:
            try:
                result = await sync_jira_defects(project_key)
                all_results.append(result)
                
                # 清除相关缓存
                await cache_service.clear_pattern(f"defect:*")
                await cache_service.clear_pattern(f"dashboard:*")
                
            except Exception as e:
                logger.error(f"同步JIRA项目 {project_key} 失败: {e}")
                all_results.append({"success": False, "error": str(e), "project_key": project_key})
        
        # 汇总结果
        success_count = len([r for r in all_results if r.get("success", False)])
        total_count = len(all_results)
        
        return {
            "success": success_count > 0,
            "data": {
                "projects_synced": success_count,
                "total_projects": total_count,
                "results": all_results
            }
        }
    
    async def _sync_sonarqube_metrics(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """同步SonarQube指标数据"""
        project_keys = config.get("project_keys", [])
        all_results = []
        
        for project_key in project_keys:
            try:
                result = await sync_sonarqube_metrics(project_key)
                all_results.append(result)
                
                # 清除相关缓存
                await cache_service.clear_pattern(f"coverage:*")
                await cache_service.clear_pattern(f"quality_gate:*")
                await cache_service.clear_pattern(f"dashboard:*")
                
            except Exception as e:
                logger.error(f"同步SonarQube项目 {project_key} 失败: {e}")
                all_results.append({"success": False, "error": str(e), "project_key": project_key})
        
        # 汇总结果
        success_count = len([r for r in all_results if r.get("success", False)])
        total_count = len(all_results)
        
        return {
            "success": success_count > 0,
            "data": {
                "projects_synced": success_count,
                "total_projects": total_count,
                "results": all_results
            }
        }
    
    async def _sync_jenkins_builds(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """同步Jenkins构建数据"""
        job_names = config.get("job_names", [])
        all_results = []
        
        for job_name in job_names:
            try:
                result = await sync_jenkins_builds(job_name)
                all_results.append(result)
                
                # 清除相关缓存
                await cache_service.clear_pattern(f"performance:*")
                await cache_service.clear_pattern(f"dashboard:*")
                
            except Exception as e:
                logger.error(f"同步Jenkins任务 {job_name} 失败: {e}")
                all_results.append({"success": False, "error": str(e), "job_name": job_name})
        
        # 汇总结果
        success_count = len([r for r in all_results if r.get("success", False)])
        total_count = len(all_results)
        
        return {
            "success": success_count > 0,
            "data": {
                "jobs_synced": success_count,
                "total_jobs": total_count,
                "results": all_results
            }
        }
    
    async def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("启动数据同步调度器")
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # 检查需要执行的任务
                for task_id, task in self.tasks.items():
                    if (task.enabled and 
                        task.next_sync and 
                        current_time >= task.next_sync and 
                        task_id not in self.running_tasks):
                        
                        # 创建异步任务
                        async_task = asyncio.create_task(
                            self.execute_task(task_id, SyncType.SCHEDULED)
                        )
                        self.running_tasks[task_id] = async_task
                
                # 清理已完成的任务
                completed_tasks = [
                    task_id for task_id, async_task in self.running_tasks.items()
                    if async_task.done()
                ]
                for task_id in completed_tasks:
                    del self.running_tasks[task_id]
                
                # 等待下次检查
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"调度器执行异常: {e}")
                await asyncio.sleep(60)
    
    async def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        
        # 取消所有运行中的任务
        for task_id, async_task in self.running_tasks.items():
            async_task.cancel()
        
        self.running_tasks.clear()
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
        
        logger.info("停止数据同步调度器")
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return None
        
        task = self.tasks[task_id]
        return {
            "id": task.id,
            "name": task.name,
            "source": task.source,
            "target": task.target,
            "status": task.status.value,
            "enabled": task.enabled,
            "last_sync": task.last_sync.isoformat() if task.last_sync else None,
            "next_sync": task.next_sync.isoformat() if task.next_sync else None,
            "error_message": task.error_message,
            "retry_count": task.retry_count,
            "max_retries": task.max_retries,
            "schedule_interval": task.schedule_interval,
            "is_running": task_id in self.running_tasks
        }
    
    def get_all_tasks_status(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        return [self.get_task_status(task_id) for task_id in self.tasks.keys()]
    
    def get_sync_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取同步历史"""
        recent_history = self.sync_history[-limit:] if limit > 0 else self.sync_history
        
        return [
            {
                "task_id": result.task_id,
                "status": result.status.value,
                "start_time": result.start_time.isoformat(),
                "end_time": result.end_time.isoformat() if result.end_time else None,
                "duration": result.duration,
                "records_processed": result.records_processed,
                "records_success": result.records_success,
                "records_failed": result.records_failed,
                "error_message": result.error_message
            }
            for result in reversed(recent_history)
        ]


# 全局数据同步服务实例
data_sync_service = DataSyncService()
