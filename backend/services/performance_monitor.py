"""
性能监控服务
"""

import time
import psutil
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from functools import wraps
import logging

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    timestamp: datetime
    metric_name: str
    value: float
    unit: str
    tags: Dict[str, str] = None


@dataclass
class APIPerformanceMetric:
    """API性能指标"""
    endpoint: str
    method: str
    response_time: float
    status_code: int
    timestamp: datetime
    memory_usage: float
    cpu_usage: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: List[PerformanceMetric] = []
        self.api_metrics: List[APIPerformanceMetric] = []
        self.max_metrics = 10000  # 最大保存指标数量
        self.alert_thresholds = {
            'response_time': 1.0,  # 1秒
            'memory_usage': 80.0,  # 80%
            'cpu_usage': 80.0,     # 80%
            'error_rate': 5.0      # 5%
        }
    
    def add_metric(self, metric: PerformanceMetric):
        """添加性能指标"""
        self.metrics.append(metric)
        
        # 保持指标数量在限制内
        if len(self.metrics) > self.max_metrics:
            self.metrics = self.metrics[-self.max_metrics:]
    
    def add_api_metric(self, metric: APIPerformanceMetric):
        """添加API性能指标"""
        self.api_metrics.append(metric)
        
        # 保持指标数量在限制内
        if len(self.api_metrics) > self.max_metrics:
            self.api_metrics = self.api_metrics[-self.max_metrics:]
    
    def get_system_metrics(self) -> Dict[str, float]:
        """获取系统性能指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available': memory.available / (1024**3),  # GB
                'disk_usage': disk.percent,
                'disk_free': disk.free / (1024**3)  # GB
            }
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def get_api_performance_stats(self, hours: int = 24) -> Dict[str, Any]:
        """获取API性能统计"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [
            m for m in self.api_metrics 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {}
        
        # 计算统计数据
        response_times = [m.response_time for m in recent_metrics]
        error_count = len([m for m in recent_metrics if m.status_code >= 400])
        
        stats = {
            'total_requests': len(recent_metrics),
            'avg_response_time': sum(response_times) / len(response_times),
            'max_response_time': max(response_times),
            'min_response_time': min(response_times),
            'error_rate': (error_count / len(recent_metrics)) * 100,
            'requests_per_hour': len(recent_metrics) / hours
        }
        
        # 按端点分组统计
        endpoint_stats = {}
        for metric in recent_metrics:
            endpoint = f"{metric.method} {metric.endpoint}"
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    'count': 0,
                    'total_time': 0,
                    'errors': 0
                }
            
            endpoint_stats[endpoint]['count'] += 1
            endpoint_stats[endpoint]['total_time'] += metric.response_time
            if metric.status_code >= 400:
                endpoint_stats[endpoint]['errors'] += 1
        
        # 计算每个端点的平均响应时间和错误率
        for endpoint, data in endpoint_stats.items():
            data['avg_response_time'] = data['total_time'] / data['count']
            data['error_rate'] = (data['errors'] / data['count']) * 100
        
        stats['endpoint_stats'] = endpoint_stats
        
        return stats
    
    def check_performance_alerts(self) -> List[Dict[str, Any]]:
        """检查性能告警"""
        alerts = []
        
        # 检查系统指标
        system_metrics = self.get_system_metrics()
        
        for metric_name, value in system_metrics.items():
            if metric_name in self.alert_thresholds:
                threshold = self.alert_thresholds[metric_name]
                if value > threshold:
                    alerts.append({
                        'type': 'system_performance',
                        'metric': metric_name,
                        'value': value,
                        'threshold': threshold,
                        'severity': 'warning' if value < threshold * 1.2 else 'critical',
                        'timestamp': datetime.now(),
                        'message': f'{metric_name} 超过阈值: {value:.2f}% > {threshold}%'
                    })
        
        # 检查API性能
        api_stats = self.get_api_performance_stats(hours=1)  # 最近1小时
        
        if api_stats:
            # 检查平均响应时间
            if api_stats['avg_response_time'] > self.alert_thresholds['response_time']:
                alerts.append({
                    'type': 'api_performance',
                    'metric': 'response_time',
                    'value': api_stats['avg_response_time'],
                    'threshold': self.alert_thresholds['response_time'],
                    'severity': 'warning',
                    'timestamp': datetime.now(),
                    'message': f'API平均响应时间过高: {api_stats["avg_response_time"]:.2f}s'
                })
            
            # 检查错误率
            if api_stats['error_rate'] > self.alert_thresholds['error_rate']:
                alerts.append({
                    'type': 'api_performance',
                    'metric': 'error_rate',
                    'value': api_stats['error_rate'],
                    'threshold': self.alert_thresholds['error_rate'],
                    'severity': 'critical',
                    'timestamp': datetime.now(),
                    'message': f'API错误率过高: {api_stats["error_rate"]:.2f}%'
                })
        
        return alerts
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """生成性能报告"""
        system_metrics = self.get_system_metrics()
        api_stats = self.get_api_performance_stats(hours)
        alerts = self.check_performance_alerts()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'period_hours': hours,
            'system_metrics': system_metrics,
            'api_performance': api_stats,
            'alerts': alerts,
            'summary': {
                'total_alerts': len(alerts),
                'critical_alerts': len([a for a in alerts if a['severity'] == 'critical']),
                'system_health': 'good' if not alerts else 'warning'
            }
        }


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


def monitor_api_performance(func):
    """API性能监控装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        start_cpu = psutil.cpu_percent()
        
        # 提取请求信息
        request = None
        for arg in args:
            if hasattr(arg, 'method') and hasattr(arg, 'url'):
                request = arg
                break
        
        endpoint = getattr(request, 'url', {}).path if request else func.__name__
        method = getattr(request, 'method', 'UNKNOWN') if request else 'FUNCTION'
        
        try:
            # 执行原函数
            result = await func(*args, **kwargs)
            status_code = 200
            
            # 如果返回的是Response对象，获取状态码
            if hasattr(result, 'status_code'):
                status_code = result.status_code
            
            return result
            
        except Exception as e:
            status_code = 500
            raise
            
        finally:
            # 计算性能指标
            end_time = time.time()
            response_time = end_time - start_time
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            end_cpu = psutil.cpu_percent()
            
            # 记录API性能指标
            metric = APIPerformanceMetric(
                endpoint=endpoint,
                method=method,
                response_time=response_time,
                status_code=status_code,
                timestamp=datetime.now(),
                memory_usage=end_memory - start_memory,
                cpu_usage=end_cpu - start_cpu
            )
            
            performance_monitor.add_api_metric(metric)
            
            # 如果响应时间过长，记录警告
            if response_time > 1.0:
                logger.warning(
                    f"慢API调用: {method} {endpoint} - {response_time:.2f}s"
                )
    
    return wrapper


class MemoryProfiler:
    """内存分析器"""
    
    def __init__(self):
        self.snapshots = []
    
    def take_snapshot(self, label: str = None):
        """获取内存快照"""
        try:
            import tracemalloc
            
            if not tracemalloc.is_tracing():
                tracemalloc.start()
            
            snapshot = tracemalloc.take_snapshot()
            self.snapshots.append({
                'label': label or f'snapshot_{len(self.snapshots)}',
                'timestamp': datetime.now(),
                'snapshot': snapshot
            })
            
            return snapshot
        except ImportError:
            logger.warning("tracemalloc不可用，无法进行内存分析")
            return None
    
    def compare_snapshots(self, snapshot1_idx: int = -2, snapshot2_idx: int = -1):
        """比较两个内存快照"""
        if len(self.snapshots) < 2:
            return None
        
        try:
            snapshot1 = self.snapshots[snapshot1_idx]['snapshot']
            snapshot2 = self.snapshots[snapshot2_idx]['snapshot']
            
            top_stats = snapshot2.compare_to(snapshot1, 'lineno')
            
            return {
                'comparison': [
                    {
                        'filename': stat.traceback.format()[0],
                        'size_diff': stat.size_diff,
                        'count_diff': stat.count_diff
                    }
                    for stat in top_stats[:10]  # 前10个最大差异
                ]
            }
        except Exception as e:
            logger.error(f"内存快照比较失败: {e}")
            return None
    
    def get_memory_leaks(self):
        """检测内存泄漏"""
        if len(self.snapshots) < 2:
            return []
        
        comparison = self.compare_snapshots()
        if not comparison:
            return []
        
        # 查找持续增长的内存使用
        leaks = []
        for item in comparison['comparison']:
            if item['size_diff'] > 1024 * 1024:  # 大于1MB的增长
                leaks.append({
                    'file': item['filename'],
                    'size_increase': item['size_diff'],
                    'severity': 'high' if item['size_diff'] > 10 * 1024 * 1024 else 'medium'
                })
        
        return leaks


# 全局内存分析器
memory_profiler = MemoryProfiler()


async def start_performance_monitoring():
    """启动性能监控"""
    logger.info("启动性能监控服务")
    
    while True:
        try:
            # 记录系统指标
            system_metrics = performance_monitor.get_system_metrics()
            timestamp = datetime.now()
            
            for metric_name, value in system_metrics.items():
                metric = PerformanceMetric(
                    timestamp=timestamp,
                    metric_name=metric_name,
                    value=value,
                    unit='%' if 'usage' in metric_name else 'GB'
                )
                performance_monitor.add_metric(metric)
            
            # 检查告警
            alerts = performance_monitor.check_performance_alerts()
            if alerts:
                for alert in alerts:
                    logger.warning(f"性能告警: {alert['message']}")
            
            # 每分钟检查一次
            await asyncio.sleep(60)
            
        except Exception as e:
            logger.error(f"性能监控错误: {e}")
            await asyncio.sleep(60)
