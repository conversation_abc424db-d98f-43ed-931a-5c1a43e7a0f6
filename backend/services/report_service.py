"""
报告生成服务
"""
import io
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from models.dashboard import Project, Team
from models.defect import Defect
from models.coverage import CoverageMetric
from models.alert import Alert

logger = logging.getLogger(__name__)


class ReportService:
    """报告生成服务"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def generate_quality_report(
        self,
        project_ids: List[int],
        date_range: str = "30d",
        format: str = "excel",
        include_sections: List[str] = None
    ) -> bytes:
        """生成质量报告"""
        try:
            logger.info(f"开始生成质量报告，项目: {project_ids}, 格式: {format}")
            
            # 默认包含所有部分
            if include_sections is None:
                include_sections = ['overview', 'defects', 'coverage', 'alerts', 'trends']

            # 收集数据
            report_data = await self._collect_report_data(project_ids, date_range, include_sections)

            if format == "excel":
                return await self._generate_excel_report(report_data)
            elif format == "csv":
                return await self._generate_csv_report(report_data)
            elif format == "pdf":
                return await self._generate_pdf_report(report_data)
            else:
                raise ValueError(f"不支持的格式: {format}")

        except Exception as e:
            logger.error(f"生成质量报告失败: {e}")
            raise

    async def _collect_report_data(self, project_ids: List[int], date_range: str, include_sections: List[str]) -> Dict:
        """收集报告数据"""
        end_date = datetime.now()
        days_map = {"7d": 7, "30d": 30, "90d": 90, "1y": 365}
        start_date = end_date - timedelta(days=days_map.get(date_range, 30))

        report_data = {
            'metadata': {
                'generated_at': end_date.isoformat(),
                'date_range': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                'project_count': len(project_ids),
                'include_sections': include_sections
            }
        }

        # 项目基本信息
        if 'overview' in include_sections:
            projects_query = select(Project).where(Project.id.in_(project_ids))
            projects_result = await self.db.execute(projects_query)
            projects = projects_result.scalars().all()
            report_data['projects'] = projects

        # 缺陷数据
        if 'defects' in include_sections:
            defects_data = await self._collect_defects_data(project_ids, start_date, end_date)
            report_data['defects'] = defects_data

        # 覆盖率数据
        if 'coverage' in include_sections:
            coverage_data = await self._collect_coverage_data(project_ids, start_date, end_date)
            report_data['coverage'] = coverage_data

        # 预警数据
        if 'alerts' in include_sections:
            alerts_data = await self._collect_alerts_data(project_ids, start_date, end_date)
            report_data['alerts'] = alerts_data

        # 趋势数据
        if 'trends' in include_sections:
            trends_data = await self._collect_trends_data(project_ids, start_date, end_date)
            report_data['trends'] = trends_data

        return report_data

    async def _collect_defects_data(self, project_ids: List[int], start_date: datetime, end_date: datetime) -> Dict:
        """收集缺陷数据"""
        # 按严重程度统计
        severity_query = select(
            Defect.project_id,
            Defect.severity,
            func.count(Defect.id).label('count')
        ).where(
            and_(
                Defect.project_id.in_(project_ids),
                Defect.found_date >= start_date,
                Defect.found_date <= end_date
            )
        ).group_by(Defect.project_id, Defect.severity)

        severity_result = await self.db.execute(severity_query)
        severity_data = severity_result.fetchall()

        # 按状态统计
        status_query = select(
            Defect.project_id,
            Defect.status,
            func.count(Defect.id).label('count')
        ).where(
            and_(
                Defect.project_id.in_(project_ids),
                Defect.found_date >= start_date,
                Defect.found_date <= end_date
            )
        ).group_by(Defect.project_id, Defect.status)

        status_result = await self.db.execute(status_query)
        status_data = status_result.fetchall()

        # 趋势数据
        trend_query = select(
            func.date(Defect.found_date).label('date'),
            Defect.project_id,
            func.count(Defect.id).label('count')
        ).where(
            and_(
                Defect.project_id.in_(project_ids),
                Defect.found_date >= start_date,
                Defect.found_date <= end_date
            )
        ).group_by(
            func.date(Defect.found_date),
            Defect.project_id
        ).order_by(func.date(Defect.found_date))

        trend_result = await self.db.execute(trend_query)
        trend_data = trend_result.fetchall()

        return {
            'severity_stats': severity_data,
            'status_stats': status_data,
            'trend_data': trend_data
        }

    async def _collect_coverage_data(self, project_ids: List[int], start_date: datetime, end_date: datetime) -> Dict:
        """收集覆盖率数据"""
        # 平均覆盖率
        avg_query = select(
            CoverageMetric.project_id,
            func.avg(CoverageMetric.line_coverage).label('avg_line_coverage'),
            func.avg(CoverageMetric.branch_coverage).label('avg_branch_coverage'),
            func.avg(CoverageMetric.function_coverage).label('avg_function_coverage')
        ).where(
            and_(
                CoverageMetric.project_id.in_(project_ids),
                CoverageMetric.measurement_date >= start_date,
                CoverageMetric.measurement_date <= end_date
            )
        ).group_by(CoverageMetric.project_id)

        avg_result = await self.db.execute(avg_query)
        avg_data = avg_result.fetchall()

        # 趋势数据
        trend_query = select(
            func.date(CoverageMetric.measurement_date).label('date'),
            CoverageMetric.project_id,
            func.avg(CoverageMetric.line_coverage).label('line_coverage'),
            func.avg(CoverageMetric.branch_coverage).label('branch_coverage')
        ).where(
            and_(
                CoverageMetric.project_id.in_(project_ids),
                CoverageMetric.measurement_date >= start_date,
                CoverageMetric.measurement_date <= end_date
            )
        ).group_by(
            func.date(CoverageMetric.measurement_date),
            CoverageMetric.project_id
        ).order_by(func.date(CoverageMetric.measurement_date))

        trend_result = await self.db.execute(trend_query)
        trend_data = trend_result.fetchall()

        return {
            'avg_stats': avg_data,
            'trend_data': trend_data
        }

    async def _collect_alerts_data(self, project_ids: List[int], start_date: datetime, end_date: datetime) -> Dict:
        """收集预警数据"""
        # 按级别统计
        level_query = select(
            Alert.project_id,
            Alert.level,
            func.count(Alert.id).label('count')
        ).where(
            and_(
                Alert.project_id.in_(project_ids),
                Alert.created_at >= start_date,
                Alert.created_at <= end_date
            )
        ).group_by(Alert.project_id, Alert.level)

        level_result = await self.db.execute(level_query)
        level_data = level_result.fetchall()

        # 按类型统计
        type_query = select(
            Alert.project_id,
            Alert.alert_type,
            func.count(Alert.id).label('count')
        ).where(
            and_(
                Alert.project_id.in_(project_ids),
                Alert.created_at >= start_date,
                Alert.created_at <= end_date
            )
        ).group_by(Alert.project_id, Alert.alert_type)

        type_result = await self.db.execute(type_query)
        type_data = type_result.fetchall()

        return {
            'level_stats': level_data,
            'type_stats': type_data
        }

    async def _collect_trends_data(self, project_ids: List[int], start_date: datetime, end_date: datetime) -> Dict:
        """收集趋势数据"""
        # 这里可以添加更多趋势分析
        return {
            'quality_trends': [],
            'performance_trends': []
        }

    async def _generate_excel_report(self, data: Dict) -> bytes:
        """生成Excel报告"""
        output = io.BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 项目概览
            if 'projects' in data:
                projects_df = pd.DataFrame([
                    {
                        'ID': p.id,
                        '项目名称': p.name,
                        '描述': p.description or '',
                        '状态': p.status,
                        '创建时间': p.created_at.strftime('%Y-%m-%d') if p.created_at else ''
                    }
                    for p in data['projects']
                ])
                projects_df.to_excel(writer, sheet_name='项目概览', index=False)

            # 缺陷统计
            if 'defects' in data:
                defects_severity_df = pd.DataFrame([
                    {
                        '项目ID': d.project_id,
                        '严重程度': d.severity.value if hasattr(d.severity, 'value') else str(d.severity),
                        '数量': d.count
                    }
                    for d in data['defects']['severity_stats']
                ])
                defects_severity_df.to_excel(writer, sheet_name='缺陷严重程度统计', index=False)

                defects_status_df = pd.DataFrame([
                    {
                        '项目ID': d.project_id,
                        '状态': d.status.value if hasattr(d.status, 'value') else str(d.status),
                        '数量': d.count
                    }
                    for d in data['defects']['status_stats']
                ])
                defects_status_df.to_excel(writer, sheet_name='缺陷状态统计', index=False)

            # 覆盖率统计
            if 'coverage' in data:
                coverage_df = pd.DataFrame([
                    {
                        '项目ID': c.project_id,
                        '平均行覆盖率': f"{c.avg_line_coverage:.2%}" if c.avg_line_coverage else '0%',
                        '平均分支覆盖率': f"{c.avg_branch_coverage:.2%}" if c.avg_branch_coverage else '0%',
                        '平均函数覆盖率': f"{c.avg_function_coverage:.2%}" if c.avg_function_coverage else '0%'
                    }
                    for c in data['coverage']['avg_stats']
                ])
                coverage_df.to_excel(writer, sheet_name='覆盖率统计', index=False)

            # 预警统计
            if 'alerts' in data:
                alerts_level_df = pd.DataFrame([
                    {
                        '项目ID': a.project_id,
                        '预警级别': a.level,
                        '数量': a.count
                    }
                    for a in data['alerts']['level_stats']
                ])
                alerts_level_df.to_excel(writer, sheet_name='预警级别统计', index=False)

        output.seek(0)
        return output.getvalue()

    async def _generate_csv_report(self, data: Dict) -> bytes:
        """生成CSV报告"""
        # 合并所有数据到一个CSV
        combined_data = []

        if 'projects' in data:
            for project in data['projects']:
                row = {
                    '项目ID': project.id,
                    '项目名称': project.name,
                    '项目状态': project.status,
                    '创建时间': project.created_at.strftime('%Y-%m-%d') if project.created_at else ''
                }

                # 添加缺陷数据
                if 'defects' in data:
                    project_defects = [d for d in data['defects']['severity_stats'] if d.project_id == project.id]
                    for severity in ['critical', 'high', 'medium', 'low']:
                        count = sum(d.count for d in project_defects 
                                  if (hasattr(d.severity, 'value') and d.severity.value == severity) or str(d.severity) == severity)
                        row[f'{severity.upper()}缺陷数'] = count

                # 添加覆盖率数据
                if 'coverage' in data:
                    project_coverage = next((c for c in data['coverage']['avg_stats'] if c.project_id == project.id), None)
                    if project_coverage:
                        row['平均行覆盖率'] = f"{project_coverage.avg_line_coverage:.2%}" if project_coverage.avg_line_coverage else '0%'
                        row['平均分支覆盖率'] = f"{project_coverage.avg_branch_coverage:.2%}" if project_coverage.avg_branch_coverage else '0%'

                # 添加预警数据
                if 'alerts' in data:
                    project_alerts = [a for a in data['alerts']['level_stats'] if a.project_id == project.id]
                    for level in ['critical', 'warning', 'info']:
                        count = sum(a.count for a in project_alerts if a.level == level)
                        row[f'{level.upper()}预警数'] = count

                combined_data.append(row)

        df = pd.DataFrame(combined_data)
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8-sig')

        return output.getvalue().encode('utf-8-sig')

    async def _generate_pdf_report(self, data: Dict) -> bytes:
        """生成PDF报告"""
        # 这里可以使用reportlab或其他PDF生成库
        # 暂时返回空字节，实际实现需要安装相应的PDF库
        logger.warning("PDF报告生成功能尚未实现")
        return b""
