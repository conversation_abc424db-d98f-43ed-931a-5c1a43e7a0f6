"""
Redis缓存服务
"""

import json
import hashlib
from typing import Any, Optional, Dict, List
from datetime import datetime, timedelta
import redis.asyncio as redis
from config.settings import Settings
from database import get_redis

settings = Settings()


class CacheService:
    """Redis缓存服务类"""
    
    def __init__(self):
        self.redis_client = None
        self.ttl_config = settings.CACHE_TTL
    
    async def get_redis_client(self):
        """获取Redis客户端"""
        if self.redis_client is None:
            self.redis_client = await get_redis()
        return self.redis_client
    
    def _generate_cache_key(self, prefix: str, params: Dict = None) -> str:
        """生成缓存键"""
        if params:
            # 对参数进行排序和序列化，确保相同参数生成相同的键
            sorted_params = json.dumps(params, sort_keys=True)
            param_hash = hashlib.md5(sorted_params.encode()).hexdigest()[:8]
            return f"{prefix}:{param_hash}"
        return prefix
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            redis_client = await self.get_redis_client()
            cached_data = await redis_client.get(key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            print(f"缓存获取失败: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        try:
            redis_client = await self.get_redis_client()
            serialized_value = json.dumps(value, default=str)
            if ttl:
                await redis_client.setex(key, ttl, serialized_value)
            else:
                await redis_client.set(key, serialized_value)
            return True
        except Exception as e:
            print(f"缓存设置失败: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存数据"""
        try:
            redis_client = await self.get_redis_client()
            await redis_client.delete(key)
            return True
        except Exception as e:
            print(f"缓存删除失败: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> bool:
        """清除匹配模式的缓存"""
        try:
            redis_client = await self.get_redis_client()
            keys = await redis_client.keys(pattern)
            if keys:
                await redis_client.delete(*keys)
            return True
        except Exception as e:
            print(f"缓存清除失败: {e}")
            return False
    
    # 业务相关的缓存方法
    async def get_dashboard_overview(self, params: Dict = None) -> Optional[Dict]:
        """获取仪表板概览缓存"""
        key = self._generate_cache_key("dashboard:overview", params)
        return await self.get(key)
    
    async def set_dashboard_overview(self, data: Dict, params: Dict = None) -> bool:
        """设置仪表板概览缓存"""
        key = self._generate_cache_key("dashboard:overview", params)
        ttl = self.ttl_config.get("dashboard_overview", 300)
        return await self.set(key, data, ttl)
    
    async def get_dashboard_trends(self, params: Dict = None) -> Optional[Dict]:
        """获取仪表板趋势缓存"""
        key = self._generate_cache_key("dashboard:trends", params)
        return await self.get(key)
    
    async def set_dashboard_trends(self, data: Dict, params: Dict = None) -> bool:
        """设置仪表板趋势缓存"""
        key = self._generate_cache_key("dashboard:trends", params)
        ttl = self.ttl_config.get("dashboard_trends", 1800)
        return await self.set(key, data, ttl)
    
    async def get_project_stats(self, project_id: int, params: Dict = None) -> Optional[Dict]:
        """获取项目统计缓存"""
        cache_params = {"project_id": project_id, **(params or {})}
        key = self._generate_cache_key("project:stats", cache_params)
        return await self.get(key)
    
    async def set_project_stats(self, project_id: int, data: Dict, params: Dict = None) -> bool:
        """设置项目统计缓存"""
        cache_params = {"project_id": project_id, **(params or {})}
        key = self._generate_cache_key("project:stats", cache_params)
        ttl = self.ttl_config.get("project_stats", 600)
        return await self.set(key, data, ttl)
    
    async def get_team_comparison(self, params: Dict = None) -> Optional[Dict]:
        """获取团队对比缓存"""
        key = self._generate_cache_key("team:comparison", params)
        return await self.get(key)
    
    async def set_team_comparison(self, data: Dict, params: Dict = None) -> bool:
        """设置团队对比缓存"""
        key = self._generate_cache_key("team:comparison", params)
        ttl = self.ttl_config.get("team_comparison", 900)
        return await self.set(key, data, ttl)
    
    async def get_defect_trends(self, params: Dict = None) -> Optional[Dict]:
        """获取缺陷趋势缓存"""
        key = self._generate_cache_key("defect:trends", params)
        return await self.get(key)
    
    async def set_defect_trends(self, data: Dict, params: Dict = None) -> bool:
        """设置缺陷趋势缓存"""
        key = self._generate_cache_key("defect:trends", params)
        ttl = self.ttl_config.get("defect_trends", 1200)
        return await self.set(key, data, ttl)
    
    async def get_coverage_stats(self, params: Dict = None) -> Optional[Dict]:
        """获取覆盖率统计缓存"""
        key = self._generate_cache_key("coverage:stats", params)
        return await self.get(key)
    
    async def set_coverage_stats(self, data: Dict, params: Dict = None) -> bool:
        """设置覆盖率统计缓存"""
        key = self._generate_cache_key("coverage:stats", params)
        ttl = self.ttl_config.get("coverage_stats", 1800)
        return await self.set(key, data, ttl)
    
    async def get_performance_metrics(self, params: Dict = None) -> Optional[Dict]:
        """获取性能指标缓存"""
        key = self._generate_cache_key("performance:metrics", params)
        return await self.get(key)
    
    async def set_performance_metrics(self, data: Dict, params: Dict = None) -> bool:
        """设置性能指标缓存"""
        key = self._generate_cache_key("performance:metrics", params)
        ttl = self.ttl_config.get("performance_metrics", 300)
        return await self.set(key, data, ttl)
    
    async def get_quality_gate_stats(self, params: Dict = None) -> Optional[Dict]:
        """获取质量门禁统计缓存"""
        key = self._generate_cache_key("quality_gate:stats", params)
        return await self.get(key)
    
    async def set_quality_gate_stats(self, data: Dict, params: Dict = None) -> bool:
        """设置质量门禁统计缓存"""
        key = self._generate_cache_key("quality_gate:stats", params)
        ttl = self.ttl_config.get("quality_gate_stats", 600)
        return await self.set(key, data, ttl)
    
    async def invalidate_project_cache(self, project_id: int):
        """清除项目相关缓存"""
        patterns = [
            f"project:stats:*{project_id}*",
            f"dashboard:*",
            f"team:*",
            f"defect:*{project_id}*",
            f"coverage:*{project_id}*"
        ]
        for pattern in patterns:
            await self.clear_pattern(pattern)
    
    async def invalidate_all_cache(self):
        """清除所有缓存"""
        await self.clear_pattern("*")


# 创建全局缓存服务实例
cache_service = CacheService()
