"""
覆盖率工具集成服务
支持多种覆盖率工具的数据采集和处理
"""

import json
import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
import aiohttp
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from models.coverage import (
    CoverageMetric, FileCoverage, CoverageSource, CoverageType
)


class CoverageIntegrationService:
    """覆盖率集成服务"""
    
    def __init__(self, db_session: AsyncSession):
        self.db = db_session
    
    async def import_jest_coverage(self, project_id: int, coverage_file_path: str, 
                                 branch_name: str = "main", build_number: str = None) -> CoverageMetric:
        """导入Jest覆盖率数据"""
        
        try:
            with open(coverage_file_path, 'r', encoding='utf-8') as f:
                coverage_data = json.load(f)
            
            # 解析Jest覆盖率数据
            total_coverage = coverage_data.get('total', {})
            
            # 创建覆盖率指标
            metric = CoverageMetric(
                project_id=project_id,
                branch_name=branch_name,
                build_number=build_number,
                line_coverage=total_coverage.get('lines', {}).get('pct', 0.0),
                branch_coverage=total_coverage.get('branches', {}).get('pct', 0.0),
                function_coverage=total_coverage.get('functions', {}).get('pct', 0.0),
                statement_coverage=total_coverage.get('statements', {}).get('pct', 0.0),
                total_lines=total_coverage.get('lines', {}).get('total', 0),
                covered_lines=total_coverage.get('lines', {}).get('covered', 0),
                total_branches=total_coverage.get('branches', {}).get('total', 0),
                covered_branches=total_coverage.get('branches', {}).get('covered', 0),
                total_functions=total_coverage.get('functions', {}).get('total', 0),
                covered_functions=total_coverage.get('functions', {}).get('covered', 0),
                source=CoverageSource.JEST,
                raw_data=coverage_data
            )
            
            self.db.add(metric)
            await self.db.flush()
            
            # 处理文件级覆盖率
            files_data = coverage_data.get('files', {})
            for file_path, file_data in files_data.items():
                file_coverage = FileCoverage(
                    coverage_metric_id=metric.id,
                    file_path=file_path,
                    file_name=Path(file_path).name,
                    package_name=self._extract_package_name(file_path),
                    line_coverage=file_data.get('lines', {}).get('pct', 0.0),
                    branch_coverage=file_data.get('branches', {}).get('pct', 0.0),
                    function_coverage=file_data.get('functions', {}).get('pct', 0.0),
                    total_lines=file_data.get('lines', {}).get('total', 0),
                    covered_lines=file_data.get('lines', {}).get('covered', 0),
                    uncovered_lines=self._format_uncovered_lines(file_data.get('uncoveredLines', []))
                )
                self.db.add(file_coverage)
            
            await self.db.commit()
            return metric
            
        except Exception as e:
            await self.db.rollback()
            raise Exception(f"Failed to import Jest coverage: {str(e)}")
    
    async def import_jacoco_coverage(self, project_id: int, xml_file_path: str,
                                   branch_name: str = "main", build_number: str = None) -> CoverageMetric:
        """导入JaCoCo覆盖率数据"""
        
        try:
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            # 解析JaCoCo XML数据
            total_lines = 0
            covered_lines = 0
            total_branches = 0
            covered_branches = 0
            total_methods = 0
            covered_methods = 0
            
            # 遍历所有counter元素
            for counter in root.findall('.//counter'):
                counter_type = counter.get('type')
                missed = int(counter.get('missed', 0))
                covered = int(counter.get('covered', 0))
                total = missed + covered
                
                if counter_type == 'LINE':
                    total_lines += total
                    covered_lines += covered
                elif counter_type == 'BRANCH':
                    total_branches += total
                    covered_branches += covered
                elif counter_type == 'METHOD':
                    total_methods += total
                    covered_methods += covered
            
            # 计算覆盖率百分比
            line_coverage = (covered_lines / total_lines * 100) if total_lines > 0 else 0
            branch_coverage = (covered_branches / total_branches * 100) if total_branches > 0 else 0
            function_coverage = (covered_methods / total_methods * 100) if total_methods > 0 else 0
            
            # 创建覆盖率指标
            metric = CoverageMetric(
                project_id=project_id,
                branch_name=branch_name,
                build_number=build_number,
                line_coverage=round(line_coverage, 2),
                branch_coverage=round(branch_coverage, 2),
                function_coverage=round(function_coverage, 2),
                total_lines=total_lines,
                covered_lines=covered_lines,
                total_branches=total_branches,
                covered_branches=covered_branches,
                total_functions=total_methods,
                covered_functions=covered_methods,
                source=CoverageSource.JACOCO,
                raw_data={"xml_file": xml_file_path}
            )
            
            self.db.add(metric)
            await self.db.flush()
            
            # 处理包级和类级覆盖率
            await self._process_jacoco_packages(root, metric.id)
            
            await self.db.commit()
            return metric
            
        except Exception as e:
            await self.db.rollback()
            raise Exception(f"Failed to import JaCoCo coverage: {str(e)}")
    
    async def import_sonarqube_coverage(self, project_id: int, sonar_url: str, 
                                      project_key: str, auth_token: str,
                                      branch_name: str = "main") -> CoverageMetric:
        """从SonarQube导入覆盖率数据"""
        
        try:
            # SonarQube API端点
            measures_url = f"{sonar_url}/api/measures/component"
            
            # 请求参数
            params = {
                'component': project_key,
                'metricKeys': 'coverage,line_coverage,branch_coverage,lines_to_cover,uncovered_lines,conditions_to_cover,uncovered_conditions'
            }
            
            if branch_name != "main":
                params['branch'] = branch_name
            
            headers = {
                'Authorization': f'Bearer {auth_token}'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(measures_url, params=params, headers=headers) as response:
                    if response.status != 200:
                        raise Exception(f"SonarQube API error: {response.status}")
                    
                    data = await response.json()
            
            # 解析SonarQube数据
            measures = {m['metric']: float(m['value']) for m in data['component']['measures']}
            
            line_coverage = measures.get('line_coverage', 0.0)
            branch_coverage = measures.get('branch_coverage', 0.0)
            overall_coverage = measures.get('coverage', 0.0)
            
            lines_to_cover = int(measures.get('lines_to_cover', 0))
            uncovered_lines = int(measures.get('uncovered_lines', 0))
            covered_lines = lines_to_cover - uncovered_lines
            
            conditions_to_cover = int(measures.get('conditions_to_cover', 0))
            uncovered_conditions = int(measures.get('uncovered_conditions', 0))
            covered_conditions = conditions_to_cover - uncovered_conditions
            
            # 创建覆盖率指标
            metric = CoverageMetric(
                project_id=project_id,
                branch_name=branch_name,
                line_coverage=line_coverage,
                branch_coverage=branch_coverage,
                statement_coverage=overall_coverage,
                total_lines=lines_to_cover,
                covered_lines=covered_lines,
                total_branches=conditions_to_cover,
                covered_branches=covered_conditions,
                source=CoverageSource.SONARQUBE,
                report_url=f"{sonar_url}/dashboard?id={project_key}",
                raw_data=data
            )
            
            self.db.add(metric)
            await self.db.commit()
            return metric
            
        except Exception as e:
            await self.db.rollback()
            raise Exception(f"Failed to import SonarQube coverage: {str(e)}")
    
    async def import_pytest_coverage(self, project_id: int, coverage_file_path: str,
                                   branch_name: str = "main", build_number: str = None) -> CoverageMetric:
        """导入Pytest覆盖率数据"""
        
        try:
            with open(coverage_file_path, 'r', encoding='utf-8') as f:
                coverage_data = json.load(f)
            
            # 解析Pytest覆盖率数据
            totals = coverage_data.get('totals', {})
            
            # 创建覆盖率指标
            metric = CoverageMetric(
                project_id=project_id,
                branch_name=branch_name,
                build_number=build_number,
                line_coverage=totals.get('percent_covered', 0.0),
                statement_coverage=totals.get('percent_covered', 0.0),
                total_lines=totals.get('num_statements', 0),
                covered_lines=totals.get('covered_lines', 0),
                source=CoverageSource.PYTEST,
                raw_data=coverage_data
            )
            
            self.db.add(metric)
            await self.db.flush()
            
            # 处理文件级覆盖率
            files_data = coverage_data.get('files', {})
            for file_path, file_data in files_data.items():
                summary = file_data.get('summary', {})
                file_coverage = FileCoverage(
                    coverage_metric_id=metric.id,
                    file_path=file_path,
                    file_name=Path(file_path).name,
                    package_name=self._extract_package_name(file_path),
                    line_coverage=summary.get('percent_covered', 0.0),
                    total_lines=summary.get('num_statements', 0),
                    covered_lines=summary.get('covered_lines', 0),
                    uncovered_lines=self._format_uncovered_lines(file_data.get('missing_lines', []))
                )
                self.db.add(file_coverage)
            
            await self.db.commit()
            return metric
            
        except Exception as e:
            await self.db.rollback()
            raise Exception(f"Failed to import Pytest coverage: {str(e)}")
    
    def _extract_package_name(self, file_path: str) -> str:
        """从文件路径提取包名"""
        path = Path(file_path)
        
        # 对于JavaScript/TypeScript文件
        if path.suffix in ['.js', '.ts', '.jsx', '.tsx']:
            parts = path.parts
            if 'src' in parts:
                src_index = parts.index('src')
                if src_index < len(parts) - 1:
                    return '/'.join(parts[src_index + 1:-1])
        
        # 对于Python文件
        elif path.suffix == '.py':
            parts = path.parts
            # 移除文件名，保留目录结构
            return '.'.join(parts[:-1]) if len(parts) > 1 else ''
        
        # 对于Java文件
        elif path.suffix == '.java':
            parts = path.parts
            if 'src' in parts:
                src_index = parts.index('src')
                # 查找main/java或test/java
                for i in range(src_index + 1, len(parts)):
                    if parts[i] == 'java':
                        if i < len(parts) - 1:
                            return '.'.join(parts[i + 1:-1])
        
        # 默认返回目录路径
        return '/'.join(path.parts[:-1]) if len(path.parts) > 1 else ''
    
    def _format_uncovered_lines(self, uncovered_lines: List[int]) -> str:
        """格式化未覆盖行号"""
        if not uncovered_lines:
            return ""
        
        # 将连续的行号合并为范围
        ranges = []
        start = uncovered_lines[0]
        end = start
        
        for line in uncovered_lines[1:]:
            if line == end + 1:
                end = line
            else:
                if start == end:
                    ranges.append(str(start))
                else:
                    ranges.append(f"{start}-{end}")
                start = end = line
        
        # 添加最后一个范围
        if start == end:
            ranges.append(str(start))
        else:
            ranges.append(f"{start}-{end}")
        
        return ", ".join(ranges)
    
    async def _process_jacoco_packages(self, root: ET.Element, metric_id: int):
        """处理JaCoCo包级覆盖率数据"""
        
        for package in root.findall('.//package'):
            package_name = package.get('name', '').replace('/', '.')
            
            for class_elem in package.findall('.//class'):
                class_name = class_elem.get('name', '').replace('/', '.')
                source_file = class_elem.get('sourcefilename', '')
                
                if source_file:
                    # 计算类级覆盖率
                    line_missed = 0
                    line_covered = 0
                    branch_missed = 0
                    branch_covered = 0
                    
                    for counter in class_elem.findall('.//counter'):
                        counter_type = counter.get('type')
                        missed = int(counter.get('missed', 0))
                        covered = int(counter.get('covered', 0))
                        
                        if counter_type == 'LINE':
                            line_missed += missed
                            line_covered += covered
                        elif counter_type == 'BRANCH':
                            branch_missed += missed
                            branch_covered += covered
                    
                    total_lines = line_missed + line_covered
                    total_branches = branch_missed + branch_covered
                    
                    line_coverage = (line_covered / total_lines * 100) if total_lines > 0 else 0
                    branch_coverage = (branch_covered / total_branches * 100) if total_branches > 0 else 0
                    
                    file_coverage = FileCoverage(
                        coverage_metric_id=metric_id,
                        file_path=f"{package_name}/{source_file}",
                        file_name=source_file,
                        package_name=package_name,
                        line_coverage=round(line_coverage, 2),
                        branch_coverage=round(branch_coverage, 2),
                        total_lines=total_lines,
                        covered_lines=line_covered
                    )
                    self.db.add(file_coverage)
