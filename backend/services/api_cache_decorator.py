"""
API响应缓存装饰器
"""

import json
import hashlib
from functools import wraps
from typing import Any, Callable, Dict, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from services.cache_service import cache_service


def cache_response(
    ttl: int = 300,
    key_prefix: str = "api",
    include_params: bool = True,
    include_headers: bool = False,
    cache_condition: Optional[Callable] = None
):
    """
    API响应缓存装饰器
    
    Args:
        ttl: 缓存时间（秒）
        key_prefix: 缓存键前缀
        include_params: 是否包含请求参数在缓存键中
        include_headers: 是否包含请求头在缓存键中
        cache_condition: 缓存条件函数，返回True时才缓存
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 提取请求对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            # 生成缓存键
            cache_key = _generate_cache_key(
                func.__name__,
                key_prefix,
                request,
                kwargs,
                include_params,
                include_headers
            )
            
            # 尝试从缓存获取数据
            cached_data = await cache_service.get(cache_key)
            if cached_data is not None:
                # 返回缓存的响应
                return JSONResponse(
                    content=cached_data,
                    headers={"X-Cache": "HIT"}
                )
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 检查是否需要缓存
            should_cache = True
            if cache_condition:
                should_cache = cache_condition(result)
            
            # 缓存响应数据
            if should_cache and isinstance(result, (dict, JSONResponse)):
                if isinstance(result, JSONResponse):
                    cache_data = json.loads(result.body.decode())
                else:
                    cache_data = result
                
                await cache_service.set(cache_key, cache_data, ttl)
                
                # 添加缓存标识头
                if isinstance(result, JSONResponse):
                    result.headers["X-Cache"] = "MISS"
                else:
                    result = JSONResponse(
                        content=result,
                        headers={"X-Cache": "MISS"}
                    )
            
            return result
        
        return wrapper
    return decorator


def _generate_cache_key(
    func_name: str,
    prefix: str,
    request: Optional[Request],
    kwargs: Dict[str, Any],
    include_params: bool,
    include_headers: bool
) -> str:
    """生成缓存键"""
    key_parts = [prefix, func_name]
    
    # 添加路径参数
    if kwargs:
        path_params = {k: v for k, v in kwargs.items() if not k.startswith('_')}
        if path_params:
            params_str = json.dumps(path_params, sort_keys=True)
            key_parts.append(hashlib.md5(params_str.encode()).hexdigest()[:8])
    
    # 添加查询参数
    if include_params and request:
        query_params = dict(request.query_params)
        if query_params:
            params_str = json.dumps(query_params, sort_keys=True)
            key_parts.append(hashlib.md5(params_str.encode()).hexdigest()[:8])
    
    # 添加请求头
    if include_headers and request:
        relevant_headers = {
            k: v for k, v in request.headers.items()
            if k.lower() in ['accept', 'accept-language', 'user-agent']
        }
        if relevant_headers:
            headers_str = json.dumps(relevant_headers, sort_keys=True)
            key_parts.append(hashlib.md5(headers_str.encode()).hexdigest()[:8])
    
    return ":".join(key_parts)


def cache_invalidate(patterns: list):
    """
    缓存失效装饰器
    
    Args:
        patterns: 需要清除的缓存模式列表
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 清除相关缓存
            for pattern in patterns:
                # 支持动态模式，可以使用kwargs中的值
                if "{" in pattern and "}" in pattern:
                    try:
                        formatted_pattern = pattern.format(**kwargs)
                        await cache_service.clear_pattern(formatted_pattern)
                    except KeyError:
                        # 如果格式化失败，使用原模式
                        await cache_service.clear_pattern(pattern)
                else:
                    await cache_service.clear_pattern(pattern)
            
            return result
        
        return wrapper
    return decorator


class ConditionalCache:
    """条件缓存类"""
    
    @staticmethod
    def success_only(response: Any) -> bool:
        """只缓存成功的响应"""
        if isinstance(response, dict):
            return response.get("success", False)
        elif isinstance(response, JSONResponse):
            try:
                data = json.loads(response.body.decode())
                return data.get("success", False)
            except:
                return False
        return False
    
    @staticmethod
    def non_empty_data(response: Any) -> bool:
        """只缓存非空数据的响应"""
        if isinstance(response, dict):
            data = response.get("data")
            return data is not None and (
                (isinstance(data, (list, dict)) and len(data) > 0) or
                (not isinstance(data, (list, dict)))
            )
        elif isinstance(response, JSONResponse):
            try:
                response_data = json.loads(response.body.decode())
                data = response_data.get("data")
                return data is not None and (
                    (isinstance(data, (list, dict)) and len(data) > 0) or
                    (not isinstance(data, (list, dict)))
                )
            except:
                return False
        return False
    
    @staticmethod
    def status_200_only(response: Any) -> bool:
        """只缓存状态码为200的响应"""
        if isinstance(response, JSONResponse):
            return response.status_code == 200
        return True  # 对于dict类型，默认认为是成功的


# 预定义的缓存装饰器
def cache_dashboard_data(ttl: int = 300):
    """仪表板数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="dashboard",
        include_params=True,
        cache_condition=ConditionalCache.success_only
    )


def cache_project_data(ttl: int = 600):
    """项目数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="project",
        include_params=True,
        cache_condition=ConditionalCache.non_empty_data
    )


def cache_team_data(ttl: int = 900):
    """团队数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="team",
        include_params=True,
        cache_condition=ConditionalCache.success_only
    )


def cache_defect_data(ttl: int = 1200):
    """缺陷数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="defect",
        include_params=True,
        cache_condition=ConditionalCache.success_only
    )


def cache_coverage_data(ttl: int = 1800):
    """覆盖率数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="coverage",
        include_params=True,
        cache_condition=ConditionalCache.success_only
    )


def cache_performance_data(ttl: int = 300):
    """性能数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="performance",
        include_params=True,
        cache_condition=ConditionalCache.success_only
    )


def cache_quality_gate_data(ttl: int = 600):
    """质量门禁数据缓存装饰器"""
    return cache_response(
        ttl=ttl,
        key_prefix="quality_gate",
        include_params=True,
        cache_condition=ConditionalCache.success_only
    )


# 缓存失效模式
CACHE_INVALIDATION_PATTERNS = {
    "project": [
        "dashboard:*",
        "project:*",
        "team:*"
    ],
    "defect": [
        "dashboard:*",
        "defect:*",
        "project:*:{project_id}*"
    ],
    "coverage": [
        "dashboard:*",
        "coverage:*",
        "project:*:{project_id}*"
    ],
    "performance": [
        "dashboard:*",
        "performance:*",
        "project:*:{project_id}*"
    ],
    "quality_gate": [
        "dashboard:*",
        "quality_gate:*",
        "project:*:{project_id}*"
    ]
}
