"""
Jenkins集成服务
"""

import aiohttp
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import base64
import json
import logging
from dataclasses import dataclass
from config.settings import Settings

logger = logging.getLogger(__name__)
settings = Settings()


@dataclass
class JenkinsBuild:
    """Jenkins构建数据类"""
    number: int
    url: str
    result: str
    duration: int
    timestamp: datetime
    building: bool
    description: str
    display_name: str
    full_display_name: str


@dataclass
class JenkinsJob:
    """Jenkins任务数据类"""
    name: str
    url: str
    color: str
    buildable: bool
    last_build: Optional[JenkinsBuild]
    last_successful_build: Optional[JenkinsBuild]
    last_failed_build: Optional[JenkinsBuild]


class JenkinsIntegration:
    """Jenkins集成服务"""
    
    def __init__(self):
        self.base_url = settings.JENKINS_URL
        self.username = settings.JENKINS_USERNAME
        self.token = settings.JENKINS_TOKEN
        self.session = None
        self.auth_header = self._create_auth_header()
    
    def _create_auth_header(self) -> str:
        """创建认证头"""
        if not self.username or not self.token:
            raise ValueError("<PERSON>用户名和令牌未配置")
        
        credentials = f"{self.username}:{self.token}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers={
                'Authorization': self.auth_header,
                'Content-Type': 'application/json'
            },
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def test_connection(self) -> bool:
        """测试Jenkins连接"""
        try:
            url = f"{self.base_url}/api/json"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"Jenkins连接成功，版本: {data.get('version', 'unknown')}")
                    return True
                else:
                    logger.error(f"Jenkins连接失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"Jenkins连接测试失败: {e}")
            return False
    
    async def get_jobs(self) -> List[JenkinsJob]:
        """获取任务列表"""
        try:
            url = f"{self.base_url}/api/json?tree=jobs[name,url,color,buildable,lastBuild[number,url,result,duration,timestamp,building,description,displayName,fullDisplayName],lastSuccessfulBuild[number,url,result,duration,timestamp,building,description,displayName,fullDisplayName],lastFailedBuild[number,url,result,duration,timestamp,building,description,displayName,fullDisplayName]]"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    jobs = []
                    
                    for job_data in data.get('jobs', []):
                        job = JenkinsJob(
                            name=job_data['name'],
                            url=job_data['url'],
                            color=job_data['color'],
                            buildable=job_data['buildable'],
                            last_build=self._parse_build(job_data.get('lastBuild')),
                            last_successful_build=self._parse_build(job_data.get('lastSuccessfulBuild')),
                            last_failed_build=self._parse_build(job_data.get('lastFailedBuild'))
                        )
                        jobs.append(job)
                    
                    logger.info(f"获取到 {len(jobs)} 个Jenkins任务")
                    return jobs
                else:
                    logger.error(f"获取任务列表失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取任务列表异常: {e}")
            return []
    
    def _parse_build(self, build_data: Optional[Dict[str, Any]]) -> Optional[JenkinsBuild]:
        """解析构建数据"""
        if not build_data:
            return None
        
        try:
            timestamp = datetime.fromtimestamp(build_data['timestamp'] / 1000)
            
            return JenkinsBuild(
                number=build_data['number'],
                url=build_data['url'],
                result=build_data.get('result', 'UNKNOWN'),
                duration=build_data.get('duration', 0),
                timestamp=timestamp,
                building=build_data.get('building', False),
                description=build_data.get('description', ''),
                display_name=build_data.get('displayName', ''),
                full_display_name=build_data.get('fullDisplayName', '')
            )
        except Exception as e:
            logger.error(f"解析构建数据失败: {e}")
            return None
    
    async def get_job_builds(
        self, 
        job_name: str, 
        limit: int = 50
    ) -> List[JenkinsBuild]:
        """获取任务的构建历史"""
        try:
            url = f"{self.base_url}/job/{job_name}/api/json?tree=builds[number,url,result,duration,timestamp,building,description,displayName,fullDisplayName]{{0,{limit}}}"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    builds = []
                    
                    for build_data in data.get('builds', []):
                        build = self._parse_build(build_data)
                        if build:
                            builds.append(build)
                    
                    logger.info(f"获取到任务 {job_name} 的 {len(builds)} 个构建")
                    return builds
                else:
                    logger.error(f"获取构建历史失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取构建历史异常: {e}")
            return []
    
    async def get_build_statistics(self, job_name: str, days: int = 30) -> Dict[str, Any]:
        """获取构建统计数据"""
        builds = await self.get_job_builds(job_name, limit=200)
        
        if not builds:
            return {}
        
        # 过滤最近N天的构建
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_builds = [build for build in builds if build.timestamp >= cutoff_date]
        
        if not recent_builds:
            return {}
        
        # 统计分析
        total_builds = len(recent_builds)
        successful_builds = len([b for b in recent_builds if b.result == 'SUCCESS'])
        failed_builds = len([b for b in recent_builds if b.result == 'FAILURE'])
        unstable_builds = len([b for b in recent_builds if b.result == 'UNSTABLE'])
        aborted_builds = len([b for b in recent_builds if b.result == 'ABORTED'])
        
        # 计算成功率
        success_rate = (successful_builds / total_builds) * 100 if total_builds > 0 else 0
        
        # 计算平均构建时间
        completed_builds = [b for b in recent_builds if not b.building and b.duration > 0]
        avg_duration = sum(b.duration for b in completed_builds) / len(completed_builds) if completed_builds else 0
        avg_duration_minutes = avg_duration / (1000 * 60)  # 转换为分钟
        
        # 按天统计构建数量
        daily_stats = {}
        for build in recent_builds:
            date_key = build.timestamp.strftime('%Y-%m-%d')
            if date_key not in daily_stats:
                daily_stats[date_key] = {
                    'total': 0,
                    'success': 0,
                    'failure': 0,
                    'unstable': 0,
                    'aborted': 0
                }
            
            daily_stats[date_key]['total'] += 1
            if build.result == 'SUCCESS':
                daily_stats[date_key]['success'] += 1
            elif build.result == 'FAILURE':
                daily_stats[date_key]['failure'] += 1
            elif build.result == 'UNSTABLE':
                daily_stats[date_key]['unstable'] += 1
            elif build.result == 'ABORTED':
                daily_stats[date_key]['aborted'] += 1
        
        # 计算构建频率
        builds_per_day = total_builds / days if days > 0 else 0
        
        return {
            'summary': {
                'total_builds': total_builds,
                'successful_builds': successful_builds,
                'failed_builds': failed_builds,
                'unstable_builds': unstable_builds,
                'aborted_builds': aborted_builds,
                'success_rate': success_rate,
                'avg_duration_minutes': avg_duration_minutes,
                'builds_per_day': builds_per_day
            },
            'daily_trends': daily_stats,
            'period': {
                'start_date': cutoff_date.isoformat(),
                'end_date': datetime.now().isoformat(),
                'days': days
            }
        }
    
    async def trigger_build(self, job_name: str, parameters: Optional[Dict[str, str]] = None) -> bool:
        """触发构建"""
        try:
            if parameters:
                url = f"{self.base_url}/job/{job_name}/buildWithParameters"
                data = parameters
            else:
                url = f"{self.base_url}/job/{job_name}/build"
                data = {}
            
            async with self.session.post(url, data=data) as response:
                if response.status in [200, 201]:
                    logger.info(f"成功触发任务 {job_name} 的构建")
                    return True
                else:
                    logger.error(f"触发构建失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"触发构建异常: {e}")
            return False
    
    async def get_build_console_output(self, job_name: str, build_number: int) -> str:
        """获取构建控制台输出"""
        try:
            url = f"{self.base_url}/job/{job_name}/{build_number}/consoleText"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.error(f"获取控制台输出失败，状态码: {response.status}")
                    return ""
        except Exception as e:
            logger.error(f"获取控制台输出异常: {e}")
            return ""
    
    async def get_pipeline_stages(self, job_name: str, build_number: int) -> List[Dict[str, Any]]:
        """获取流水线阶段信息"""
        try:
            url = f"{self.base_url}/job/{job_name}/{build_number}/wfapi/describe"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    stages = []
                    
                    for stage in data.get('stages', []):
                        stages.append({
                            'id': stage.get('id'),
                            'name': stage.get('name'),
                            'status': stage.get('status'),
                            'start_time': stage.get('startTimeMillis'),
                            'duration': stage.get('durationMillis'),
                            'pause_duration': stage.get('pauseDurationMillis', 0)
                        })
                    
                    return stages
                else:
                    logger.error(f"获取流水线阶段失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取流水线阶段异常: {e}")
            return []


# 全局Jenkins集成实例
jenkins_integration = JenkinsIntegration()


async def sync_jenkins_builds(job_name: str) -> Dict[str, Any]:
    """同步Jenkins构建数据"""
    try:
        async with jenkins_integration as jenkins:
            # 测试连接
            if not await jenkins.test_connection():
                return {"success": False, "error": "Jenkins连接失败"}
            
            # 获取构建统计数据
            stats = await jenkins.get_build_statistics(job_name, days=30)
            
            # 获取最近的构建列表
            recent_builds = await jenkins.get_job_builds(job_name, limit=20)
            
            return {
                "success": True,
                "data": {
                    "statistics": stats,
                    "recent_builds": [
                        {
                            "number": build.number,
                            "result": build.result,
                            "duration": build.duration,
                            "timestamp": build.timestamp.isoformat(),
                            "building": build.building,
                            "display_name": build.display_name
                        }
                        for build in recent_builds
                    ]
                }
            }
    except Exception as e:
        logger.error(f"同步Jenkins构建数据失败: {e}")
        return {"success": False, "error": str(e)}
