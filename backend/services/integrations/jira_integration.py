"""
JIRA集成服务
"""

import aiohttp
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import base64
import json
import logging
from dataclasses import dataclass
from config.settings import Settings

logger = logging.getLogger(__name__)
settings = Settings()


@dataclass
class JiraIssue:
    """JIRA问题数据类"""
    key: str
    summary: str
    description: str
    issue_type: str
    status: str
    priority: str
    assignee: Optional[str]
    reporter: str
    created: datetime
    updated: datetime
    resolved: Optional[datetime]
    project_key: str
    labels: List[str]
    components: List[str]
    fix_versions: List[str]


class JiraIntegration:
    """JIRA集成服务"""
    
    def __init__(self):
        self.base_url = settings.JIRA_URL
        self.username = settings.JIRA_USERNAME
        self.token = settings.JIRA_TOKEN
        self.session = None
        self.auth_header = self._create_auth_header()
    
    def _create_auth_header(self) -> str:
        """创建认证头"""
        if not self.username or not self.token:
            raise ValueError("JIRA用户名和令牌未配置")
        
        credentials = f"{self.username}:{self.token}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            headers={
                'Authorization': self.auth_header,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def test_connection(self) -> bool:
        """测试JIRA连接"""
        try:
            url = f"{self.base_url}/rest/api/2/myself"
            async with self.session.get(url) as response:
                if response.status == 200:
                    user_info = await response.json()
                    logger.info(f"JIRA连接成功，用户: {user_info.get('displayName')}")
                    return True
                else:
                    logger.error(f"JIRA连接失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"JIRA连接测试失败: {e}")
            return False
    
    async def get_projects(self) -> List[Dict[str, Any]]:
        """获取项目列表"""
        try:
            url = f"{self.base_url}/rest/api/2/project"
            async with self.session.get(url) as response:
                if response.status == 200:
                    projects = await response.json()
                    return [
                        {
                            'key': project['key'],
                            'name': project['name'],
                            'description': project.get('description', ''),
                            'lead': project.get('lead', {}).get('displayName', ''),
                            'project_type': project.get('projectTypeKey', '')
                        }
                        for project in projects
                    ]
                else:
                    logger.error(f"获取项目列表失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取项目列表异常: {e}")
            return []
    
    async def get_issues(
        self, 
        project_key: str, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        issue_types: Optional[List[str]] = None,
        statuses: Optional[List[str]] = None,
        max_results: int = 1000
    ) -> List[JiraIssue]:
        """获取问题列表"""
        try:
            # 构建JQL查询
            jql_parts = [f"project = {project_key}"]
            
            if start_date:
                jql_parts.append(f"created >= '{start_date.strftime('%Y-%m-%d')}'")
            
            if end_date:
                jql_parts.append(f"created <= '{end_date.strftime('%Y-%m-%d')}'")
            
            if issue_types:
                types_str = "', '".join(issue_types)
                jql_parts.append(f"issuetype in ('{types_str}')")
            
            if statuses:
                statuses_str = "', '".join(statuses)
                jql_parts.append(f"status in ('{statuses_str}')")
            
            jql = " AND ".join(jql_parts)
            
            # API请求参数
            params = {
                'jql': jql,
                'maxResults': max_results,
                'fields': 'summary,description,issuetype,status,priority,assignee,reporter,created,updated,resolutiondate,project,labels,components,fixVersions'
            }
            
            url = f"{self.base_url}/rest/api/2/search"
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    issues = []
                    
                    for issue_data in data.get('issues', []):
                        issue = self._parse_issue(issue_data)
                        if issue:
                            issues.append(issue)
                    
                    logger.info(f"获取到 {len(issues)} 个问题")
                    return issues
                else:
                    logger.error(f"获取问题列表失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取问题列表异常: {e}")
            return []
    
    def _parse_issue(self, issue_data: Dict[str, Any]) -> Optional[JiraIssue]:
        """解析问题数据"""
        try:
            fields = issue_data['fields']
            
            # 解析日期
            created = datetime.fromisoformat(fields['created'].replace('Z', '+00:00'))
            updated = datetime.fromisoformat(fields['updated'].replace('Z', '+00:00'))
            
            resolved = None
            if fields.get('resolutiondate'):
                resolved = datetime.fromisoformat(fields['resolutiondate'].replace('Z', '+00:00'))
            
            # 解析用户
            assignee = None
            if fields.get('assignee'):
                assignee = fields['assignee'].get('displayName', '')
            
            reporter = ''
            if fields.get('reporter'):
                reporter = fields['reporter'].get('displayName', '')
            
            # 解析标签和组件
            labels = [label for label in fields.get('labels', [])]
            components = [comp['name'] for comp in fields.get('components', [])]
            fix_versions = [version['name'] for version in fields.get('fixVersions', [])]
            
            return JiraIssue(
                key=issue_data['key'],
                summary=fields.get('summary', ''),
                description=fields.get('description', ''),
                issue_type=fields['issuetype']['name'],
                status=fields['status']['name'],
                priority=fields['priority']['name'] if fields.get('priority') else 'Medium',
                assignee=assignee,
                reporter=reporter,
                created=created,
                updated=updated,
                resolved=resolved,
                project_key=fields['project']['key'],
                labels=labels,
                components=components,
                fix_versions=fix_versions
            )
        except Exception as e:
            logger.error(f"解析问题数据失败: {e}")
            return None
    
    async def get_issue_statistics(self, project_key: str, days: int = 30) -> Dict[str, Any]:
        """获取问题统计数据"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        issues = await self.get_issues(
            project_key=project_key,
            start_date=start_date,
            end_date=end_date
        )
        
        if not issues:
            return {}
        
        # 统计分析
        total_issues = len(issues)
        
        # 按类型统计
        type_stats = {}
        for issue in issues:
            type_stats[issue.issue_type] = type_stats.get(issue.issue_type, 0) + 1
        
        # 按状态统计
        status_stats = {}
        for issue in issues:
            status_stats[issue.status] = status_stats.get(issue.status, 0) + 1
        
        # 按优先级统计
        priority_stats = {}
        for issue in issues:
            priority_stats[issue.priority] = priority_stats.get(issue.priority, 0) + 1
        
        # 解决时间统计
        resolved_issues = [issue for issue in issues if issue.resolved]
        avg_resolution_time = 0
        if resolved_issues:
            resolution_times = [
                (issue.resolved - issue.created).total_seconds() / 3600  # 小时
                for issue in resolved_issues
            ]
            avg_resolution_time = sum(resolution_times) / len(resolution_times)
        
        # 趋势数据（按天）
        daily_stats = {}
        for issue in issues:
            date_key = issue.created.strftime('%Y-%m-%d')
            if date_key not in daily_stats:
                daily_stats[date_key] = {'created': 0, 'resolved': 0}
            daily_stats[date_key]['created'] += 1
            
            if issue.resolved and issue.resolved.date() >= start_date.date():
                resolved_date_key = issue.resolved.strftime('%Y-%m-%d')
                if resolved_date_key not in daily_stats:
                    daily_stats[resolved_date_key] = {'created': 0, 'resolved': 0}
                daily_stats[resolved_date_key]['resolved'] += 1
        
        return {
            'summary': {
                'total_issues': total_issues,
                'resolved_issues': len(resolved_issues),
                'open_issues': total_issues - len(resolved_issues),
                'resolution_rate': (len(resolved_issues) / total_issues) * 100 if total_issues > 0 else 0,
                'avg_resolution_time_hours': avg_resolution_time
            },
            'type_distribution': type_stats,
            'status_distribution': status_stats,
            'priority_distribution': priority_stats,
            'daily_trends': daily_stats,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            }
        }
    
    async def create_issue(
        self, 
        project_key: str, 
        summary: str, 
        description: str,
        issue_type: str = "Bug",
        priority: str = "Medium",
        assignee: Optional[str] = None
    ) -> Optional[str]:
        """创建问题"""
        try:
            issue_data = {
                "fields": {
                    "project": {"key": project_key},
                    "summary": summary,
                    "description": description,
                    "issuetype": {"name": issue_type},
                    "priority": {"name": priority}
                }
            }
            
            if assignee:
                issue_data["fields"]["assignee"] = {"name": assignee}
            
            url = f"{self.base_url}/rest/api/2/issue"
            async with self.session.post(url, json=issue_data) as response:
                if response.status == 201:
                    result = await response.json()
                    issue_key = result['key']
                    logger.info(f"成功创建问题: {issue_key}")
                    return issue_key
                else:
                    error_text = await response.text()
                    logger.error(f"创建问题失败，状态码: {response.status}, 错误: {error_text}")
                    return None
        except Exception as e:
            logger.error(f"创建问题异常: {e}")
            return None
    
    async def update_issue_status(self, issue_key: str, status: str) -> bool:
        """更新问题状态"""
        try:
            # 获取可用的状态转换
            url = f"{self.base_url}/rest/api/2/issue/{issue_key}/transitions"
            async with self.session.get(url) as response:
                if response.status == 200:
                    transitions = await response.json()
                    
                    # 查找目标状态的转换ID
                    transition_id = None
                    for transition in transitions['transitions']:
                        if transition['to']['name'].lower() == status.lower():
                            transition_id = transition['id']
                            break
                    
                    if not transition_id:
                        logger.error(f"未找到状态 '{status}' 的转换")
                        return False
                    
                    # 执行状态转换
                    transition_data = {
                        "transition": {"id": transition_id}
                    }
                    
                    async with self.session.post(url, json=transition_data) as trans_response:
                        if trans_response.status == 204:
                            logger.info(f"成功更新问题 {issue_key} 状态为 {status}")
                            return True
                        else:
                            logger.error(f"状态转换失败，状态码: {trans_response.status}")
                            return False
                else:
                    logger.error(f"获取状态转换失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"更新问题状态异常: {e}")
            return False


# 全局JIRA集成实例
jira_integration = JiraIntegration()


async def sync_jira_defects(project_key: str) -> Dict[str, Any]:
    """同步JIRA缺陷数据"""
    try:
        async with jira_integration as jira:
            # 测试连接
            if not await jira.test_connection():
                return {"success": False, "error": "JIRA连接失败"}

            # 获取最近30天的缺陷
            issues = await jira.get_issues(
                project_key=project_key,
                issue_types=["Bug", "Defect"],
                start_date=datetime.now() - timedelta(days=30)
            )

            # 获取统计数据
            stats = await jira.get_issue_statistics(project_key, days=30)

            return {
                "success": True,
                "data": {
                    "issues": [
                        {
                            "key": issue.key,
                            "summary": issue.summary,
                            "status": issue.status,
                            "priority": issue.priority,
                            "created": issue.created.isoformat(),
                            "assignee": issue.assignee
                        }
                        for issue in issues
                    ],
                    "statistics": stats
                }
            }
    except Exception as e:
        logger.error(f"同步JIRA缺陷数据失败: {e}")
        return {"success": False, "error": str(e)}
