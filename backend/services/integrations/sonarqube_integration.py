"""
SonarQube集成服务
"""

import aiohttp
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
import logging
from dataclasses import dataclass
from config.settings import Settings

logger = logging.getLogger(__name__)
settings = Settings()


@dataclass
class SonarProject:
    """SonarQube项目数据类"""
    key: str
    name: str
    qualifier: str
    visibility: str
    last_analysis_date: Optional[datetime]


@dataclass
class QualityGate:
    """质量门禁数据类"""
    status: str
    conditions: List[Dict[str, Any]]
    project_key: str
    analysis_id: str


@dataclass
class CodeMetrics:
    """代码指标数据类"""
    project_key: str
    lines_of_code: int
    coverage: float
    duplicated_lines_density: float
    bugs: int
    vulnerabilities: int
    code_smells: int
    security_hotspots: int
    technical_debt: str
    reliability_rating: str
    security_rating: str
    maintainability_rating: str


class SonarQubeIntegration:
    """SonarQube集成服务"""
    
    def __init__(self):
        self.base_url = settings.SONARQUBE_URL
        self.token = settings.SONARQUBE_TOKEN
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        headers = {
            'Authorization': f'Bearer {self.token}',
            'Content-Type': 'application/json'
        }
        
        self.session = aiohttp.ClientSession(
            headers=headers,
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def test_connection(self) -> bool:
        """测试SonarQube连接"""
        try:
            url = f"{self.base_url}/api/system/status"
            async with self.session.get(url) as response:
                if response.status == 200:
                    status = await response.json()
                    logger.info(f"SonarQube连接成功，状态: {status.get('status')}")
                    return True
                else:
                    logger.error(f"SonarQube连接失败，状态码: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"SonarQube连接测试失败: {e}")
            return False
    
    async def get_projects(self) -> List[SonarProject]:
        """获取项目列表"""
        try:
            url = f"{self.base_url}/api/projects/search"
            params = {'ps': 500}  # 每页500个项目
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    projects = []
                    
                    for project_data in data.get('components', []):
                        last_analysis = None
                        if project_data.get('lastAnalysisDate'):
                            last_analysis = datetime.fromisoformat(
                                project_data['lastAnalysisDate'].replace('Z', '+00:00')
                            )
                        
                        project = SonarProject(
                            key=project_data['key'],
                            name=project_data['name'],
                            qualifier=project_data['qualifier'],
                            visibility=project_data.get('visibility', 'public'),
                            last_analysis_date=last_analysis
                        )
                        projects.append(project)
                    
                    logger.info(f"获取到 {len(projects)} 个SonarQube项目")
                    return projects
                else:
                    logger.error(f"获取项目列表失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取项目列表异常: {e}")
            return []
    
    async def get_project_metrics(self, project_key: str) -> Optional[CodeMetrics]:
        """获取项目代码指标"""
        try:
            # 定义需要获取的指标
            metrics = [
                'ncloc',  # 代码行数
                'coverage',  # 覆盖率
                'duplicated_lines_density',  # 重复行密度
                'bugs',  # Bug数量
                'vulnerabilities',  # 漏洞数量
                'code_smells',  # 代码异味
                'security_hotspots',  # 安全热点
                'sqale_index',  # 技术债务
                'reliability_rating',  # 可靠性评级
                'security_rating',  # 安全性评级
                'sqale_rating'  # 可维护性评级
            ]
            
            url = f"{self.base_url}/api/measures/component"
            params = {
                'component': project_key,
                'metricKeys': ','.join(metrics)
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    component = data.get('component', {})
                    measures = component.get('measures', [])
                    
                    # 解析指标数据
                    metric_values = {}
                    for measure in measures:
                        metric_key = measure['metric']
                        value = measure.get('value', '0')
                        metric_values[metric_key] = value
                    
                    # 转换技术债务时间格式
                    technical_debt = metric_values.get('sqale_index', '0')
                    if technical_debt.isdigit():
                        debt_minutes = int(technical_debt)
                        debt_hours = debt_minutes // 60
                        debt_days = debt_hours // 8  # 假设每天8小时工作
                        if debt_days > 0:
                            technical_debt = f"{debt_days}天"
                        elif debt_hours > 0:
                            technical_debt = f"{debt_hours}小时"
                        else:
                            technical_debt = f"{debt_minutes}分钟"
                    
                    return CodeMetrics(
                        project_key=project_key,
                        lines_of_code=int(metric_values.get('ncloc', 0)),
                        coverage=float(metric_values.get('coverage', 0)),
                        duplicated_lines_density=float(metric_values.get('duplicated_lines_density', 0)),
                        bugs=int(metric_values.get('bugs', 0)),
                        vulnerabilities=int(metric_values.get('vulnerabilities', 0)),
                        code_smells=int(metric_values.get('code_smells', 0)),
                        security_hotspots=int(metric_values.get('security_hotspots', 0)),
                        technical_debt=technical_debt,
                        reliability_rating=metric_values.get('reliability_rating', 'A'),
                        security_rating=metric_values.get('security_rating', 'A'),
                        maintainability_rating=metric_values.get('sqale_rating', 'A')
                    )
                else:
                    logger.error(f"获取项目指标失败，状态码: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"获取项目指标异常: {e}")
            return None
    
    async def get_quality_gate_status(self, project_key: str) -> Optional[QualityGate]:
        """获取质量门禁状态"""
        try:
            url = f"{self.base_url}/api/qualitygates/project_status"
            params = {'projectKey': project_key}
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    project_status = data.get('projectStatus', {})
                    
                    return QualityGate(
                        status=project_status.get('status', 'NONE'),
                        conditions=project_status.get('conditions', []),
                        project_key=project_key,
                        analysis_id=project_status.get('analysisId', '')
                    )
                else:
                    logger.error(f"获取质量门禁状态失败，状态码: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"获取质量门禁状态异常: {e}")
            return None
    
    async def get_project_history(
        self, 
        project_key: str, 
        metrics: List[str],
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        """获取项目历史数据"""
        try:
            url = f"{self.base_url}/api/measures/search_history"
            params = {
                'component': project_key,
                'metrics': ','.join(metrics),
                'ps': 1000  # 每页1000条记录
            }
            
            if from_date:
                params['from'] = from_date.strftime('%Y-%m-%d')
            if to_date:
                params['to'] = to_date.strftime('%Y-%m-%d')
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    measures = data.get('measures', [])
                    
                    history_data = {}
                    for measure in measures:
                        metric_key = measure['metric']
                        history_data[metric_key] = []
                        
                        for history_point in measure.get('history', []):
                            history_data[metric_key].append({
                                'date': history_point['date'],
                                'value': history_point.get('value', '0')
                            })
                    
                    return history_data
                else:
                    logger.error(f"获取项目历史数据失败，状态码: {response.status}")
                    return {}
        except Exception as e:
            logger.error(f"获取项目历史数据异常: {e}")
            return {}
    
    async def get_issues(
        self, 
        project_key: str,
        types: Optional[List[str]] = None,
        severities: Optional[List[str]] = None,
        statuses: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """获取项目问题列表"""
        try:
            url = f"{self.base_url}/api/issues/search"
            params = {
                'componentKeys': project_key,
                'ps': 500  # 每页500个问题
            }
            
            if types:
                params['types'] = ','.join(types)
            if severities:
                params['severities'] = ','.join(severities)
            if statuses:
                params['statuses'] = ','.join(statuses)
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    issues = data.get('issues', [])
                    
                    return [
                        {
                            'key': issue['key'],
                            'rule': issue['rule'],
                            'severity': issue['severity'],
                            'component': issue['component'],
                            'line': issue.get('line'),
                            'message': issue['message'],
                            'status': issue['status'],
                            'type': issue['type'],
                            'creation_date': issue['creationDate'],
                            'update_date': issue['updateDate']
                        }
                        for issue in issues
                    ]
                else:
                    logger.error(f"获取项目问题失败，状态码: {response.status}")
                    return []
        except Exception as e:
            logger.error(f"获取项目问题异常: {e}")
            return []


# 全局SonarQube集成实例
sonarqube_integration = SonarQubeIntegration()


async def sync_sonarqube_metrics(project_key: str) -> Dict[str, Any]:
    """同步SonarQube指标数据"""
    try:
        async with sonarqube_integration as sonar:
            # 测试连接
            if not await sonar.test_connection():
                return {"success": False, "error": "SonarQube连接失败"}
            
            # 获取项目指标
            metrics = await sonar.get_project_metrics(project_key)
            if not metrics:
                return {"success": False, "error": "获取项目指标失败"}
            
            # 获取质量门禁状态
            quality_gate = await sonar.get_quality_gate_status(project_key)
            
            # 获取历史趋势数据（最近30天）
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            history = await sonar.get_project_history(
                project_key=project_key,
                metrics=['coverage', 'bugs', 'vulnerabilities', 'code_smells'],
                from_date=start_date,
                to_date=end_date
            )
            
            return {
                "success": True,
                "data": {
                    "metrics": {
                        "lines_of_code": metrics.lines_of_code,
                        "coverage": metrics.coverage,
                        "duplicated_lines_density": metrics.duplicated_lines_density,
                        "bugs": metrics.bugs,
                        "vulnerabilities": metrics.vulnerabilities,
                        "code_smells": metrics.code_smells,
                        "security_hotspots": metrics.security_hotspots,
                        "technical_debt": metrics.technical_debt,
                        "reliability_rating": metrics.reliability_rating,
                        "security_rating": metrics.security_rating,
                        "maintainability_rating": metrics.maintainability_rating
                    },
                    "quality_gate": {
                        "status": quality_gate.status if quality_gate else "NONE",
                        "conditions": quality_gate.conditions if quality_gate else []
                    },
                    "history": history
                }
            }
    except Exception as e:
        logger.error(f"同步SonarQube指标数据失败: {e}")
        return {"success": False, "error": str(e)}
