"""
数据库配置和连接管理
"""

import os
import redis.asyncio as redis
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from config.settings import Settings

# 获取配置
settings = Settings()

# 数据库配置
DATABASE_URL = settings.DATABASE_URL

# 创建异步数据库引擎
engine = create_async_engine(
    DATABASE_URL,
    echo=settings.DEBUG,  # 根据DEBUG配置显示SQL语句
    future=True,
    pool_size=20,  # 连接池大小
    max_overflow=30,  # 最大溢出连接数
    pool_pre_ping=True,  # 连接前ping检查
    pool_recycle=3600  # 连接回收时间（1小时）
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()

# Redis连接池
redis_pool = None

async def get_redis():
    """获取Redis连接"""
    global redis_pool
    if redis_pool is None:
        redis_pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_DB,
            decode_responses=True,
            max_connections=20
        )
    return redis.Redis(connection_pool=redis_pool)

# 依赖注入：获取数据库会话
async def get_db():
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# 初始化数据库
async def init_db():
    """初始化数据库表"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

# 关闭数据库连接
async def close_db():
    """关闭数据库连接"""
    global redis_pool
    await engine.dispose()
    if redis_pool:
        await redis_pool.disconnect()
