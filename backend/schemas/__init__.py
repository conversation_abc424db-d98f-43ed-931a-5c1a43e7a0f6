"""
Pydantic 响应模型包
"""

from .base import *
from .dashboard import *
from .automation import *
from .performance import *
from .quality_gate import *

__all__ = [
    # Base schemas
    "BaseResponse", "PaginatedResponse", "ErrorResponse",
    
    # Dashboard schemas
    "ProjectResponse", "TeamResponse", "MetricCardResponse", 
    "DashboardOverviewResponse", "TrendDataResponse",
    
    # Automation schemas
    "TestCaseResponse", "TestExecutionResponse", "AutomationOverviewResponse",
    
    # Performance schemas
    "PerformanceMetricResponse", "ServiceMetricResponse", "PerformanceOverviewResponse",
    
    # Quality Gate schemas
    "QualityGateRuleResponse", "QualityGateExecutionResponse", "QualityGateOverviewResponse"
]
