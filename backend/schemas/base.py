"""
基础响应模型
"""

from typing import Generic, TypeVar, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime

T = TypeVar('T')


class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    timestamp: datetime = Field(default_factory=datetime.now)


class DataResponse(BaseResponse, Generic[T]):
    """带数据的响应模型"""
    data: T


class PaginatedResponse(BaseResponse, Generic[T]):
    """分页响应模型"""
    data: List[T]
    pagination: dict = Field(
        description="分页信息",
        example={
            "page": 1,
            "page_size": 20,
            "total": 100,
            "total_pages": 5,
            "has_next": True,
            "has_prev": False
        }
    )


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    error_code: Optional[str] = None
    details: Optional[dict] = None
    timestamp: datetime = Field(default_factory=datetime.now)


class QueryParams(BaseModel):
    """查询参数基类"""
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    sort_by: Optional[str] = Field(None, description="排序字段")
    sort_order: Optional[str] = Field("desc", pattern="^(asc|desc)$", description="排序方向")


class DateRangeParams(BaseModel):
    """日期范围参数"""
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    period: Optional[str] = Field("7d", description="时间周期", pattern="^(1d|7d|30d|90d)$")
