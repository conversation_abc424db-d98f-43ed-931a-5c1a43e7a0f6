"""
报告生成API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from typing import List, Optional, Dict, Any
from datetime import datetime
import io

from database import get_db
from models.dashboard import Project
from services.report_service import ReportService

router = APIRouter(prefix="/api/reports", tags=["reports"])


@router.get("/quality/download")
async def download_quality_report(
    project_ids: str = Query(..., description="项目ID列表，逗号分隔"),
    date_range: str = Query("30d", description="日期范围"),
    format: str = Query("excel", description="报告格式"),
    include_sections: Optional[str] = Query(None, description="包含的部分，逗号分隔"),
    db: AsyncSession = Depends(get_db)
):
    """下载质量报告"""
    try:
        # 解析项目ID
        project_id_list = [int(pid.strip()) for pid in project_ids.split(',') if pid.strip()]
        
        if not project_id_list:
            raise HTTPException(status_code=400, detail="项目ID不能为空")

        # 验证项目是否存在
        projects_query = select(Project).where(Project.id.in_(project_id_list))
        projects_result = await db.execute(projects_query)
        existing_projects = projects_result.scalars().all()
        
        if len(existing_projects) != len(project_id_list):
            raise HTTPException(status_code=404, detail="部分项目不存在")

        # 解析包含的部分
        sections = None
        if include_sections:
            sections = [s.strip() for s in include_sections.split(',') if s.strip()]

        # 生成报告
        report_service = ReportService(db)
        report_data = await report_service.generate_quality_report(
            project_ids=project_id_list,
            date_range=date_range,
            format=format,
            include_sections=sections
        )

        # 设置文件名和MIME类型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format == "excel":
            filename = f"quality_report_{timestamp}.xlsx"
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif format == "csv":
            filename = f"quality_report_{timestamp}.csv"
            media_type = "text/csv"
        elif format == "pdf":
            filename = f"quality_report_{timestamp}.pdf"
            media_type = "application/pdf"
        else:
            raise HTTPException(status_code=400, detail="不支持的报告格式")

        # 返回文件流
        return StreamingResponse(
            io.BytesIO(report_data),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")


@router.post("/quality/generate")
async def generate_quality_report(
    background_tasks: BackgroundTasks,
    project_ids: List[int],
    date_range: str = "30d",
    format: str = "excel",
    include_sections: Optional[List[str]] = None,
    notification_email: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """异步生成质量报告"""
    try:
        # 验证项目是否存在
        projects_query = select(Project).where(Project.id.in_(project_ids))
        projects_result = await db.execute(projects_query)
        existing_projects = projects_result.scalars().all()
        
        if len(existing_projects) != len(project_ids):
            raise HTTPException(status_code=404, detail="部分项目不存在")

        # 在后台任务中生成报告
        background_tasks.add_task(
            _generate_report_background,
            db,
            project_ids,
            date_range,
            format,
            include_sections,
            notification_email
        )

        return {
            "success": True,
            "message": "报告生成任务已启动，完成后将通过邮件通知",
            "task_id": f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动报告生成失败: {str(e)}")


@router.get("/templates")
async def get_report_templates():
    """获取报告模板列表"""
    templates = [
        {
            "id": "quality_overview",
            "name": "质量概览报告",
            "description": "包含项目质量的全面概览，包括缺陷、覆盖率、预警等",
            "sections": ["overview", "defects", "coverage", "alerts"],
            "formats": ["excel", "csv", "pdf"]
        },
        {
            "id": "defect_analysis",
            "name": "缺陷分析报告",
            "description": "专注于缺陷数据的详细分析",
            "sections": ["overview", "defects", "trends"],
            "formats": ["excel", "csv"]
        },
        {
            "id": "coverage_report",
            "name": "测试覆盖率报告",
            "description": "测试覆盖率的详细统计和趋势分析",
            "sections": ["overview", "coverage", "trends"],
            "formats": ["excel", "csv"]
        },
        {
            "id": "alert_summary",
            "name": "预警汇总报告",
            "description": "预警数据的汇总和分析",
            "sections": ["overview", "alerts", "trends"],
            "formats": ["excel", "csv"]
        }
    ]

    return {
        "success": True,
        "data": {
            "templates": templates
        }
    }


@router.get("/stats")
async def get_report_stats(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    db: AsyncSession = Depends(get_db)
):
    """获取报告生成统计"""
    try:
        # 这里可以添加报告生成历史的统计
        # 暂时返回模拟数据
        stats = {
            "total_reports": 156,
            "reports_this_month": 23,
            "popular_formats": {
                "excel": 89,
                "csv": 45,
                "pdf": 22
            },
            "popular_sections": {
                "overview": 156,
                "defects": 134,
                "coverage": 123,
                "alerts": 98,
                "trends": 87
            },
            "recent_reports": [
                {
                    "id": 1,
                    "name": "质量概览报告_20250605",
                    "format": "excel",
                    "generated_at": "2025-06-05T10:30:00",
                    "project_count": 3,
                    "file_size": "2.3MB"
                },
                {
                    "id": 2,
                    "name": "缺陷分析报告_20250604",
                    "format": "csv",
                    "generated_at": "2025-06-04T15:45:00",
                    "project_count": 1,
                    "file_size": "856KB"
                }
            ]
        }

        return {
            "success": True,
            "data": stats
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取报告统计失败: {str(e)}")


@router.get("/formats")
async def get_supported_formats():
    """获取支持的报告格式"""
    formats = [
        {
            "id": "excel",
            "name": "Excel",
            "description": "Microsoft Excel格式，支持多个工作表",
            "extension": ".xlsx",
            "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "features": ["多工作表", "图表", "格式化"]
        },
        {
            "id": "csv",
            "name": "CSV",
            "description": "逗号分隔值格式，便于数据处理",
            "extension": ".csv",
            "mime_type": "text/csv",
            "features": ["轻量级", "通用性强", "易于处理"]
        },
        {
            "id": "pdf",
            "name": "PDF",
            "description": "便携式文档格式，适合打印和分享",
            "extension": ".pdf",
            "mime_type": "application/pdf",
            "features": ["格式固定", "易于分享", "专业外观"],
            "status": "开发中"
        }
    ]

    return {
        "success": True,
        "data": {
            "formats": formats
        }
    }


@router.get("/sections")
async def get_available_sections():
    """获取可用的报告部分"""
    sections = [
        {
            "id": "overview",
            "name": "项目概览",
            "description": "项目基本信息和总体统计",
            "required": True
        },
        {
            "id": "defects",
            "name": "缺陷分析",
            "description": "缺陷数据的详细统计和分析"
        },
        {
            "id": "coverage",
            "name": "测试覆盖率",
            "description": "测试覆盖率统计和趋势"
        },
        {
            "id": "alerts",
            "name": "质量预警",
            "description": "预警数据和处理情况"
        },
        {
            "id": "trends",
            "name": "趋势分析",
            "description": "各项指标的趋势分析"
        }
    ]

    return {
        "success": True,
        "data": {
            "sections": sections
        }
    }


async def _generate_report_background(
    db: AsyncSession,
    project_ids: List[int],
    date_range: str,
    format: str,
    include_sections: Optional[List[str]],
    notification_email: Optional[str]
):
    """后台生成报告任务"""
    try:
        report_service = ReportService(db)
        report_data = await report_service.generate_quality_report(
            project_ids=project_ids,
            date_range=date_range,
            format=format,
            include_sections=include_sections
        )

        # 这里可以保存报告到文件系统或云存储
        # 并发送邮件通知用户

        if notification_email:
            # 发送邮件通知
            pass

    except Exception as e:
        # 记录错误日志
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"后台生成报告失败: {e}")

        if notification_email:
            # 发送错误通知邮件
            pass
