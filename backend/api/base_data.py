"""
基础数据相关API端点
提供项目、团队等基础数据的CRUD操作
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload

# 导入数据库和模型
from database import get_db
from models.dashboard import Project, Team, ProjectStatus, QualityLevel

router = APIRouter(tags=["base-data"])

# 响应模型
class ProjectResponse(BaseModel):
    """项目响应模型"""
    id: int
    name: str
    description: Optional[str] = None
    status: str
    team_id: Optional[int] = None
    team_name: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TeamResponse(BaseModel):
    """团队响应模型"""
    id: int
    name: str
    description: Optional[str] = None
    lead_name: Optional[str] = None
    member_count: int
    interface_coverage: float
    main_path_coverage: float
    quality_gate_pass_rate: float
    avg_response_time: float
    quality_score: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PaginationResponse(BaseModel):
    """分页响应模型"""
    page: int
    page_size: int
    total: int
    total_pages: int
    has_next: bool
    has_prev: bool

# 生成模拟数据的函数
def generate_mock_projects():
    """生成模拟项目数据"""
    now = datetime.now()
    return [
        {
            "id": 1,
            "name": "用户中心",
            "description": "用户管理和认证系统",
            "status": "active",
            "team_id": 1,
            "team_name": "用户中心团队",
            "created_at": (now - timedelta(days=30)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 2,
            "name": "订单系统",
            "description": "电商订单处理系统",
            "status": "active",
            "team_id": 2,
            "team_name": "订单团队",
            "created_at": (now - timedelta(days=25)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 3,
            "name": "支付系统",
            "description": "支付网关和处理系统",
            "status": "active",
            "team_id": 3,
            "team_name": "支付团队",
            "created_at": (now - timedelta(days=20)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 4,
            "name": "库存管理",
            "description": "商品库存管理系统",
            "status": "active",
            "team_id": 2,
            "team_name": "订单团队",
            "created_at": (now - timedelta(days=15)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 5,
            "name": "推荐引擎",
            "description": "商品推荐算法系统",
            "status": "inactive",
            "team_id": 4,
            "team_name": "算法团队",
            "created_at": (now - timedelta(days=10)).isoformat(),
            "updated_at": now.isoformat()
        }
    ]

def generate_mock_teams():
    """生成模拟团队数据"""
    now = datetime.now()
    return [
        {
            "id": 1,
            "name": "用户中心团队",
            "description": "负责用户管理和认证相关功能",
            "lead_name": "张三",
            "member_count": 8,
            "interface_coverage": 85.0,
            "main_path_coverage": 95.0,
            "quality_gate_pass_rate": 98.0,
            "avg_response_time": 95.0,
            "quality_score": "A",
            "created_at": (now - timedelta(days=60)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 2,
            "name": "订单团队",
            "description": "负责订单和库存管理功能",
            "lead_name": "李四",
            "member_count": 12,
            "interface_coverage": 75.0,
            "main_path_coverage": 90.0,
            "quality_gate_pass_rate": 95.0,
            "avg_response_time": 145.0,
            "quality_score": "B",
            "created_at": (now - timedelta(days=55)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 3,
            "name": "支付团队",
            "description": "负责支付网关和金融相关功能",
            "lead_name": "王五",
            "member_count": 6,
            "interface_coverage": 82.0,
            "main_path_coverage": 100.0,
            "quality_gate_pass_rate": 100.0,
            "avg_response_time": 110.0,
            "quality_score": "A",
            "created_at": (now - timedelta(days=50)).isoformat(),
            "updated_at": now.isoformat()
        },
        {
            "id": 4,
            "name": "算法团队",
            "description": "负责推荐算法和数据分析",
            "lead_name": "赵六",
            "member_count": 5,
            "interface_coverage": 65.0,
            "main_path_coverage": 80.0,
            "quality_gate_pass_rate": 85.0,
            "avg_response_time": 200.0,
            "quality_score": "C",
            "created_at": (now - timedelta(days=45)).isoformat(),
            "updated_at": now.isoformat()
        }
    ]

@router.get("/api/projects")
async def get_projects(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量", alias="page_size"),
    sortBy: str = Query("created_at", description="排序字段", alias="sort_by"),
    sortOrder: str = Query("desc", regex="^(asc|desc)$", description="排序方向", alias="sort_order"),
    status: Optional[str] = Query(None, description="项目状态筛选"),
    team_id: Optional[int] = Query(None, description="团队ID筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    dateRange: Optional[str] = Query(None, description="日期范围"),
    db: AsyncSession = Depends(get_db)
):
    """获取项目列表"""
    try:
        # 暂时使用模拟数据，后续可以替换为真实数据库查询
        all_projects = generate_mock_projects()
        
        # 筛选逻辑
        filtered_projects = all_projects
        
        if status:
            filtered_projects = [p for p in filtered_projects if p["status"] == status]
        
        if team_id:
            filtered_projects = [p for p in filtered_projects if p["team_id"] == team_id]
        
        if search:
            search_lower = search.lower()
            filtered_projects = [
                p for p in filtered_projects 
                if search_lower in p["name"].lower() or 
                   (p["description"] and search_lower in p["description"].lower())
            ]
        
        # 排序逻辑
        reverse = sortOrder == "desc"
        if sortBy in ["name", "status", "created_at", "updated_at"]:
            filtered_projects.sort(
                key=lambda x: x.get(sortBy, ""),
                reverse=reverse
            )

        # 分页逻辑
        total = len(filtered_projects)
        start_idx = (page - 1) * pageSize
        end_idx = start_idx + pageSize
        paginated_projects = filtered_projects[start_idx:end_idx]

        # 分页信息
        total_pages = (total + pageSize - 1) // pageSize
        pagination = PaginationResponse(
            page=page,
            page_size=pageSize,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        return JSONResponse(content={
            "success": True,
            "data": paginated_projects,
            "pagination": pagination.dict(),
            "last_updated": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目列表失败: {str(e)}")

@router.get("/api/teams")
async def get_teams(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量", alias="page_size"),
    sortBy: str = Query("created_at", description="排序字段", alias="sort_by"),
    sortOrder: str = Query("desc", regex="^(asc|desc)$", description="排序方向", alias="sort_order"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    dateRange: Optional[str] = Query(None, description="日期范围"),
    db: AsyncSession = Depends(get_db)
):
    """获取团队列表"""
    try:
        # 暂时使用模拟数据，后续可以替换为真实数据库查询
        all_teams = generate_mock_teams()
        
        # 搜索筛选
        filtered_teams = all_teams
        if search:
            search_lower = search.lower()
            filtered_teams = [
                t for t in filtered_teams 
                if search_lower in t["name"].lower() or 
                   (t["description"] and search_lower in t["description"].lower()) or
                   (t["lead_name"] and search_lower in t["lead_name"].lower())
            ]
        
        # 排序逻辑
        reverse = sortOrder == "desc"
        if sortBy in ["name", "lead_name", "member_count", "quality_score", "created_at"]:
            filtered_teams.sort(
                key=lambda x: x.get(sortBy, ""),
                reverse=reverse
            )

        # 分页逻辑
        total = len(filtered_teams)
        start_idx = (page - 1) * pageSize
        end_idx = start_idx + pageSize
        paginated_teams = filtered_teams[start_idx:end_idx]

        # 分页信息
        total_pages = (total + pageSize - 1) // pageSize
        pagination = PaginationResponse(
            page=page,
            page_size=pageSize,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        return JSONResponse(content={
            "success": True,
            "data": paginated_teams,
            "pagination": pagination.dict(),
            "last_updated": datetime.now().isoformat()
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取团队列表失败: {str(e)}")

@router.get("/api/projects/{project_id}")
async def get_project(
    project_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取单个项目详情"""
    try:
        # 暂时使用模拟数据
        projects = generate_mock_projects()
        project = next((p for p in projects if p["id"] == project_id), None)
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        return JSONResponse(content={
            "success": True,
            "data": project
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取项目详情失败: {str(e)}")

@router.get("/api/teams/{team_id}")
async def get_team(
    team_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取单个团队详情"""
    try:
        # 暂时使用模拟数据
        teams = generate_mock_teams()
        team = next((t for t in teams if t["id"] == team_id), None)
        
        if not team:
            raise HTTPException(status_code=404, detail="团队不存在")
        
        return JSONResponse(content={
            "success": True,
            "data": team
        })
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取团队详情失败: {str(e)}")

# 其他缺失的API端点

@router.get("/api/test-cases")
async def get_test_cases(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    test_type: Optional[str] = Query(None, description="测试类型筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取测试用例列表"""
    try:
        # 模拟测试用例数据
        mock_test_cases = [
            {
                "id": 1,
                "name": "用户登录接口测试",
                "description": "测试用户登录功能的各种场景",
                "test_type": "api",
                "status": "active",
                "project_id": 1,
                "is_automated": True,
                "priority": "high",
                "created_at": datetime.now() - timedelta(days=10),
                "updated_at": datetime.now()
            },
            {
                "id": 2,
                "name": "订单创建流程测试",
                "description": "测试订单创建的完整流程",
                "test_type": "e2e",
                "status": "active",
                "project_id": 2,
                "is_automated": True,
                "priority": "high",
                "created_at": datetime.now() - timedelta(days=8),
                "updated_at": datetime.now()
            },
            {
                "id": 3,
                "name": "支付接口测试",
                "description": "测试支付相关接口",
                "test_type": "api",
                "status": "active",
                "project_id": 3,
                "is_automated": False,
                "priority": "medium",
                "created_at": datetime.now() - timedelta(days=5),
                "updated_at": datetime.now()
            }
        ]

        # 应用筛选
        filtered_cases = mock_test_cases
        if test_type:
            filtered_cases = [tc for tc in filtered_cases if tc["test_type"] == test_type]
        if status:
            filtered_cases = [tc for tc in filtered_cases if tc["status"] == status]
        if project_id:
            filtered_cases = [tc for tc in filtered_cases if tc["project_id"] == project_id]

        # 分页
        total = len(filtered_cases)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_cases = filtered_cases[start_idx:end_idx]

        return JSONResponse(content={
            "success": True,
            "data": paginated_cases,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "total_pages": (total + page_size - 1) // page_size
            }
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试用例失败: {str(e)}")

@router.get("/api/test-executions")
async def get_test_executions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    sort_by: str = Query("start_time", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    status: Optional[str] = Query(None, description="执行状态筛选"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取测试执行记录"""
    try:
        # 模拟测试执行数据
        mock_executions = [
            {
                "id": 1,
                "test_case_id": 1,
                "execution_id": "exec_001",
                "status": "passed",
                "start_time": datetime.now() - timedelta(hours=2),
                "end_time": datetime.now() - timedelta(hours=1, minutes=45),
                "duration": 900.0,
                "environment": "test",
                "executor": "自动化系统"
            },
            {
                "id": 2,
                "test_case_id": 2,
                "execution_id": "exec_002",
                "status": "failed",
                "start_time": datetime.now() - timedelta(hours=1),
                "end_time": datetime.now() - timedelta(minutes=30),
                "duration": 1800.0,
                "environment": "test",
                "executor": "自动化系统"
            }
        ]

        # 应用筛选
        filtered_executions = mock_executions
        if status:
            filtered_executions = [ex for ex in filtered_executions if ex["status"] == status]

        # 分页
        total = len(filtered_executions)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paginated_executions = filtered_executions[start_idx:end_idx]

        return JSONResponse(content={
            "success": True,
            "data": paginated_executions,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "total_pages": (total + page_size - 1) // page_size
            }
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取测试执行记录失败: {str(e)}")

@router.get("/api/automation-metrics")
async def get_automation_metrics(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    metric_name: Optional[str] = Query(None, description="指标名称筛选"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取自动化测试指标"""
    try:
        # 模拟自动化指标数据
        mock_metrics = [
            {
                "id": 1,
                "project_id": 1,
                "metric_name": "接口自动化覆盖率",
                "metric_value": 78.0,
                "metric_unit": "%",
                "status": "good",
                "trend": "up",
                "record_date": datetime.now()
            },
            {
                "id": 2,
                "project_id": 1,
                "metric_name": "自动化执行成功率",
                "metric_value": 96.0,
                "metric_unit": "%",
                "status": "excellent",
                "trend": "stable",
                "record_date": datetime.now()
            }
        ]

        # 应用筛选
        filtered_metrics = mock_metrics
        if metric_name:
            filtered_metrics = [m for m in filtered_metrics if metric_name.lower() in m["metric_name"].lower()]
        if project_id:
            filtered_metrics = [m for m in filtered_metrics if m["project_id"] == project_id]

        return JSONResponse(content={
            "success": True,
            "data": filtered_metrics
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取自动化指标失败: {str(e)}")

@router.get("/api/performance-metrics")
async def get_performance_metrics(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    metric_type: Optional[str] = Query(None, description="指标类型筛选"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取性能指标数据"""
    try:
        # 模拟性能指标数据
        mock_metrics = [
            {
                "id": 1,
                "project_id": 1,
                "metric_name": "平均响应时间",
                "metric_value": 125.0,
                "metric_unit": "ms",
                "status": "good",
                "trend": "down",
                "record_time": datetime.now()
            },
            {
                "id": 2,
                "project_id": 1,
                "metric_name": "系统吞吐量",
                "metric_value": 1250.0,
                "metric_unit": "req/s",
                "status": "excellent",
                "trend": "up",
                "record_time": datetime.now()
            }
        ]

        # 应用筛选
        filtered_metrics = mock_metrics
        if project_id:
            filtered_metrics = [m for m in filtered_metrics if m["project_id"] == project_id]

        return JSONResponse(content={
            "success": True,
            "data": filtered_metrics
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能指标失败: {str(e)}")

@router.get("/api/service-metrics")
async def get_service_metrics(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    service_name: Optional[str] = Query(None, description="服务名称筛选"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取服务性能指标"""
    try:
        # 模拟服务指标数据
        mock_service_metrics = [
            {
                "id": 1,
                "service_name": "用户服务",
                "project_id": 1,
                "avg_response_time": 95.0,
                "p95_response_time": 180.0,
                "p99_response_time": 250.0,
                "throughput": 800.0,
                "error_rate": 0.05,
                "availability": 99.95,
                "record_time": datetime.now()
            },
            {
                "id": 2,
                "service_name": "订单服务",
                "project_id": 2,
                "avg_response_time": 145.0,
                "p95_response_time": 280.0,
                "p99_response_time": 400.0,
                "throughput": 600.0,
                "error_rate": 0.12,
                "availability": 99.90,
                "record_time": datetime.now()
            }
        ]

        # 应用筛选
        filtered_metrics = mock_service_metrics
        if service_name:
            filtered_metrics = [m for m in filtered_metrics if service_name.lower() in m["service_name"].lower()]
        if project_id:
            filtered_metrics = [m for m in filtered_metrics if m["project_id"] == project_id]

        return JSONResponse(content={
            "success": True,
            "data": filtered_metrics
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务指标失败: {str(e)}")

@router.get("/api/system-metrics")
async def get_system_metrics(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    host_name: Optional[str] = Query(None, description="主机名筛选"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取系统性能指标"""
    try:
        # 模拟系统指标数据
        mock_system_metrics = [
            {
                "id": 1,
                "host_name": "web-server-01",
                "project_id": 1,
                "cpu_usage": 65.0,
                "memory_usage": 72.0,
                "disk_usage": 45.0,
                "network_in": 50.0,
                "network_out": 30.0,
                "record_time": datetime.now()
            },
            {
                "id": 2,
                "host_name": "web-server-02",
                "project_id": 1,
                "cpu_usage": 58.0,
                "memory_usage": 68.0,
                "disk_usage": 42.0,
                "network_in": 45.0,
                "network_out": 28.0,
                "record_time": datetime.now()
            }
        ]

        # 应用筛选
        filtered_metrics = mock_system_metrics
        if host_name:
            filtered_metrics = [m for m in filtered_metrics if host_name.lower() in m["host_name"].lower()]
        if project_id:
            filtered_metrics = [m for m in filtered_metrics if m["project_id"] == project_id]

        return JSONResponse(content={
            "success": True,
            "data": filtered_metrics
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")

@router.get("/api/quality-gate-executions")
async def get_quality_gate_executions(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    project_id: Optional[int] = Query(None, description="项目ID筛选"),
    status: Optional[str] = Query(None, description="执行状态筛选"),
    db: AsyncSession = Depends(get_db)
):
    """获取质量门禁执行记录"""
    try:
        # 模拟质量门禁执行数据
        mock_executions = [
            {
                "id": 1,
                "project_id": 1,
                "execution_id": "qg_exec_001",
                "overall_status": "passed",
                "overall_score": 95.0,
                "total_rules": 10,
                "passed_rules": 9,
                "failed_rules": 1,
                "execution_time": datetime.now() - timedelta(hours=1),
                "environment": "prod"
            },
            {
                "id": 2,
                "project_id": 2,
                "execution_id": "qg_exec_002",
                "overall_status": "failed",
                "overall_score": 75.0,
                "total_rules": 8,
                "passed_rules": 6,
                "failed_rules": 2,
                "execution_time": datetime.now() - timedelta(hours=2),
                "environment": "test"
            }
        ]

        # 应用筛选
        filtered_executions = mock_executions
        if project_id:
            filtered_executions = [ex for ex in filtered_executions if ex["project_id"] == project_id]
        if status:
            filtered_executions = [ex for ex in filtered_executions if ex["overall_status"] == status]

        return JSONResponse(content={
            "success": True,
            "data": filtered_executions
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取质量门禁执行记录失败: {str(e)}")
