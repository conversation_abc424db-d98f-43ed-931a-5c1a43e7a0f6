"""
测试覆盖率API接口
提供覆盖率数据的查询、统计和分析功能
"""

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, asc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import json

from database import get_db
from models.coverage import (
    CoverageMetric, FileCoverage, CoverageTarget,
    CoverageType, CoverageSource, CoverageLevel,
    CoverageMetricResponse, CoverageTrendData, 
    CoverageDistributionData, CoverageStatsData
)
from models.dashboard import Project

router = APIRouter(prefix="/api/coverage", tags=["coverage"])


@router.get("/", response_model=List[CoverageMetricResponse])
async def get_coverage_metrics(
    db: AsyncSession = Depends(get_db),
    project_id: Optional[int] = Query(None, description="项目ID"),
    branch_name: Optional[str] = Query(None, description="分支名称"),
    source: Optional[str] = Query(None, description="数据来源"),
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量", alias="page_size"),
    sortBy: str = Query("measurement_date", description="排序字段", alias="sort_by"),
    sortOrder: str = Query("desc", description="排序方向", alias="sort_order")
):
    """获取覆盖率指标列表"""
    
    # 构建查询条件
    conditions = []
    if project_id:
        conditions.append(CoverageMetric.project_id == project_id)
    if branch_name:
        conditions.append(CoverageMetric.branch_name == branch_name)
    if source:
        conditions.append(CoverageMetric.source == source)
    
    # 构建查询 - 移除 selectinload 以避免 Project 枚举问题
    query = select(CoverageMetric)

    if conditions:
        query = query.where(and_(*conditions))

    # 排序
    sort_column = getattr(CoverageMetric, sortBy, CoverageMetric.measurement_date)
    if sortOrder.lower() == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))

    # 分页
    offset = (page - 1) * pageSize
    query = query.offset(offset).limit(pageSize)

    # 执行查询
    result = await db.execute(query)
    metrics = result.scalars().all()
    
    # 转换为响应模型
    response_data = []
    for metric in metrics:
        response_data.append(CoverageMetricResponse(
            id=metric.id,
            project_id=metric.project_id,
            branch_name=metric.branch_name,
            line_coverage=metric.line_coverage,
            branch_coverage=metric.branch_coverage,
            function_coverage=metric.function_coverage,
            overall_coverage=metric.overall_coverage,
            coverage_level=metric.coverage_level.value,
            measurement_date=metric.measurement_date,
            source=metric.source
        ))
    
    return response_data


@router.get("/trends")
async def get_coverage_trends(
    db: AsyncSession = Depends(get_db),
    project_id: Optional[int] = Query(None, description="项目ID"),
    branch_name: str = Query("main", description="分支名称"),
    date_range: str = Query("30d", description="时间范围"),
    group_by: str = Query("day", description="分组方式"),
    coverage_type: str = Query("line", description="覆盖率类型")
):
    """获取覆盖率趋势数据"""
    
    # 解析时间范围
    range_map = {
        "7d": 7, "30d": 30, "90d": 90, "1y": 365
    }
    days = range_map.get(date_range, 30)
    start_date = datetime.now() - timedelta(days=days)
    
    # 构建查询条件
    conditions = [
        CoverageMetric.measurement_date >= start_date,
        CoverageMetric.branch_name == branch_name
    ]
    if project_id:
        conditions.append(CoverageMetric.project_id == project_id)
    
    # 构建查询 - 移除 selectinload 以避免 Project 枚举问题
    query = select(CoverageMetric)
    query = query.where(and_(*conditions))
    query = query.order_by(CoverageMetric.measurement_date)

    # 执行查询
    result = await db.execute(query)
    metrics = result.scalars().all()

    # 获取项目名称映射（避免枚举问题）
    project_names = {}
    if metrics:
        project_ids = list(set(m.project_id for m in metrics))
        project_query = select(Project.id, Project.name).where(Project.id.in_(project_ids))
        project_result = await db.execute(project_query)
        project_names = {pid: name for pid, name in project_result.fetchall()}

    # 生成趋势数据
    trends = []
    for metric in metrics:
        trend_data = CoverageTrendData(
            date=metric.measurement_date.strftime("%Y-%m-%d"),
            line_coverage=metric.line_coverage,
            branch_coverage=metric.branch_coverage,
            function_coverage=metric.function_coverage,
            overall_coverage=metric.overall_coverage,
            project_name=project_names.get(metric.project_id, f"Project {metric.project_id}")
        )
        trends.append(trend_data)
    
    return {
        "success": True,
        "data": {
            "trends": [trend.dict() for trend in trends],
            "date_range": date_range,
            "group_by": group_by,
            "coverage_type": coverage_type,
            "total_points": len(trends)
        }
    }


@router.get("/distribution")
async def get_coverage_distribution(
    db: AsyncSession = Depends(get_db),
    project_id: Optional[int] = Query(None, description="项目ID"),
    dimension: str = Query("level", description="分布维度"),
    branch_name: str = Query("main", description="分支名称")
):
    """获取覆盖率分布统计"""
    
    # 构建查询条件
    conditions = [CoverageMetric.branch_name == branch_name]
    if project_id:
        conditions.append(CoverageMetric.project_id == project_id)
    
    # 获取最新的覆盖率数据
    subquery = select(
        CoverageMetric.project_id,
        func.max(CoverageMetric.measurement_date).label("latest_date")
    ).where(and_(*conditions)).group_by(CoverageMetric.project_id).subquery()
    
    query = select(CoverageMetric)
    query = query.join(
        subquery,
        and_(
            CoverageMetric.project_id == subquery.c.project_id,
            CoverageMetric.measurement_date == subquery.c.latest_date
        )
    )

    # 执行查询
    result = await db.execute(query)
    metrics = result.scalars().all()

    # 获取项目名称映射（避免枚举问题）
    project_names = {}
    if metrics:
        project_ids = list(set(m.project_id for m in metrics))
        project_query = select(Project.id, Project.name).where(Project.id.in_(project_ids))
        project_result = await db.execute(project_query)
        project_names = {pid: name for pid, name in project_result.fetchall()}

    # 按维度分组统计
    if dimension == "level":
        # 按覆盖率等级分布
        level_counts = {}
        level_projects = {}

        for metric in metrics:
            level = metric.coverage_level.value
            level_counts[level] = level_counts.get(level, 0) + 1
            if level not in level_projects:
                level_projects[level] = []
            level_projects[level].append(project_names.get(metric.project_id, f"Project {metric.project_id}"))
        
        total = len(metrics)
        distribution = []
        
        for level in CoverageLevel:
            count = level_counts.get(level.value, 0)
            percentage = (count / total * 100) if total > 0 else 0
            
            distribution.append(CoverageDistributionData(
                level=level.value,
                count=count,
                percentage=round(percentage, 1),
                projects=level_projects.get(level.value, [])
            ))
    
    elif dimension == "source":
        # 按数据来源分布
        source_counts = {}
        source_projects = {}
        
        for metric in metrics:
            source = metric.source
            source_counts[source] = source_counts.get(source, 0) + 1
            if source not in source_projects:
                source_projects[source] = []
            source_projects[source].append(project_names.get(metric.project_id, f"Project {metric.project_id}"))
        
        total = len(metrics)
        distribution = []
        
        for source, count in source_counts.items():
            percentage = (count / total * 100) if total > 0 else 0
            
            distribution.append(CoverageDistributionData(
                level=source,
                count=count,
                percentage=round(percentage, 1),
                projects=source_projects[source]
            ))
    
    else:
        raise HTTPException(status_code=400, detail=f"Unsupported dimension: {dimension}")
    
    return {
        "success": True,
        "data": {
            "distribution": [item.dict() for item in distribution],
            "dimension": dimension,
            "total_projects": len(metrics)
        }
    }


@router.get("/stats")
async def get_coverage_stats(
    db: AsyncSession = Depends(get_db),
    project_id: Optional[int] = Query(None, description="项目ID"),
    branch_name: str = Query("main", description="分支名称")
):
    """获取覆盖率统计概览"""
    
    # 构建查询条件
    conditions = [CoverageMetric.branch_name == branch_name]
    if project_id:
        conditions.append(CoverageMetric.project_id == project_id)
    
    # 获取最新的覆盖率数据
    subquery = select(
        CoverageMetric.project_id,
        func.max(CoverageMetric.measurement_date).label("latest_date")
    ).where(and_(*conditions)).group_by(CoverageMetric.project_id).subquery()
    
    query = select(CoverageMetric).join(
        subquery,
        and_(
            CoverageMetric.project_id == subquery.c.project_id,
            CoverageMetric.measurement_date == subquery.c.latest_date
        )
    )
    
    # 执行查询
    result = await db.execute(query)
    metrics = result.scalars().all()
    
    if not metrics:
        return {
            "success": True,
            "data": CoverageStatsData(
                total_projects=0,
                average_line_coverage=0.0,
                average_branch_coverage=0.0,
                average_function_coverage=0.0,
                excellent_projects=0,
                good_projects=0,
                fair_projects=0,
                poor_projects=0,
                critical_projects=0
            ).dict()
        }
    
    # 计算统计数据
    total_projects = len(metrics)
    total_line_coverage = sum(m.line_coverage for m in metrics)
    total_branch_coverage = sum(m.branch_coverage for m in metrics)
    total_function_coverage = sum(m.function_coverage for m in metrics)
    
    # 按等级统计
    level_counts = {level.value: 0 for level in CoverageLevel}
    latest_date = None
    
    for metric in metrics:
        level_counts[metric.coverage_level.value] += 1
        if latest_date is None or metric.measurement_date > latest_date:
            latest_date = metric.measurement_date
    
    stats = CoverageStatsData(
        total_projects=total_projects,
        average_line_coverage=round(total_line_coverage / total_projects, 1),
        average_branch_coverage=round(total_branch_coverage / total_projects, 1),
        average_function_coverage=round(total_function_coverage / total_projects, 1),
        excellent_projects=level_counts[CoverageLevel.EXCELLENT.value],
        good_projects=level_counts[CoverageLevel.GOOD.value],
        fair_projects=level_counts[CoverageLevel.FAIR.value],
        poor_projects=level_counts[CoverageLevel.POOR.value],
        critical_projects=level_counts[CoverageLevel.CRITICAL.value],
        latest_measurement_date=latest_date
    )
    
    return {
        "success": True,
        "data": stats.dict()
    }


@router.get("/files/{metric_id}")
async def get_file_coverage(
    metric_id: int,
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量", alias="page_size"),
    sortBy: str = Query("line_coverage", description="排序字段", alias="sort_by"),
    sortOrder: str = Query("asc", description="排序方向", alias="sort_order")
):
    """获取指定覆盖率指标的文件覆盖率详情"""
    
    # 验证覆盖率指标是否存在
    metric_query = select(CoverageMetric).where(CoverageMetric.id == metric_id)
    metric_result = await db.execute(metric_query)
    metric = metric_result.scalar_one_or_none()
    
    if not metric:
        raise HTTPException(status_code=404, detail="Coverage metric not found")
    
    # 构建文件覆盖率查询
    query = select(FileCoverage).where(FileCoverage.coverage_metric_id == metric_id)
    
    # 排序
    sort_column = getattr(FileCoverage, sortBy, FileCoverage.line_coverage)
    if sortOrder.lower() == "desc":
        query = query.order_by(desc(sort_column))
    else:
        query = query.order_by(asc(sort_column))
    
    # 分页
    offset = (page - 1) * pageSize
    query = query.offset(offset).limit(pageSize)
    
    # 执行查询
    result = await db.execute(query)
    file_coverages = result.scalars().all()
    
    # 转换为响应数据
    files_data = []
    for file_cov in file_coverages:
        files_data.append({
            "id": file_cov.id,
            "file_path": file_cov.file_path,
            "file_name": file_cov.file_name,
            "package_name": file_cov.package_name,
            "line_coverage": file_cov.line_coverage,
            "branch_coverage": file_cov.branch_coverage,
            "function_coverage": file_cov.function_coverage,
            "total_lines": file_cov.total_lines,
            "covered_lines": file_cov.covered_lines,
            "coverage_level": file_cov.coverage_level.value,
            "uncovered_lines": file_cov.uncovered_lines
        })
    
    return {
        "success": True,
        "data": {
            "files": files_data,
            "metric_info": {
                "id": metric.id,
                "project_id": metric.project_id,
                "branch_name": metric.branch_name,
                "measurement_date": metric.measurement_date.isoformat(),
                "overall_coverage": metric.overall_coverage
            },
            "pagination": {
                "page": page,
                "pageSize": pageSize,
                "total": len(files_data)
            }
        }
    }
