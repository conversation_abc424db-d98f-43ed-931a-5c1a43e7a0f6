"""
缺陷管理相关API端点
提供缺陷数据的CRUD操作、趋势分析和统计功能
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc, asc, case
from sqlalchemy.orm import selectinload

# 导入数据库和模型
from database import get_db
from models.defect import Defect, DefectSeverity, DefectPriority, DefectStatus, DefectType
from models.dashboard import User

router = APIRouter(prefix="/api/defects", tags=["defects"])

# 响应模型
class DefectResponse(BaseModel):
    """缺陷响应模型"""
    id: int
    title: str
    description: Optional[str] = None
    severity: str
    priority: str
    status: str
    defect_type: str
    project_id: int
    assignee_id: Optional[int] = None
    reporter_id: Optional[int] = None
    found_date: datetime
    resolved_date: Optional[datetime] = None
    environment: Optional[str] = None
    version: Optional[str] = None
    resolution_time_days: Optional[int] = None
    is_overdue: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DefectTrendResponse(BaseModel):
    """缺陷趋势响应模型"""
    date: str
    total_count: int
    critical_count: int
    high_count: int
    medium_count: int
    low_count: int
    resolved_count: int

class DefectDistributionResponse(BaseModel):
    """缺陷分布响应模型"""
    label: str
    count: int
    percentage: float

class DefectStatsResponse(BaseModel):
    """缺陷统计响应模型"""
    total_defects: int
    open_defects: int
    resolved_defects: int
    overdue_defects: int
    avg_resolution_time: float
    critical_defects: int
    high_defects: int

# API端点
@router.get("/", response_model=List[DefectResponse])
async def get_defects(
    page: int = Query(1, ge=1, description="页码"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量", alias="page_size"),
    sortBy: str = Query("found_date", description="排序字段", alias="sort_by"),
    sortOrder: str = Query("desc", description="排序方向", alias="sort_order"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    severity: Optional[str] = Query(None, description="严重程度"),
    status: Optional[str] = Query(None, description="状态"),
    assignee_id: Optional[int] = Query(None, description="处理人ID"),
    dateRange: Optional[str] = Query(None, description="日期范围", alias="date_range"),
    db: AsyncSession = Depends(get_db)
):
    """获取缺陷列表"""
    try:
        # 构建查询
        query = select(Defect)
        
        # 添加筛选条件
        if project_id:
            query = query.where(Defect.project_id == project_id)
        
        if severity:
            query = query.where(Defect.severity == DefectSeverity(severity))
        
        if status:
            query = query.where(Defect.status == DefectStatus(status))
        
        if assignee_id:
            query = query.where(Defect.assignee_id == assignee_id)
        
        # 日期范围筛选
        if dateRange:
            end_date = datetime.now()
            days_map = {"7d": 7, "30d": 30, "90d": 90, "1y": 365}
            days = days_map.get(dateRange, 30)
            start_date = end_date - timedelta(days=days)
            query = query.where(Defect.found_date >= start_date)
        
        # 排序
        sort_column = getattr(Defect, sortBy, Defect.found_date)
        if sortOrder.lower() == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # 分页
        offset = (page - 1) * pageSize
        query = query.offset(offset).limit(pageSize)
        
        # 执行查询
        result = await db.execute(query)
        defects = result.scalars().all()
        
        # 转换为响应模型
        defect_responses = []
        for defect in defects:
            defect_responses.append(DefectResponse(
                id=defect.id,
                title=defect.title,
                description=defect.description,
                severity=defect.severity.value,
                priority=defect.priority.value,
                status=defect.status.value,
                defect_type=defect.defect_type.value,
                project_id=defect.project_id,
                assignee_id=defect.assignee_id,
                reporter_id=defect.reporter_id,
                found_date=defect.found_date,
                resolved_date=defect.resolved_date,
                environment=defect.environment,
                version=defect.version,
                resolution_time_days=defect.resolution_time_days,
                is_overdue=defect.is_overdue,
                created_at=defect.created_at,
                updated_at=defect.updated_at
            ))
        
        return defect_responses
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缺陷列表失败: {str(e)}")

@router.get("/trends")
async def get_defect_trends(
    project_id: Optional[int] = Query(None, description="项目ID"),
    date_range: str = Query("30d", regex="^(7d|30d|90d|1y)$", description="日期范围"),
    group_by: str = Query("day", regex="^(day|week|month)$", description="分组方式"),
    db: AsyncSession = Depends(get_db)
):
    """获取缺陷趋势数据"""
    try:
        # 计算日期范围
        end_date = datetime.now()
        days_map = {"7d": 7, "30d": 30, "90d": 90, "1y": 365}
        start_date = end_date - timedelta(days=days_map[date_range])
        
        # 构建查询 - SQLite兼容的日期函数
        if group_by == "day":
            date_trunc = func.date(Defect.found_date)
        elif group_by == "week":
            # SQLite: 获取周的第一天
            date_trunc = func.date(Defect.found_date, 'weekday 0', '-6 days')
        else:  # month
            # SQLite: 获取月的第一天
            date_trunc = func.date(Defect.found_date, 'start of month')
        
        query = select(
            date_trunc.label("date"),
            func.count(Defect.id).label("total_count"),
            func.sum(case((Defect.severity == DefectSeverity.CRITICAL, 1), else_=0)).label("critical_count"),
            func.sum(case((Defect.severity == DefectSeverity.HIGH, 1), else_=0)).label("high_count"),
            func.sum(case((Defect.severity == DefectSeverity.MEDIUM, 1), else_=0)).label("medium_count"),
            func.sum(case((Defect.severity == DefectSeverity.LOW, 1), else_=0)).label("low_count"),
            func.sum(case((Defect.status == DefectStatus.RESOLVED, 1), else_=0)).label("resolved_count")
        ).where(
            and_(
                Defect.found_date >= start_date,
                Defect.found_date <= end_date
            )
        )
        
        if project_id:
            query = query.where(Defect.project_id == project_id)
        
        query = query.group_by(date_trunc).order_by(date_trunc)
        
        result = await db.execute(query)
        trends = result.fetchall()
        
        # 格式化数据
        trend_data = []
        for trend in trends:
            # 处理SQLite返回的日期格式（字符串）和PostgreSQL返回的datetime对象
            if trend.date:
                if isinstance(trend.date, str):
                    # SQLite返回字符串格式的日期
                    date_str = trend.date
                else:
                    # PostgreSQL返回datetime对象
                    date_str = trend.date.strftime("%Y-%m-%d")
            else:
                date_str = ""

            trend_data.append(DefectTrendResponse(
                date=date_str,
                total_count=trend.total_count or 0,
                critical_count=trend.critical_count or 0,
                high_count=trend.high_count or 0,
                medium_count=trend.medium_count or 0,
                low_count=trend.low_count or 0,
                resolved_count=trend.resolved_count or 0
            ))
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "trends": [trend.dict() for trend in trend_data],
                "date_range": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                "group_by": group_by
            }
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缺陷趋势失败: {str(e)}")

@router.get("/distribution")
async def get_defect_distribution(
    project_id: Optional[int] = Query(None, description="项目ID"),
    dimension: str = Query("severity", regex="^(severity|status|priority|type)$", description="分布维度"),
    db: AsyncSession = Depends(get_db)
):
    """获取缺陷分布统计"""
    try:
        # 根据维度选择分组字段
        if dimension == "severity":
            group_field = Defect.severity
        elif dimension == "status":
            group_field = Defect.status
        elif dimension == "priority":
            group_field = Defect.priority
        else:  # type
            group_field = Defect.defect_type
        
        query = select(
            group_field,
            func.count(Defect.id).label("count")
        )
        
        if project_id:
            query = query.where(Defect.project_id == project_id)
        
        query = query.group_by(group_field)
        
        result = await db.execute(query)
        distribution = result.fetchall()
        
        # 计算总数和百分比
        total_count = sum(item.count for item in distribution)
        
        distribution_data = []
        for item in distribution:
            percentage = (item.count / total_count * 100) if total_count > 0 else 0
            distribution_data.append(DefectDistributionResponse(
                label=item[0].value if hasattr(item[0], 'value') else str(item[0]),
                count=item.count,
                percentage=round(percentage, 2)
            ))
        
        return JSONResponse(content={
            "success": True,
            "data": {
                "distribution": [dist.dict() for dist in distribution_data],
                "dimension": dimension,
                "total_count": total_count
            }
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缺陷分布失败: {str(e)}")

@router.get("/stats")
async def get_defect_stats(
    project_id: Optional[int] = Query(None, description="项目ID"),
    db: AsyncSession = Depends(get_db)
):
    """获取缺陷统计概览"""
    try:
        # 基础查询
        base_query = select(Defect)
        if project_id:
            base_query = base_query.where(Defect.project_id == project_id)
        
        # 总缺陷数
        total_query = select(func.count(Defect.id))
        if project_id:
            total_query = total_query.where(Defect.project_id == project_id)
        
        total_result = await db.execute(total_query)
        total_defects = total_result.scalar() or 0
        
        # 各状态缺陷数
        open_query = total_query.where(Defect.status.in_([DefectStatus.OPEN, DefectStatus.IN_PROGRESS, DefectStatus.REOPENED]))
        open_result = await db.execute(open_query)
        open_defects = open_result.scalar() or 0
        
        resolved_query = total_query.where(Defect.status.in_([DefectStatus.RESOLVED, DefectStatus.CLOSED]))
        resolved_result = await db.execute(resolved_query)
        resolved_defects = resolved_result.scalar() or 0
        
        # 严重程度统计
        critical_query = total_query.where(Defect.severity == DefectSeverity.CRITICAL)
        critical_result = await db.execute(critical_query)
        critical_defects = critical_result.scalar() or 0
        
        high_query = total_query.where(Defect.severity == DefectSeverity.HIGH)
        high_result = await db.execute(high_query)
        high_defects = high_result.scalar() or 0
        
        # 平均解决时间（已解决的缺陷）
        avg_resolution_query = select(
            func.avg(
                func.extract('epoch', Defect.resolved_date - Defect.found_date) / 86400
            )
        ).where(
            and_(
                Defect.resolved_date.isnot(None),
                Defect.project_id == project_id if project_id else True
            )
        )
        
        avg_result = await db.execute(avg_resolution_query)
        avg_resolution_time = avg_result.scalar() or 0
        
        stats = DefectStatsResponse(
            total_defects=total_defects,
            open_defects=open_defects,
            resolved_defects=resolved_defects,
            overdue_defects=0,  # 需要在应用层计算
            avg_resolution_time=round(avg_resolution_time, 2),
            critical_defects=critical_defects,
            high_defects=high_defects
        )
        
        return JSONResponse(content={
            "success": True,
            "data": stats.dict()
        })
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缺陷统计失败: {str(e)}")
