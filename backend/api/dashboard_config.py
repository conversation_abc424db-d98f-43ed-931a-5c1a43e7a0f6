"""
仪表板配置API接口
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import Optional, Dict, Any, List
from datetime import datetime

from database import get_db
from models.dashboard import DashboardConfig, DashboardWidget

router = APIRouter(prefix="/api/dashboard-config", tags=["dashboard-config"])

# Pydantic Schemas for DashboardConfig
class DashboardConfigBase(BaseModel):
    config_name: str = Field(..., description="配置名称")
    is_default: Optional[bool] = Field(False, description="是否为默认配置")
    layout_config: Optional[Dict[str, Any]] = Field(None, description="布局配置JSON")
    widget_configs: Optional[Dict[str, Any]] = Field(None, description="组件配置JSON")
    focused_metric_ids: Optional[List[str]] = Field(None, description="用户关注的指标ID列表")

class DashboardConfigCreate(DashboardConfigBase):
    pass

class DashboardConfigUpdate(DashboardConfigBase):
    config_name: Optional[str] = None # 在更新时，所有字段都是可选的

class DashboardConfigResponse(DashboardConfigBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


@router.get("/users/{user_id}/configs", response_model=List[DashboardConfigResponse])
async def get_user_dashboard_configs(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取用户的仪表板配置列表"""
    try:
        query = select(DashboardConfig).where(DashboardConfig.user_id == user_id)
        result = await db.execute(query)
        configs = result.scalars().all()
        return configs
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


@router.post("/users/{user_id}/configs", response_model=DashboardConfigResponse)
async def create_dashboard_config(
    user_id: int,
    config_data: DashboardConfigCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建新的仪表板配置"""
    try:
        # 如果设置为默认配置，先取消其他默认配置
        if config_data.is_default:
            update_query = select(DashboardConfig).where(
                and_(
                    DashboardConfig.user_id == user_id,
                    DashboardConfig.is_default == True
                )
            )
            result = await db.execute(update_query)
            existing_defaults = result.scalars().all()
            for config in existing_defaults:
                config.is_default = False
        
        # 创建新配置
        new_config = DashboardConfig(
            user_id=user_id,
            config_name=config_data.config_name,
            is_default=config_data.is_default,
            layout_config=config_data.layout_config or {},
            widget_configs=config_data.widget_configs or {},
            focused_metric_ids=config_data.focused_metric_ids or []
        )
        
        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)
        
        return new_config
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")


@router.put("/configs/{config_id}", response_model=DashboardConfigResponse)
async def update_dashboard_config(
    config_id: int,
    config_data: DashboardConfigUpdate,
    db: AsyncSession = Depends(get_db)
):
    """更新仪表板配置"""
    try:
        query = select(DashboardConfig).where(DashboardConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        # 如果设置为默认配置，先取消同用户的其他默认配置
        if config_data.is_default is True and not config.is_default:
            update_query = select(DashboardConfig).where(
                and_(
                    DashboardConfig.user_id == config.user_id,
                    DashboardConfig.is_default == True,
                    DashboardConfig.id != config_id # 排除当前正在更新的配置
                )
            )
            result = await db.execute(update_query)
            existing_defaults = result.scalars().all()
            for existing_config in existing_defaults:
                existing_config.is_default = False
        
        # 更新配置
        update_data = config_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(config, key, value)
        
        await db.commit()
        await db.refresh(config)
        
        return config
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.delete("/configs/{config_id}")
async def delete_dashboard_config(
    config_id: int,
    db: AsyncSession = Depends(get_db)
):
    """删除仪表板配置"""
    try:
        query = select(DashboardConfig).where(DashboardConfig.id == config_id)
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        await db.delete(config)
        await db.commit()
        
        return {
            "success": True,
            "message": "配置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")


@router.get("/widgets") # 保持不变
async def get_available_widgets(
    category: Optional[str] = Query(None, description="组件分类"),
    db: AsyncSession = Depends(get_db)
):
    """获取可用的仪表板组件列表"""
    try:
        query = select(DashboardWidget).where(DashboardWidget.is_active == True)
        
        if category:
            query = query.where(DashboardWidget.category == category)
        
        result = await db.execute(query)
        widgets = result.scalars().all()
        
        return {
            "success": True,
            "data": [
                {
                    "id": widget.id,
                    "widget_type": widget.widget_type,
                    "widget_title": widget.widget_title,
                    "widget_config": widget.widget_config,
                    "category": widget.category,
                    "description": widget.description
                }
                for widget in widgets
            ]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取组件列表失败: {str(e)}")


@router.get("/users/{user_id}/default-config", response_model=Optional[DashboardConfigResponse])
async def get_user_default_config(
    user_id: int,
    db: AsyncSession = Depends(get_db)
):
    """获取用户的默认仪表板配置"""
    try:
        query = select(DashboardConfig).where(
            and_(
                DashboardConfig.user_id == user_id,
                DashboardConfig.is_default == True
            )
        )
        result = await db.execute(query)
        config = result.scalar_one_or_none()
        
        if not config:
            # 如果没有用户特定的默认配置，可以考虑返回一个全局的、预定义的默认配置
            # 或者返回 None，让前端处理
            # 为了与之前的逻辑保持一定兼容性，并提供一个基础配置，我们构建一个临时的默认配置
            # 但不包含 focused_metric_ids，因为这是用户特定的
            return { # 直接返回字典，因为 Pydantic 模型需要 id
                "id": None, # 表示这是一个临时的、非数据库存储的配置
                "user_id": user_id,
                "config_name": "系统默认",
                "is_default": True,
                "layout_config": get_default_layout_config(),
                "widget_configs": get_default_widget_configs(),
                "focused_metric_ids": [], # 用户关注指标默认为空
                "created_at": datetime.now(), # 仅为示例
                "updated_at": datetime.now()  # 仅为示例
            }
        
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取默认配置失败: {str(e)}")


def get_default_layout_config():
    """获取默认布局配置"""
    return [
        {"i": "metric-overview", "x": 0, "y": 0, "w": 12, "h": 4},
        {"i": "defect-trend", "x": 0, "y": 4, "w": 6, "h": 6},
        {"i": "coverage-trend", "x": 6, "y": 4, "w": 6, "h": 6},
        {"i": "team-comparison", "x": 0, "y": 10, "w": 12, "h": 6}
    ]

# 移除了 set_favorite_metrics 和 get_favorite_metrics，
# 因为它们的功能现在由 update_dashboard_config 和 get_user_dashboard_configs/get_user_default_config 中的 focused_metric_ids 字段处理。
# 如果需要保留这两个特定端点，需要重新设计它们与 focused_metric_ids 的交互。
# 为了简化，这里选择移除。

def get_default_widget_configs():
    """获取默认组件配置"""
    return {
        "metric-overview": {
            "type": "metric-cards",
            "title": "质量指标概览",
            "config": {
                "showTrend": True,
                "showTarget": True,
                "showQuickAccess": False,
                "metricTypes": ["coverage", "performance", "quality_gate", "efficiency"]
            }
        },
        "defect-trend": {
            "type": "defect-trend-chart",
            "title": "缺陷趋势",
            "config": {"dateRange": "30d", "groupBy": "day"}
        },
        "coverage-trend": {
            "type": "coverage-trend-chart",
            "title": "覆盖率趋势",
            "config": {"dateRange": "30d", "metricType": "line"}
        },
        "team-comparison": {
            "type": "team-comparison-chart",
            "title": "团队质量对比",
            "config": {"showDetails": True}
        }
    }
