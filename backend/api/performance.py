"""
性能监控相关API端点
"""

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Dict, Any
from datetime import datetime, timedelta
from pydantic import BaseModel

router = APIRouter(prefix="/api/performance", tags=["performance"])

class PerformanceMetric(BaseModel):
    """性能指标模型"""
    name: str
    value: str
    unit: str
    status: str  # "excellent", "good", "warning", "danger"
    threshold: str
    trend: str   # "up", "down", "stable"

class ServicePerformance(BaseModel):
    """服务性能模型"""
    service_name: str
    avg_response_time: float
    p95_response_time: float
    p99_response_time: float
    throughput: float
    error_rate: float
    availability: float

class AlertRule(BaseModel):
    """告警规则模型"""
    rule_name: str
    metric: str
    threshold: str
    severity: str  # "critical", "warning", "info"
    status: str    # "active", "inactive"

def generate_performance_mock_data():
    """生成性能监控模拟数据"""
    return {
        "metrics": [
            PerformanceMetric(
                name="平均响应时间",
                value="125",
                unit="ms",
                status="good",
                threshold="< 150ms",
                trend="down"
            ),
            PerformanceMetric(
                name="P95响应时间",
                value="280",
                unit="ms",
                status="good",
                threshold="< 300ms",
                trend="stable"
            ),
            PerformanceMetric(
                name="系统吞吐量",
                value="1250",
                unit="req/s",
                status="excellent",
                threshold="> 1000 req/s",
                trend="up"
            ),
            PerformanceMetric(
                name="错误率",
                value="0.12",
                unit="%",
                status="excellent",
                threshold="< 0.5%",
                trend="down"
            ),
            PerformanceMetric(
                name="系统可用性",
                value="99.95",
                unit="%",
                status="excellent",
                threshold="> 99.9%",
                trend="stable"
            ),
            PerformanceMetric(
                name="CPU使用率",
                value="65",
                unit="%",
                status="good",
                threshold="< 80%",
                trend="stable"
            )
        ],
        "service_performance": [
            ServicePerformance(
                service_name="用户中心",
                avg_response_time=95.0,
                p95_response_time=180.0,
                p99_response_time=250.0,
                throughput=320.0,
                error_rate=0.08,
                availability=99.98
            ),
            ServicePerformance(
                service_name="订单系统",
                avg_response_time=145.0,
                p95_response_time=280.0,
                p99_response_time=420.0,
                throughput=280.0,
                error_rate=0.15,
                availability=99.92
            ),
            ServicePerformance(
                service_name="支付系统",
                avg_response_time=110.0,
                p95_response_time=200.0,
                p99_response_time=300.0,
                throughput=180.0,
                error_rate=0.05,
                availability=99.99
            ),
            ServicePerformance(
                service_name="商品管理",
                avg_response_time=210.0,
                p95_response_time=450.0,
                p99_response_time=680.0,
                throughput=250.0,
                error_rate=0.25,
                availability=99.85
            ),
            ServicePerformance(
                service_name="营销系统",
                avg_response_time=165.0,
                p95_response_time=320.0,
                p99_response_time=480.0,
                throughput=220.0,
                error_rate=0.18,
                availability=99.90
            )
        ],
        "response_time_trend": {
            "labels": ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            "datasets": [
                {
                    "label": "平均响应时间",
                    "data": [95, 110, 145, 180, 165, 125],
                    "borderColor": "#4f46e5",
                    "backgroundColor": "rgba(79, 70, 229, 0.1)",
                    "tension": 0.3,
                    "fill": True
                },
                {
                    "label": "P95响应时间",
                    "data": [180, 220, 280, 350, 320, 250],
                    "borderColor": "#f97316",
                    "backgroundColor": "rgba(249, 115, 22, 0.1)",
                    "tension": 0.3,
                    "fill": True
                }
            ]
        },
        "throughput_trend": {
            "labels": ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            "datasets": [
                {
                    "label": "系统吞吐量 (req/s)",
                    "data": [800, 600, 1200, 1800, 1500, 1000],
                    "borderColor": "#10b981",
                    "backgroundColor": "rgba(16, 185, 129, 0.1)",
                    "tension": 0.3,
                    "fill": True
                }
            ]
        },
        "alert_rules": [
            AlertRule(
                rule_name="响应时间告警",
                metric="avg_response_time",
                threshold="> 200ms",
                severity="warning",
                status="active"
            ),
            AlertRule(
                rule_name="错误率告警",
                metric="error_rate",
                threshold="> 1%",
                severity="critical",
                status="active"
            ),
            AlertRule(
                rule_name="可用性告警",
                metric="availability",
                threshold="< 99.9%",
                severity="critical",
                status="active"
            )
        ]
    }

@router.get("/overview")
async def get_performance_overview():
    """获取性能监控概览数据"""
    try:
        data = generate_performance_mock_data()
        return JSONResponse(content={
            "success": True,
            "data": {
                "metrics": [metric.dict() for metric in data["metrics"]],
                "last_updated": datetime.now().isoformat()
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/services")
async def get_service_performance():
    """获取服务性能数据"""
    try:
        data = generate_performance_mock_data()
        return JSONResponse(content={
            "success": True,
            "data": {
                "services": [service.dict() for service in data["service_performance"]]
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/trends")
async def get_performance_trends():
    """获取性能趋势数据"""
    try:
        data = generate_performance_mock_data()
        return JSONResponse(content={
            "success": True,
            "data": {
                "response_time_trend": data["response_time_trend"],
                "throughput_trend": data["throughput_trend"]
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/alerts")
async def get_alert_rules():
    """获取告警规则数据"""
    try:
        data = generate_performance_mock_data()
        return JSONResponse(content={
            "success": True,
            "data": {
                "alert_rules": [rule.dict() for rule in data["alert_rules"]]
            }
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/real-time")
async def get_real_time_metrics():
    """获取实时性能指标"""
    try:
        # 模拟实时数据
        real_time_data = {
            "timestamp": datetime.now().isoformat(),
            "current_response_time": 125,
            "current_throughput": 1250,
            "current_error_rate": 0.12,
            "active_connections": 450,
            "cpu_usage": 65,
            "memory_usage": 72,
            "disk_usage": 45
        }
        
        return JSONResponse(content={
            "success": True,
            "data": real_time_data
        })
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
