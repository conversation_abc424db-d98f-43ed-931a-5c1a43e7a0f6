"""
预警系统API接口
"""
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from database import get_db
from models.alert import <PERSON>ert, AlertRule, AlertLevel, AlertType, AlertStatus, AlertNotification
from services.alert_service import QualityAlertService
from services.notification_service import NotificationService
# from schemas.alert import (
#     AlertResponse, AlertRuleResponse, AlertRuleCreate, AlertRuleUpdate,
#     AlertUpdate, NotificationConfigTest
# )

router = APIRouter(prefix="/api/alerts", tags=["alerts"])


@router.get("/", response_model=Dict[str, Any])
async def get_alerts(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="预警状态"),
    level: Optional[str] = Query(None, description="预警级别"),
    alert_type: Optional[str] = Query(None, description="预警类型"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    db: AsyncSession = Depends(get_db)
):
    """获取预警列表"""
    try:
        # 构建查询条件
        query = select(Alert).options(
            selectinload(Alert.project),
            selectinload(Alert.rule)
        )
        
        conditions = []
        
        if status:
            conditions.append(Alert.status == status)
        if level:
            conditions.append(Alert.level == level)
        if alert_type:
            conditions.append(Alert.alert_type == alert_type)
        if project_id:
            conditions.append(Alert.project_id == project_id)
        if start_date:
            start_datetime = datetime.fromisoformat(start_date)
            conditions.append(Alert.created_at >= start_datetime)
        if end_date:
            end_datetime = datetime.fromisoformat(end_date)
            conditions.append(Alert.created_at <= end_datetime)
            
        if conditions:
            query = query.where(and_(*conditions))
            
        # 获取总数
        count_query = select(func.count(Alert.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        query = query.order_by(desc(Alert.created_at))
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(query)
        alerts = result.scalars().all()
        
        return {
            "success": True,
            "data": {
                "alerts": [_alert_to_dict(alert) for alert in alerts],
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预警列表失败: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_alert_stats(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    db: AsyncSession = Depends(get_db)
):
    """获取预警统计"""
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 基础查询条件
        base_conditions = [Alert.created_at >= start_date]
        if project_id:
            base_conditions.append(Alert.project_id == project_id)
        
        # 按级别统计
        level_query = select(
            Alert.level,
            func.count(Alert.id).label('count')
        ).where(and_(*base_conditions)).group_by(Alert.level)
        
        level_result = await db.execute(level_query)
        level_stats = {row.level: row.count for row in level_result.fetchall()}
        
        # 按类型统计
        type_query = select(
            Alert.alert_type,
            func.count(Alert.id).label('count')
        ).where(and_(*base_conditions)).group_by(Alert.alert_type)
        
        type_result = await db.execute(type_query)
        type_stats = {row.alert_type: row.count for row in type_result.fetchall()}
        
        # 按状态统计
        status_query = select(
            Alert.status,
            func.count(Alert.id).label('count')
        ).where(and_(*base_conditions)).group_by(Alert.status)
        
        status_result = await db.execute(status_query)
        status_stats = {row.status: row.count for row in status_result.fetchall()}
        
        # 趋势统计（按天）
        trend_query = select(
            func.date(Alert.created_at).label('date'),
            func.count(Alert.id).label('count')
        ).where(and_(*base_conditions)).group_by(
            func.date(Alert.created_at)
        ).order_by(func.date(Alert.created_at))
        
        trend_result = await db.execute(trend_query)
        trend_data = [
            {
                'date': row.date.strftime('%Y-%m-%d'),
                'count': row.count
            }
            for row in trend_result.fetchall()
        ]
        
        return {
            "success": True,
            "data": {
                "level_stats": level_stats,
                "type_stats": type_stats,
                "status_stats": status_stats,
                "trend_data": trend_data,
                "total_alerts": sum(level_stats.values()),
                "date_range": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预警统计失败: {str(e)}")


@router.put("/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: int,
    acknowledged_by: str = Query(..., description="确认人"),
    db: AsyncSession = Depends(get_db)
):
    """确认预警"""
    try:
        # 获取预警
        query = select(Alert).where(Alert.id == alert_id)
        result = await db.execute(query)
        alert = result.scalar_one_or_none()
        
        if not alert:
            raise HTTPException(status_code=404, detail="预警不存在")
            
        if alert.status != AlertStatus.ACTIVE.value:
            raise HTTPException(status_code=400, detail="只能确认活跃状态的预警")
        
        # 更新预警状态
        alert.status = AlertStatus.ACKNOWLEDGED.value
        alert.acknowledged_at = datetime.now()
        alert.acknowledged_by = acknowledged_by
        
        await db.commit()
        
        return {
            "success": True,
            "message": "预警确认成功",
            "data": _alert_to_dict(alert)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"确认预警失败: {str(e)}")


@router.put("/{alert_id}/resolve")
async def resolve_alert(
    alert_id: int,
    resolved_by: str = Query(..., description="解决人"),
    resolution_notes: Optional[str] = Query(None, description="解决说明"),
    db: AsyncSession = Depends(get_db)
):
    """解决预警"""
    try:
        # 获取预警
        query = select(Alert).where(Alert.id == alert_id)
        result = await db.execute(query)
        alert = result.scalar_one_or_none()
        
        if not alert:
            raise HTTPException(status_code=404, detail="预警不存在")
            
        if alert.status == AlertStatus.RESOLVED.value:
            raise HTTPException(status_code=400, detail="预警已经解决")
        
        # 更新预警状态
        alert.status = AlertStatus.RESOLVED.value
        alert.resolved_at = datetime.now()
        alert.resolved_by = resolved_by
        if resolution_notes:
            alert.resolution_notes = resolution_notes
        
        await db.commit()
        
        return {
            "success": True,
            "message": "预警解决成功",
            "data": _alert_to_dict(alert)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"解决预警失败: {str(e)}")


@router.post("/check", response_model=Dict[str, Any])
async def trigger_alert_check(
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """手动触发预警检查"""
    try:
        alert_service = QualityAlertService(db)
        
        # 在后台任务中执行预警检查
        background_tasks.add_task(alert_service.check_all_alerts)
        
        return {
            "success": True,
            "message": "预警检查已启动，将在后台执行"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发预警检查失败: {str(e)}")


@router.get("/rules", response_model=Dict[str, Any])
async def get_alert_rules(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    is_enabled: Optional[bool] = Query(None, description="是否启用"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    db: AsyncSession = Depends(get_db)
):
    """获取预警规则列表"""
    try:
        # 构建查询条件
        query = select(AlertRule).options(
            selectinload(AlertRule.project),
            selectinload(AlertRule.team)
        )
        
        conditions = []
        if is_enabled is not None:
            conditions.append(AlertRule.is_enabled == is_enabled)
        if project_id:
            conditions.append(AlertRule.project_id == project_id)
            
        if conditions:
            query = query.where(and_(*conditions))
            
        # 获取总数
        count_query = select(func.count(AlertRule.id))
        if conditions:
            count_query = count_query.where(and_(*conditions))
        count_result = await db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        query = query.order_by(desc(AlertRule.created_at))
        query = query.offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(query)
        rules = result.scalars().all()
        
        return {
            "success": True,
            "data": {
                "rules": [_rule_to_dict(rule) for rule in rules],
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预警规则失败: {str(e)}")


def _alert_to_dict(alert: Alert) -> Dict[str, Any]:
    """将预警对象转换为字典"""
    return {
        'id': alert.id,
        'title': alert.title,
        'description': alert.description,
        'alert_type': alert.alert_type,
        'level': alert.level,
        'status': alert.status,
        'project_id': alert.project_id,
        'project_name': alert.project.name if alert.project else None,
        'current_value': alert.current_value,
        'previous_value': alert.previous_value,
        'threshold_value': alert.threshold_value,
        'change_rate': alert.change_rate,
        'suggested_actions': alert.suggested_actions,
        'acknowledged_at': alert.acknowledged_at.isoformat() if alert.acknowledged_at else None,
        'acknowledged_by': alert.acknowledged_by,
        'resolved_at': alert.resolved_at.isoformat() if alert.resolved_at else None,
        'resolved_by': alert.resolved_by,
        'resolution_notes': alert.resolution_notes,
        'created_at': alert.created_at.isoformat() if alert.created_at else None,
        'updated_at': alert.updated_at.isoformat() if alert.updated_at else None
    }


def _rule_to_dict(rule: AlertRule) -> Dict[str, Any]:
    """将预警规则对象转换为字典"""
    return {
        'id': rule.id,
        'name': rule.name,
        'description': rule.description,
        'alert_type': rule.alert_type,
        'level': rule.level,
        'threshold_value': rule.threshold_value,
        'threshold_operator': rule.threshold_operator,
        'window_minutes': rule.window_minutes,
        'project_id': rule.project_id,
        'project_name': rule.project.name if rule.project else None,
        'team_id': rule.team_id,
        'team_name': rule.team.name if rule.team else None,
        'is_enabled': rule.is_enabled,
        'notification_config': rule.notification_config,
        'created_at': rule.created_at.isoformat() if rule.created_at else None,
        'updated_at': rule.updated_at.isoformat() if rule.updated_at else None
    }
