#!/usr/bin/env python3
"""
API端点测试脚本
用于验证新增的API端点是否正常工作
"""

import asyncio
import aiohttp
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

async def test_api_endpoint(session, endpoint, params=None):
    """测试单个API端点"""
    url = f"{BASE_URL}{endpoint}"
    try:
        async with session.get(url, params=params) as response:
            status = response.status
            data = await response.json()
            
            print(f"✅ {endpoint}")
            print(f"   状态码: {status}")
            print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)[:200]}...")
            print()
            
            return status == 200 and data.get("success", False)
            
    except Exception as e:
        print(f"❌ {endpoint}")
        print(f"   错误: {str(e)}")
        print()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试API端点...")
    print("=" * 50)
    
    # 要测试的API端点
    test_endpoints = [
        # 基础数据API
        "/api/projects",
        "/api/teams", 
        "/api/projects/1",
        "/api/teams/1",
        
        # 测试相关API
        "/api/test-cases",
        "/api/test-executions",
        "/api/automation-metrics",
        
        # 性能相关API
        "/api/performance-metrics",
        "/api/service-metrics", 
        "/api/system-metrics",
        
        # 质量门禁API
        "/api/quality-gate-executions",
        
        # 现有的API（验证是否仍然工作）
        "/api/dashboard/overview",
        "/api/dashboard/trends",
        "/api/dashboard/teams",
        "/api/automation/overview",
        "/api/performance/overview",
        "/api/quality-gate/overview"
    ]
    
    success_count = 0
    total_count = len(test_endpoints)
    
    async with aiohttp.ClientSession() as session:
        for endpoint in test_endpoints:
            # 为某些端点添加测试参数
            params = {}
            if "?" not in endpoint:
                params = {"page": 1, "page_size": 5}
            
            success = await test_api_endpoint(session, endpoint, params)
            if success:
                success_count += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 个端点正常工作")
    
    if success_count == total_count:
        print("🎉 所有API端点测试通过！")
    else:
        print(f"⚠️  有 {total_count - success_count} 个端点需要修复")

if __name__ == "__main__":
    print("请确保后端服务已启动 (http://localhost:8000)")
    print("启动命令: cd backend && uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
