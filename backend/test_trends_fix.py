#!/usr/bin/env python3
"""
测试缺陷趋势API修复
"""

import asyncio
import sys
import os
sys.path.append('.')

from fastapi.testclient import TestClient
from main import app

def test_trends_api():
    """测试缺陷趋势API"""
    print("🧪 测试缺陷趋势API修复...")
    
    client = TestClient(app)
    
    try:
        # 测试趋势API
        response = client.get('/api/defects/trends?date_range=30d&group_by=day')
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功!")
            print(f"🎯 成功标志: {data.get('success', False)}")
            
            if data.get('success'):
                trends = data.get('data', {}).get('trends', [])
                print(f"📈 趋势数据条数: {len(trends)}")
                if trends:
                    print(f"📅 第一条数据: {trends[0]}")
                    print(f"📅 最后一条数据: {trends[-1]}")
                print("✅ 缺陷趋势API修复成功!")
            else:
                print(f"❌ API返回失败: {data}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"💥 错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_trends_api()
