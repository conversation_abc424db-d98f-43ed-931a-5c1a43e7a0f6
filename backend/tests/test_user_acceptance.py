"""
缺陷管理模块用户验收测试
从用户角度验证功能完整性和用户体验
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta

class UserAcceptanceTest:
    """用户验收测试类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
    
    def log_test_result(self, test_name, passed, message=""):
        """记录测试结果"""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
    
    async def test_user_story_1_view_defect_overview(self):
        """用户故事1: 作为测试经理，我希望查看缺陷概览，了解当前质量状况"""
        print("\n📊 用户故事1: 查看缺陷概览")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 获取缺陷统计数据
                async with session.get(f"{self.base_url}/api/defects/stats") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success"):
                            stats = data.get("data", {})
                            
                            # 验证关键指标存在
                            required_metrics = ["total_defects", "open_defects", "resolved_defects", "critical_defects"]
                            missing_metrics = [m for m in required_metrics if m not in stats]
                            
                            if not missing_metrics:
                                self.log_test_result(
                                    "查看缺陷统计概览", 
                                    True, 
                                    f"总缺陷: {stats['total_defects']}, 未解决: {stats['open_defects']}, 严重: {stats['critical_defects']}"
                                )
                            else:
                                self.log_test_result(
                                    "查看缺陷统计概览", 
                                    False, 
                                    f"缺少关键指标: {missing_metrics}"
                                )
                        else:
                            self.log_test_result("查看缺陷统计概览", False, "API返回失败")
                    else:
                        self.log_test_result("查看缺陷统计概览", False, f"HTTP {response.status}")
            except Exception as e:
                self.log_test_result("查看缺陷统计概览", False, str(e))
    
    async def test_user_story_2_analyze_defect_trends(self):
        """用户故事2: 作为测试经理，我希望分析缺陷趋势，识别质量变化"""
        print("\n📈 用户故事2: 分析缺陷趋势")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 获取30天缺陷趋势
                params = {"date_range": "30d", "group_by": "day"}
                async with session.get(f"{self.base_url}/api/defects/trends", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success"):
                            trends = data.get("data", {}).get("trends", [])
                            
                            if trends:
                                # 验证趋势数据完整性
                                has_dates = all("date" in trend for trend in trends)
                                has_counts = all("total_count" in trend for trend in trends)
                                has_severity_breakdown = all(
                                    all(f"{severity}_count" in trend for severity in ["critical", "high", "medium", "low"])
                                    for trend in trends
                                )
                                
                                if has_dates and has_counts and has_severity_breakdown:
                                    self.log_test_result(
                                        "分析缺陷趋势", 
                                        True, 
                                        f"获取到 {len(trends)} 天的趋势数据，包含完整的严重程度分解"
                                    )
                                else:
                                    self.log_test_result("分析缺陷趋势", False, "趋势数据结构不完整")
                            else:
                                self.log_test_result("分析缺陷趋势", False, "未获取到趋势数据")
                        else:
                            self.log_test_result("分析缺陷趋势", False, "API返回失败")
                    else:
                        self.log_test_result("分析缺陷趋势", False, f"HTTP {response.status}")
            except Exception as e:
                self.log_test_result("分析缺陷趋势", False, str(e))
    
    async def test_user_story_3_filter_defects_by_severity(self):
        """用户故事3: 作为开发人员，我希望按严重程度筛选缺陷，优先处理重要问题"""
        print("\n🔍 用户故事3: 按严重程度筛选缺陷")
        
        async with aiohttp.ClientSession() as session:
            try:
                severities = ["critical", "high", "medium", "low"]
                all_passed = True
                
                for severity in severities:
                    params = {"severity": severity, "pageSize": 10}
                    async with session.get(f"{self.base_url}/api/defects/", params=params) as response:
                        if response.status == 200:
                            defects = await response.json()
                            
                            # 验证筛选结果
                            if isinstance(defects, list):
                                # 检查返回的缺陷是否都是指定严重程度
                                correct_severity = all(
                                    defect.get("severity") == severity 
                                    for defect in defects
                                ) if defects else True
                                
                                if correct_severity:
                                    self.log_test_result(
                                        f"筛选{severity}级缺陷", 
                                        True, 
                                        f"返回 {len(defects)} 条{severity}级缺陷"
                                    )
                                else:
                                    self.log_test_result(f"筛选{severity}级缺陷", False, "筛选结果包含其他严重程度的缺陷")
                                    all_passed = False
                            else:
                                self.log_test_result(f"筛选{severity}级缺陷", False, "返回数据格式错误")
                                all_passed = False
                        else:
                            self.log_test_result(f"筛选{severity}级缺陷", False, f"HTTP {response.status}")
                            all_passed = False
                
                if all_passed:
                    self.log_test_result("按严重程度筛选功能", True, "所有严重程度筛选测试通过")
                
            except Exception as e:
                self.log_test_result("按严重程度筛选缺陷", False, str(e))
    
    async def test_user_story_4_view_defect_distribution(self):
        """用户故事4: 作为测试经理，我希望查看缺陷分布，了解问题分布情况"""
        print("\n🥧 用户故事4: 查看缺陷分布")
        
        async with aiohttp.ClientSession() as session:
            try:
                dimensions = ["severity", "status", "priority", "type"]
                all_passed = True
                
                for dimension in dimensions:
                    params = {"dimension": dimension}
                    async with session.get(f"{self.base_url}/api/defects/distribution", params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("success"):
                                distribution = data.get("data", {}).get("distribution", [])
                                
                                # 验证分布数据
                                has_labels = all("label" in item for item in distribution)
                                has_counts = all("count" in item for item in distribution)
                                has_percentages = all("percentage" in item for item in distribution)
                                
                                if has_labels and has_counts and has_percentages:
                                    total_percentage = sum(item["percentage"] for item in distribution)
                                    percentage_valid = abs(total_percentage - 100) < 1  # 允许1%的误差
                                    
                                    if percentage_valid or not distribution:  # 空数据也是有效的
                                        self.log_test_result(
                                            f"查看{dimension}分布", 
                                            True, 
                                            f"获取到 {len(distribution)} 个分类，百分比总和: {total_percentage:.1f}%"
                                        )
                                    else:
                                        self.log_test_result(f"查看{dimension}分布", False, f"百分比总和异常: {total_percentage:.1f}%")
                                        all_passed = False
                                else:
                                    self.log_test_result(f"查看{dimension}分布", False, "分布数据结构不完整")
                                    all_passed = False
                            else:
                                self.log_test_result(f"查看{dimension}分布", False, "API返回失败")
                                all_passed = False
                        else:
                            self.log_test_result(f"查看{dimension}分布", False, f"HTTP {response.status}")
                            all_passed = False
                
                if all_passed:
                    self.log_test_result("缺陷分布查看功能", True, "所有维度分布查看测试通过")
                
            except Exception as e:
                self.log_test_result("查看缺陷分布", False, str(e))
    
    async def test_user_story_5_pagination_and_sorting(self):
        """用户故事5: 作为用户，我希望能够分页浏览和排序缺陷列表"""
        print("\n📄 用户故事5: 分页浏览和排序")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 测试分页功能
                params = {"page": 1, "pageSize": 5}
                async with session.get(f"{self.base_url}/api/defects/", params=params) as response:
                    if response.status == 200:
                        page1_defects = await response.json()
                        
                        if isinstance(page1_defects, list) and len(page1_defects) <= 5:
                            self.log_test_result("分页功能", True, f"第1页返回 {len(page1_defects)} 条记录")
                        else:
                            self.log_test_result("分页功能", False, f"分页大小控制异常: {len(page1_defects)}")
                    else:
                        self.log_test_result("分页功能", False, f"HTTP {response.status}")
                
                # 测试排序功能
                sort_fields = ["created_at", "title", "severity"]
                for field in sort_fields:
                    for order in ["asc", "desc"]:
                        params = {"sortBy": field, "sortOrder": order, "pageSize": 3}
                        async with session.get(f"{self.base_url}/api/defects/", params=params) as response:
                            if response.status == 200:
                                defects = await response.json()
                                if isinstance(defects, list):
                                    self.log_test_result(
                                        f"按{field}排序({order})", 
                                        True, 
                                        f"返回 {len(defects)} 条记录"
                                    )
                                else:
                                    self.log_test_result(f"按{field}排序({order})", False, "返回数据格式错误")
                            else:
                                self.log_test_result(f"按{field}排序({order})", False, f"HTTP {response.status}")
                
            except Exception as e:
                self.log_test_result("分页浏览和排序", False, str(e))
    
    async def test_data_consistency(self):
        """测试数据一致性"""
        print("\n🔄 测试数据一致性")
        
        async with aiohttp.ClientSession() as session:
            try:
                # 获取统计数据
                async with session.get(f"{self.base_url}/api/defects/stats") as response:
                    if response.status == 200:
                        stats_data = await response.json()
                        if stats_data.get("success"):
                            stats = stats_data.get("data", {})
                            total_from_stats = stats.get("total_defects", 0)
                            
                            # 获取所有缺陷
                            async with session.get(f"{self.base_url}/api/defects/?pageSize=1000") as response:
                                if response.status == 200:
                                    all_defects = await response.json()
                                    total_from_list = len(all_defects) if isinstance(all_defects, list) else 0
                                    
                                    if total_from_stats == total_from_list:
                                        self.log_test_result(
                                            "数据一致性检查", 
                                            True, 
                                            f"统计数据与列表数据一致: {total_from_stats} 条"
                                        )
                                    else:
                                        self.log_test_result(
                                            "数据一致性检查", 
                                            False, 
                                            f"数据不一致: 统计{total_from_stats} vs 列表{total_from_list}"
                                        )
                                else:
                                    self.log_test_result("数据一致性检查", False, "无法获取缺陷列表")
                        else:
                            self.log_test_result("数据一致性检查", False, "无法获取统计数据")
                    else:
                        self.log_test_result("数据一致性检查", False, f"HTTP {response.status}")
            except Exception as e:
                self.log_test_result("数据一致性检查", False, str(e))
    
    def generate_acceptance_report(self):
        """生成验收测试报告"""
        print("\n" + "=" * 60)
        print("📋 用户验收测试报告")
        print("=" * 60)
        
        passed_tests = [r for r in self.test_results if r["passed"]]
        failed_tests = [r for r in self.test_results if not r["passed"]]
        
        print(f"\n✅ 通过测试: {len(passed_tests)}")
        print(f"❌ 失败测试: {len(failed_tests)}")
        print(f"📊 通过率: {len(passed_tests) / len(self.test_results) * 100:.1f}%")
        
        if failed_tests:
            print("\n❌ 失败的测试:")
            for test in failed_tests:
                print(f"  • {test['test']}: {test['message']}")
        
        print("\n📝 验收结论:")
        if len(passed_tests) / len(self.test_results) >= 0.9:
            print("🎉 缺陷管理模块通过用户验收测试，可以交付使用")
        elif len(passed_tests) / len(self.test_results) >= 0.7:
            print("⚠️  缺陷管理模块基本满足要求，但需要修复部分问题")
        else:
            print("❌ 缺陷管理模块未通过验收测试，需要重大修复")
        
        return len(failed_tests) == 0
    
    async def run_all_tests(self):
        """运行所有用户验收测试"""
        print("🚀 开始缺陷管理模块用户验收测试")
        print("=" * 60)
        
        # 运行所有用户故事测试
        await self.test_user_story_1_view_defect_overview()
        await self.test_user_story_2_analyze_defect_trends()
        await self.test_user_story_3_filter_defects_by_severity()
        await self.test_user_story_4_view_defect_distribution()
        await self.test_user_story_5_pagination_and_sorting()
        await self.test_data_consistency()
        
        # 生成报告
        return self.generate_acceptance_report()

async def main():
    """主函数"""
    tester = UserAcceptanceTest()
    success = await tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
