"""
前后端联调集成测试
测试缺陷管理模块的前后端数据交互
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class DefectIntegrationTest:
    """缺陷管理集成测试类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def setup(self):
        """设置测试环境"""
        self.session = aiohttp.ClientSession()
        print("🔧 设置测试环境...")
    
    async def teardown(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
        print("🧹 清理测试环境...")
    
    async def test_api_health(self):
        """测试API健康状态"""
        print("\n📡 测试API健康状态...")
        try:
            async with self.session.get(f"{self.base_url}/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ API健康检查通过: {data['message']}")
                    return True
                else:
                    print(f"❌ API健康检查失败: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"❌ API连接失败: {str(e)}")
            return False
    
    async def test_defect_list_api(self):
        """测试缺陷列表API"""
        print("\n📋 测试缺陷列表API...")
        try:
            # 测试基础列表查询
            async with self.session.get(f"{self.base_url}/api/defects/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 缺陷列表查询成功，返回 {len(data)} 条记录")
                    
                    # 验证数据结构
                    if data and len(data) > 0:
                        defect = data[0]
                        required_fields = ['id', 'title', 'severity', 'status', 'priority']
                        missing_fields = [field for field in required_fields if field not in defect]
                        if missing_fields:
                            print(f"⚠️  缺少必要字段: {missing_fields}")
                        else:
                            print("✅ 数据结构验证通过")
                    
                    return True
                else:
                    print(f"❌ 缺陷列表查询失败: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 缺陷列表API测试失败: {str(e)}")
            return False
    
    async def test_defect_trends_api(self):
        """测试缺陷趋势API"""
        print("\n📈 测试缺陷趋势API...")
        try:
            params = {
                "date_range": "30d",
                "group_by": "day"
            }
            
            async with self.session.get(f"{self.base_url}/api/defects/trends", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        trends = data.get("data", {}).get("trends", [])
                        print(f"✅ 缺陷趋势查询成功，返回 {len(trends)} 个数据点")
                        
                        # 验证趋势数据结构
                        if trends:
                            trend = trends[0]
                            required_fields = ['date', 'total_count', 'critical_count', 'high_count']
                            missing_fields = [field for field in required_fields if field not in trend]
                            if missing_fields:
                                print(f"⚠️  趋势数据缺少字段: {missing_fields}")
                            else:
                                print("✅ 趋势数据结构验证通过")
                        
                        return True
                    else:
                        print(f"❌ 缺陷趋势查询失败: {data.get('message', '未知错误')}")
                        return False
                else:
                    print(f"❌ 缺陷趋势API失败: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 缺陷趋势API测试失败: {str(e)}")
            return False
    
    async def test_defect_distribution_api(self):
        """测试缺陷分布API"""
        print("\n🥧 测试缺陷分布API...")
        try:
            dimensions = ["severity", "status", "priority", "type"]
            
            for dimension in dimensions:
                params = {"dimension": dimension}
                
                async with self.session.get(f"{self.base_url}/api/defects/distribution", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success"):
                            distribution = data.get("data", {}).get("distribution", [])
                            print(f"✅ {dimension} 分布查询成功，返回 {len(distribution)} 个分类")
                        else:
                            print(f"❌ {dimension} 分布查询失败: {data.get('message', '未知错误')}")
                            return False
                    else:
                        print(f"❌ {dimension} 分布API失败: HTTP {response.status}")
                        return False
            
            return True
        except Exception as e:
            print(f"❌ 缺陷分布API测试失败: {str(e)}")
            return False
    
    async def test_defect_stats_api(self):
        """测试缺陷统计API"""
        print("\n📊 测试缺陷统计API...")
        try:
            async with self.session.get(f"{self.base_url}/api/defects/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        stats = data.get("data", {})
                        required_stats = ['total_defects', 'open_defects', 'resolved_defects', 'critical_defects']
                        missing_stats = [stat for stat in required_stats if stat not in stats]
                        
                        if missing_stats:
                            print(f"⚠️  统计数据缺少字段: {missing_stats}")
                        else:
                            print("✅ 缺陷统计查询成功")
                            print(f"   总缺陷数: {stats['total_defects']}")
                            print(f"   未解决: {stats['open_defects']}")
                            print(f"   已解决: {stats['resolved_defects']}")
                            print(f"   严重缺陷: {stats['critical_defects']}")
                        
                        return True
                    else:
                        print(f"❌ 缺陷统计查询失败: {data.get('message', '未知错误')}")
                        return False
                else:
                    print(f"❌ 缺陷统计API失败: HTTP {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 缺陷统计API测试失败: {str(e)}")
            return False
    
    async def test_api_parameters(self):
        """测试API参数处理"""
        print("\n🔧 测试API参数处理...")
        try:
            # 测试分页参数
            params = {"page": 1, "pageSize": 10, "sortBy": "created_at", "sortOrder": "desc"}
            async with self.session.get(f"{self.base_url}/api/defects/", params=params) as response:
                if response.status == 200:
                    print("✅ 分页参数处理正常")
                else:
                    print(f"❌ 分页参数处理失败: HTTP {response.status}")
                    return False
            
            # 测试筛选参数
            params = {"severity": "high", "status": "open"}
            async with self.session.get(f"{self.base_url}/api/defects/", params=params) as response:
                if response.status == 200:
                    print("✅ 筛选参数处理正常")
                else:
                    print(f"❌ 筛选参数处理失败: HTTP {response.status}")
                    return False
            
            return True
        except Exception as e:
            print(f"❌ API参数测试失败: {str(e)}")
            return False
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n🚨 测试错误处理...")
        try:
            # 测试无效参数
            params = {"date_range": "invalid", "group_by": "invalid"}
            async with self.session.get(f"{self.base_url}/api/defects/trends", params=params) as response:
                if response.status == 422:
                    print("✅ 无效参数错误处理正常")
                else:
                    print(f"⚠️  无效参数返回状态码: {response.status}")
            
            return True
        except Exception as e:
            print(f"❌ 错误处理测试失败: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始缺陷管理模块前后端联调测试")
        print("=" * 50)
        
        await self.setup()
        
        tests = [
            self.test_api_health,
            self.test_defect_list_api,
            self.test_defect_trends_api,
            self.test_defect_distribution_api,
            self.test_defect_stats_api,
            self.test_api_parameters,
            self.test_error_handling
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                result = await test()
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ 测试执行异常: {str(e)}")
        
        await self.teardown()
        
        print("\n" + "=" * 50)
        print(f"📋 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！缺陷管理模块前后端联调成功")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关功能")
            return False

async def main():
    """主函数"""
    tester = DefectIntegrationTest()
    success = await tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
