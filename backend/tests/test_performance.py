"""
缺陷管理模块性能测试
测试API响应时间、并发处理能力和资源使用情况
"""

import asyncio
import aiohttp
import time
import statistics
import psutil
import os
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

class PerformanceTest:
    """性能测试类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.results = {}
    
    async def measure_response_time(self, session, url, params=None, iterations=10):
        """测量API响应时间"""
        times = []
        
        for i in range(iterations):
            start_time = time.time()
            try:
                async with session.get(url, params=params) as response:
                    await response.read()
                    end_time = time.time()
                    
                    if response.status == 200:
                        times.append((end_time - start_time) * 1000)  # 转换为毫秒
                    else:
                        print(f"⚠️  请求失败: HTTP {response.status}")
            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")
        
        if times:
            return {
                "avg": statistics.mean(times),
                "min": min(times),
                "max": max(times),
                "median": statistics.median(times),
                "count": len(times)
            }
        return None
    
    async def test_defect_list_performance(self):
        """测试缺陷列表API性能"""
        print("\n📋 测试缺陷列表API性能...")
        
        async with aiohttp.ClientSession() as session:
            # 测试不同页面大小的性能
            page_sizes = [10, 20, 50, 100]
            
            for page_size in page_sizes:
                params = {"page": 1, "pageSize": page_size}
                url = f"{self.base_url}/api/defects/"
                
                result = await self.measure_response_time(session, url, params, 5)
                if result:
                    print(f"  页面大小 {page_size}: 平均 {result['avg']:.2f}ms, 最大 {result['max']:.2f}ms")
                    self.results[f"defect_list_page_{page_size}"] = result
    
    async def test_trends_performance(self):
        """测试趋势API性能"""
        print("\n📈 测试缺陷趋势API性能...")
        
        async with aiohttp.ClientSession() as session:
            # 测试不同时间范围的性能
            date_ranges = ["7d", "30d", "90d", "1y"]
            
            for date_range in date_ranges:
                params = {"date_range": date_range, "group_by": "day"}
                url = f"{self.base_url}/api/defects/trends"
                
                result = await self.measure_response_time(session, url, params, 5)
                if result:
                    print(f"  时间范围 {date_range}: 平均 {result['avg']:.2f}ms, 最大 {result['max']:.2f}ms")
                    self.results[f"trends_{date_range}"] = result
    
    async def test_concurrent_requests(self):
        """测试并发请求性能"""
        print("\n🚀 测试并发请求性能...")
        
        async def make_request(session, semaphore):
            async with semaphore:
                start_time = time.time()
                try:
                    async with session.get(f"{self.base_url}/api/defects/stats") as response:
                        await response.read()
                        end_time = time.time()
                        return (end_time - start_time) * 1000, response.status
                except Exception as e:
                    return None, 0
        
        # 测试不同并发级别
        concurrency_levels = [5, 10, 20, 50]
        
        for concurrency in concurrency_levels:
            semaphore = asyncio.Semaphore(concurrency)
            
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                
                tasks = [make_request(session, semaphore) for _ in range(concurrency * 2)]
                results = await asyncio.gather(*tasks)
                
                end_time = time.time()
                total_time = (end_time - start_time) * 1000
                
                # 统计结果
                successful_requests = [r[0] for r in results if r[0] is not None and r[1] == 200]
                failed_requests = len(results) - len(successful_requests)
                
                if successful_requests:
                    avg_response_time = statistics.mean(successful_requests)
                    max_response_time = max(successful_requests)
                    throughput = len(successful_requests) / (total_time / 1000)
                    
                    print(f"  并发 {concurrency}: 成功 {len(successful_requests)}, 失败 {failed_requests}")
                    print(f"    平均响应时间: {avg_response_time:.2f}ms")
                    print(f"    最大响应时间: {max_response_time:.2f}ms")
                    print(f"    吞吐量: {throughput:.2f} req/s")
                    
                    self.results[f"concurrent_{concurrency}"] = {
                        "avg_response_time": avg_response_time,
                        "max_response_time": max_response_time,
                        "throughput": throughput,
                        "success_rate": len(successful_requests) / len(results) * 100
                    }
    
    def measure_system_resources(self):
        """测量系统资源使用情况"""
        print("\n💻 测量系统资源使用情况...")
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        print(f"  CPU使用率: {cpu_percent}%")
        print(f"  内存使用率: {memory.percent}% ({memory.used / 1024**3:.2f}GB / {memory.total / 1024**3:.2f}GB)")
        print(f"  磁盘使用率: {disk.percent}% ({disk.used / 1024**3:.2f}GB / {disk.total / 1024**3:.2f}GB)")
        
        self.results["system_resources"] = {
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_gb": memory.used / 1024**3,
            "memory_total_gb": memory.total / 1024**3,
            "disk_percent": disk.percent
        }
    
    async def test_memory_usage_under_load(self):
        """测试负载下的内存使用情况"""
        print("\n🧠 测试负载下的内存使用情况...")
        
        # 记录初始内存使用
        initial_memory = psutil.virtual_memory().used / 1024**2  # MB
        
        async with aiohttp.ClientSession() as session:
            # 创建大量并发请求
            tasks = []
            for _ in range(100):
                task = session.get(f"{self.base_url}/api/defects/")
                tasks.append(task)
            
            # 执行请求并测量内存
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 关闭响应
            for response in responses:
                if hasattr(response, 'close'):
                    response.close()
        
        # 记录峰值内存使用
        peak_memory = psutil.virtual_memory().used / 1024**2  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f"  初始内存: {initial_memory:.2f}MB")
        print(f"  峰值内存: {peak_memory:.2f}MB")
        print(f"  内存增长: {memory_increase:.2f}MB")
        
        self.results["memory_under_load"] = {
            "initial_memory_mb": initial_memory,
            "peak_memory_mb": peak_memory,
            "memory_increase_mb": memory_increase
        }
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        print("\n📊 性能测试报告")
        print("=" * 50)
        
        # API响应时间报告
        print("\n🕐 API响应时间:")
        for key, result in self.results.items():
            if "defect_list" in key or "trends" in key:
                if isinstance(result, dict) and "avg" in result:
                    status = "✅" if result["avg"] < 500 else "⚠️" if result["avg"] < 1000 else "❌"
                    print(f"  {status} {key}: {result['avg']:.2f}ms (目标: <500ms)")
        
        # 并发性能报告
        print("\n🚀 并发性能:")
        for key, result in self.results.items():
            if "concurrent" in key:
                if isinstance(result, dict) and "throughput" in result:
                    status = "✅" if result["throughput"] > 10 else "⚠️" if result["throughput"] > 5 else "❌"
                    print(f"  {status} {key}: {result['throughput']:.2f} req/s, 成功率: {result['success_rate']:.1f}%")
        
        # 资源使用报告
        print("\n💻 系统资源:")
        if "system_resources" in self.results:
            resources = self.results["system_resources"]
            cpu_status = "✅" if resources["cpu_percent"] < 70 else "⚠️" if resources["cpu_percent"] < 90 else "❌"
            memory_status = "✅" if resources["memory_percent"] < 80 else "⚠️" if resources["memory_percent"] < 90 else "❌"
            
            print(f"  {cpu_status} CPU使用率: {resources['cpu_percent']}% (目标: <70%)")
            print(f"  {memory_status} 内存使用率: {resources['memory_percent']}% (目标: <80%)")
        
        # 性能建议
        print("\n💡 性能优化建议:")
        suggestions = []
        
        # 检查响应时间
        slow_apis = [k for k, v in self.results.items() 
                    if isinstance(v, dict) and "avg" in v and v["avg"] > 500]
        if slow_apis:
            suggestions.append("考虑为慢速API添加缓存机制")
        
        # 检查并发性能
        low_throughput = [k for k, v in self.results.items() 
                         if isinstance(v, dict) and "throughput" in v and v["throughput"] < 10]
        if low_throughput:
            suggestions.append("考虑优化数据库查询或增加连接池大小")
        
        # 检查资源使用
        if "system_resources" in self.results:
            resources = self.results["system_resources"]
            if resources["cpu_percent"] > 70:
                suggestions.append("CPU使用率较高，考虑优化算法或增加服务器资源")
            if resources["memory_percent"] > 80:
                suggestions.append("内存使用率较高，考虑优化内存使用或增加内存")
        
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion}")
        else:
            print("  🎉 性能表现良好，无需特别优化")
    
    async def run_all_tests(self):
        """运行所有性能测试"""
        print("🚀 开始缺陷管理模块性能测试")
        print("=" * 50)
        
        # 测量基线资源使用
        self.measure_system_resources()
        
        # 运行性能测试
        await self.test_defect_list_performance()
        await self.test_trends_performance()
        await self.test_concurrent_requests()
        await self.test_memory_usage_under_load()
        
        # 生成报告
        self.generate_performance_report()

async def main():
    """主函数"""
    tester = PerformanceTest()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
