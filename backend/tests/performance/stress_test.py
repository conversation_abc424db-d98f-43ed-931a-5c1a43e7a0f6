"""
压力测试脚本
"""

import asyncio
import aiohttp
import time
import statistics
from typing import List, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import json
import argparse


@dataclass
class TestResult:
    """测试结果"""
    url: str
    method: str
    status_code: int
    response_time: float
    timestamp: datetime
    error: str = None


class StressTestRunner:
    """压力测试运行器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results: List[TestResult] = []
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, method: str, endpoint: str, **kwargs) -> TestResult:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                await response.text()  # 读取响应内容
                response_time = time.time() - start_time
                
                return TestResult(
                    url=url,
                    method=method,
                    status_code=response.status,
                    response_time=response_time,
                    timestamp=datetime.now()
                )
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                url=url,
                method=method,
                status_code=0,
                response_time=response_time,
                timestamp=datetime.now(),
                error=str(e)
            )
    
    async def run_concurrent_requests(
        self, 
        method: str, 
        endpoint: str, 
        concurrent_users: int, 
        requests_per_user: int,
        **kwargs
    ) -> List[TestResult]:
        """运行并发请求测试"""
        print(f"开始压力测试: {concurrent_users} 并发用户, 每用户 {requests_per_user} 请求")
        print(f"目标端点: {method} {endpoint}")
        
        async def user_requests():
            """单个用户的请求序列"""
            user_results = []
            for _ in range(requests_per_user):
                result = await self.make_request(method, endpoint, **kwargs)
                user_results.append(result)
                self.results.append(result)
            return user_results
        
        # 创建并发任务
        tasks = [user_requests() for _ in range(concurrent_users)]
        
        # 执行所有任务
        start_time = time.time()
        await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        print(f"测试完成，总耗时: {total_time:.2f}秒")
        
        return self.results
    
    def analyze_results(self) -> Dict[str, Any]:
        """分析测试结果"""
        if not self.results:
            return {}
        
        # 基本统计
        total_requests = len(self.results)
        successful_requests = len([r for r in self.results if 200 <= r.status_code < 300])
        failed_requests = total_requests - successful_requests
        
        # 响应时间统计
        response_times = [r.response_time for r in self.results if r.error is None]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = self._percentile(response_times, 95)
            p99_response_time = self._percentile(response_times, 99)
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = 0
            max_response_time = min_response_time = 0
        
        # 错误统计
        error_types = {}
        for result in self.results:
            if result.error:
                error_types[result.error] = error_types.get(result.error, 0) + 1
        
        # 状态码统计
        status_codes = {}
        for result in self.results:
            status_codes[result.status_code] = status_codes.get(result.status_code, 0) + 1
        
        # 计算吞吐量
        if self.results:
            test_duration = (max(r.timestamp for r in self.results) - 
                           min(r.timestamp for r in self.results)).total_seconds()
            throughput = total_requests / test_duration if test_duration > 0 else 0
        else:
            throughput = 0
        
        return {
            'summary': {
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'failed_requests': failed_requests,
                'success_rate': (successful_requests / total_requests) * 100 if total_requests > 0 else 0,
                'throughput_rps': throughput
            },
            'response_times': {
                'average': avg_response_time,
                'median': median_response_time,
                'p95': p95_response_time,
                'p99': p99_response_time,
                'min': min_response_time,
                'max': max_response_time
            },
            'status_codes': status_codes,
            'errors': error_types,
            'performance_grade': self._calculate_performance_grade(
                avg_response_time, 
                successful_requests / total_requests if total_requests > 0 else 0
            )
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """计算百分位数"""
        if not data:
            return 0
        sorted_data = sorted(data)
        index = int((percentile / 100) * len(sorted_data))
        return sorted_data[min(index, len(sorted_data) - 1)]
    
    def _calculate_performance_grade(self, avg_response_time: float, success_rate: float) -> str:
        """计算性能等级"""
        if success_rate < 0.95:  # 成功率低于95%
            return 'F'
        elif avg_response_time > 2.0:  # 平均响应时间超过2秒
            return 'D'
        elif avg_response_time > 1.0:  # 平均响应时间超过1秒
            return 'C'
        elif avg_response_time > 0.5:  # 平均响应时间超过0.5秒
            return 'B'
        else:
            return 'A'
    
    def generate_report(self, output_file: str = None) -> str:
        """生成测试报告"""
        analysis = self.analyze_results()
        
        report = f"""
=== 质量大盘压力测试报告 ===
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

== 测试概要 ==
总请求数: {analysis['summary']['total_requests']}
成功请求数: {analysis['summary']['successful_requests']}
失败请求数: {analysis['summary']['failed_requests']}
成功率: {analysis['summary']['success_rate']:.2f}%
吞吐量: {analysis['summary']['throughput_rps']:.2f} 请求/秒
性能等级: {analysis['performance_grade']}

== 响应时间统计 ==
平均响应时间: {analysis['response_times']['average']:.3f}秒
中位数响应时间: {analysis['response_times']['median']:.3f}秒
95%响应时间: {analysis['response_times']['p95']:.3f}秒
99%响应时间: {analysis['response_times']['p99']:.3f}秒
最小响应时间: {analysis['response_times']['min']:.3f}秒
最大响应时间: {analysis['response_times']['max']:.3f}秒

== 状态码分布 ==
"""
        
        for status_code, count in analysis['status_codes'].items():
            report += f"{status_code}: {count} 次\n"
        
        if analysis['errors']:
            report += "\n== 错误统计 ==\n"
            for error, count in analysis['errors'].items():
                report += f"{error}: {count} 次\n"
        
        # 性能建议
        report += "\n== 性能建议 ==\n"
        if analysis['response_times']['average'] > 0.5:
            report += "- 平均响应时间较高，建议优化数据库查询和缓存策略\n"
        if analysis['summary']['success_rate'] < 99:
            report += "- 成功率有待提升，建议检查错误处理和系统稳定性\n"
        if analysis['response_times']['p99'] > 2.0:
            report += "- 99%响应时间过高，存在性能瓶颈\n"
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report)
                f.write("\n\n== 详细数据 ==\n")
                f.write(json.dumps(analysis, indent=2, ensure_ascii=False))
        
        return report


async def run_dashboard_stress_test():
    """运行仪表板压力测试"""
    async with StressTestRunner() as runner:
        # 测试场景配置
        test_scenarios = [
            {
                'name': '仪表板概览',
                'method': 'GET',
                'endpoint': '/api/dashboard/overview',
                'concurrent_users': 50,
                'requests_per_user': 10
            },
            {
                'name': '质量趋势',
                'method': 'GET',
                'endpoint': '/api/dashboard/trends',
                'concurrent_users': 30,
                'requests_per_user': 15
            },
            {
                'name': '团队对比',
                'method': 'GET',
                'endpoint': '/api/dashboard/teams',
                'concurrent_users': 20,
                'requests_per_user': 20
            }
        ]
        
        print("=== 质量大盘压力测试开始 ===\n")
        
        for scenario in test_scenarios:
            print(f"执行测试场景: {scenario['name']}")
            await runner.run_concurrent_requests(
                method=scenario['method'],
                endpoint=scenario['endpoint'],
                concurrent_users=scenario['concurrent_users'],
                requests_per_user=scenario['requests_per_user']
            )
            print()
        
        # 生成报告
        report = runner.generate_report('stress_test_report.txt')
        print(report)
        
        return runner.analyze_results()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='质量大盘压力测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='API基础URL')
    parser.add_argument('--users', type=int, default=50, help='并发用户数')
    parser.add_argument('--requests', type=int, default=10, help='每用户请求数')
    parser.add_argument('--endpoint', default='/api/dashboard/overview', help='测试端点')
    
    args = parser.parse_args()
    
    async def custom_test():
        async with StressTestRunner(args.base_url) as runner:
            await runner.run_concurrent_requests(
                method='GET',
                endpoint=args.endpoint,
                concurrent_users=args.users,
                requests_per_user=args.requests
            )
            
            report = runner.generate_report()
            print(report)
    
    # 运行测试
    if args.endpoint == '/api/dashboard/overview' and args.users == 50:
        # 运行完整的仪表板测试
        asyncio.run(run_dashboard_stress_test())
    else:
        # 运行自定义测试
        asyncio.run(custom_test())
