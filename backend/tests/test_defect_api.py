"""
缺陷管理API单元测试
测试缺陷相关的API端点功能
"""

import pytest
import asyncio
from httpx import AsyncClient
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from main import app
from database import get_db, Base
from models.defect import Defect, DefectSeverity, DefectStatus, DefectPriority, DefectType
from models.dashboard import User
from models.dashboard import Project

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite+aiosqlite:///:memory:"

engine = create_async_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=AsyncSession
)

@pytest.fixture
async def db_session():
    """创建测试数据库会话"""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestingSessionLocal() as session:
        yield session
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest.fixture
def override_get_db(db_session):
    """覆盖数据库依赖"""
    async def _override_get_db():
        yield db_session
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()

@pytest.fixture
async def test_data(db_session):
    """创建测试数据"""
    # 创建测试项目
    project = Project(
        name="测试项目",
        description="用于测试的项目",
        status="active"
    )
    db_session.add(project)
    await db_session.commit()
    await db_session.refresh(project)
    
    # 创建测试用户
    user = User(
        username="testuser",
        email="<EMAIL>",
        full_name="测试用户",
        role="developer"
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    
    # 创建测试缺陷
    defects = []
    for i in range(5):
        defect = Defect(
            title=f"测试缺陷 {i+1}",
            description=f"这是第{i+1}个测试缺陷",
            severity=DefectSeverity.HIGH if i < 2 else DefectSeverity.MEDIUM,
            priority=DefectPriority.HIGH,
            status=DefectStatus.OPEN if i < 3 else DefectStatus.RESOLVED,
            defect_type=DefectType.FUNCTIONAL,
            project_id=project.id,
            assignee_id=user.id,
            reporter_id=user.id,
            found_date=datetime.now() - timedelta(days=i),
            resolved_date=datetime.now() if i >= 3 else None,
            environment="test",
            version="1.0.0"
        )
        defects.append(defect)
        db_session.add(defect)
    
    await db_session.commit()
    
    return {
        "project": project,
        "user": user,
        "defects": defects
    }

@pytest.mark.asyncio
class TestDefectAPI:
    """缺陷API测试类"""
    
    async def test_get_defects_list(self, override_get_db, test_data):
        """测试获取缺陷列表"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/")
            
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5
        assert data[0]["title"] == "测试缺陷 1"
    
    async def test_get_defects_with_filters(self, override_get_db, test_data):
        """测试带筛选条件的缺陷列表"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 按严重程度筛选
            response = await ac.get("/api/defects/?severity=high")
            
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 按状态筛选
            response = await ac.get("/api/defects/?status=open")
            
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
    
    async def test_get_defects_with_pagination(self, override_get_db, test_data):
        """测试分页功能"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/?page=1&pageSize=2")
            
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
    
    async def test_get_defect_trends(self, override_get_db, test_data):
        """测试缺陷趋势数据"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/trends?date_range=7d&group_by=day")
            
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "trends" in data["data"]
        assert "date_range" in data["data"]
    
    async def test_get_defect_distribution(self, override_get_db, test_data):
        """测试缺陷分布统计"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/distribution?dimension=severity")
            
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "distribution" in data["data"]
        assert len(data["data"]["distribution"]) > 0
    
    async def test_get_defect_stats(self, override_get_db, test_data):
        """测试缺陷统计概览"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/stats")
            
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        stats = data["data"]
        assert stats["total_defects"] == 5
        assert stats["open_defects"] == 3
        assert stats["resolved_defects"] == 2
    
    async def test_defect_trends_with_project_filter(self, override_get_db, test_data):
        """测试按项目筛选的缺陷趋势"""
        project_id = test_data["project"].id
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get(f"/api/defects/trends?project_id={project_id}")
            
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
    
    async def test_defect_distribution_by_status(self, override_get_db, test_data):
        """测试按状态的缺陷分布"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/distribution?dimension=status")
            
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        distribution = data["data"]["distribution"]
        
        # 验证分布数据
        status_counts = {item["label"]: item["count"] for item in distribution}
        assert status_counts.get("open", 0) == 3
        assert status_counts.get("resolved", 0) == 2
    
    async def test_invalid_parameters(self, override_get_db, test_data):
        """测试无效参数处理"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 无效的日期范围
            response = await ac.get("/api/defects/trends?date_range=invalid")
            
        assert response.status_code == 422  # 参数验证错误
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 无效的分组方式
            response = await ac.get("/api/defects/trends?group_by=invalid")
            
        assert response.status_code == 422
    
    async def test_empty_database(self, override_get_db):
        """测试空数据库的情况"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/")
            
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 0
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/defects/stats")
            
        assert response.status_code == 200
        data = response.json()
        stats = data["data"]
        assert stats["total_defects"] == 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
