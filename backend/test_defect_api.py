"""
测试缺陷管理API
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_defect_api():
    """测试缺陷管理API端点"""
    base_url = "http://localhost:8000"
    
    async with httpx.AsyncClient() as client:
        print("开始测试缺陷管理API...")
        
        # 测试1: 获取缺陷统计
        print("\n1. 测试缺陷统计API")
        try:
            response = await client.get(f"{base_url}/api/defects/stats")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"统计数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"错误: {response.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 测试2: 获取缺陷趋势
        print("\n2. 测试缺陷趋势API")
        try:
            response = await client.get(f"{base_url}/api/defects/trends?date_range=30d&group_by=day")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"趋势数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"错误: {response.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 测试3: 获取缺陷分布
        print("\n3. 测试缺陷分布API")
        try:
            response = await client.get(f"{base_url}/api/defects/distribution?dimension=severity")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"分布数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            else:
                print(f"错误: {response.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 测试4: 获取缺陷列表
        print("\n4. 测试缺陷列表API")
        try:
            response = await client.get(f"{base_url}/api/defects/?page=1&pageSize=5")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"缺陷列表: {json.dumps(data[:2] if data else [], indent=2, ensure_ascii=False)}")  # 只显示前2条
            else:
                print(f"错误: {response.text}")
        except Exception as e:
            print(f"请求失败: {e}")
        
        print("\n缺陷管理API测试完成！")


def test_sync():
    """同步测试函数"""
    import requests
    
    base_url = "http://localhost:8000"
    
    print("开始同步测试缺陷管理API...")
    
    # 测试API文档
    print("\n1. 测试API文档")
    try:
        response = requests.get(f"{base_url}/docs")
        print(f"API文档状态码: {response.status_code}")
    except Exception as e:
        print(f"API文档请求失败: {e}")
    
    # 测试根路径
    print("\n2. 测试根路径")
    try:
        response = requests.get(f"{base_url}/")
        print(f"根路径状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"根路径响应: {response.json()}")
    except Exception as e:
        print(f"根路径请求失败: {e}")
    
    # 测试缺陷统计（简单测试）
    print("\n3. 测试缺陷统计API")
    try:
        response = requests.get(f"{base_url}/api/defects/stats")
        print(f"缺陷统计状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"统计数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"缺陷统计请求失败: {e}")


if __name__ == "__main__":
    print("选择测试方式:")
    print("1. 异步测试 (需要asyncio)")
    print("2. 同步测试 (使用requests)")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_defect_api())
    else:
        test_sync()
