#!/usr/bin/env python3
"""
简化的覆盖率数据初始化脚本
"""

import asyncio
import sys
from datetime import datetime, timedelta
import random

async def test_imports():
    """测试导入"""
    try:
        print("测试基础导入...")
        from database import engine, Base, AsyncSessionLocal
        print("✅ 数据库导入成功")
        
        from models.dashboard import Project, ProjectStatus
        print("✅ Project模型导入成功")
        
        from models.coverage import CoverageMetric, FileCoverage, CoverageTarget
        print("✅ Coverage模型导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def create_tables():
    """创建表"""
    try:
        print("创建数据库表...")
        from database import engine, Base
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 表创建成功")
        return True
    except Exception as e:
        print(f"❌ 表创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def create_sample_data():
    """创建示例数据"""
    try:
        print("创建示例数据...")
        from database import AsyncSessionLocal
        from models.dashboard import Project, ProjectStatus
        from models.coverage import CoverageMetric, CoverageTarget
        
        async with AsyncSessionLocal() as db:
            # 创建项目
            projects_data = [
                {"name": "质量大盘前端", "description": "前端应用", "status": ProjectStatus.ACTIVE},
                {"name": "质量大盘后端", "description": "后端API", "status": ProjectStatus.ACTIVE},
                {"name": "自动化测试框架", "description": "测试框架", "status": ProjectStatus.ACTIVE},
            ]
            
            created_projects = []
            for project_data in projects_data:
                project = Project(**project_data)
                db.add(project)
                created_projects.append(project)
            
            await db.commit()
            
            # 刷新获取ID
            for project in created_projects:
                await db.refresh(project)
            
            print(f"✅ 创建了 {len(created_projects)} 个项目")
            
            # 创建覆盖率目标
            for project in created_projects:
                target = CoverageTarget(
                    project_id=project.id,
                    target_line_coverage=80.0,
                    target_branch_coverage=75.0,
                    target_function_coverage=85.0,
                    warning_threshold=70.0,
                    critical_threshold=60.0,
                    is_active=True
                )
                db.add(target)
            
            await db.commit()
            print("✅ 创建了覆盖率目标")
            
            # 创建覆盖率指标
            sources = ["Jest", "SonarQube", "Pytest"]
            branches = ["main", "develop"]
            
            for project in created_projects:
                for i in range(5):  # 每个项目5条记录
                    measurement_date = datetime.now() - timedelta(days=random.randint(0, 10))
                    
                    line_coverage = round(random.uniform(70, 95), 1)
                    branch_coverage = round(random.uniform(65, 90), 1)
                    function_coverage = round(random.uniform(75, 95), 1)
                    
                    total_lines = random.randint(1000, 5000)
                    covered_lines = int(total_lines * line_coverage / 100)
                    
                    total_branches = random.randint(200, 1000)
                    covered_branches = int(total_branches * branch_coverage / 100)
                    
                    total_functions = random.randint(50, 300)
                    covered_functions = int(total_functions * function_coverage / 100)
                    
                    metric = CoverageMetric(
                        project_id=project.id,
                        branch_name=random.choice(branches),
                        commit_hash=f"abc{random.randint(1000, 9999)}",
                        build_number=f"build-{random.randint(100, 999)}",
                        line_coverage=line_coverage,
                        branch_coverage=branch_coverage,
                        function_coverage=function_coverage,
                        statement_coverage=round(line_coverage + random.uniform(-2, 2), 1),
                        condition_coverage=round(branch_coverage + random.uniform(-3, 3), 1),
                        total_lines=total_lines,
                        covered_lines=covered_lines,
                        total_branches=total_branches,
                        covered_branches=covered_branches,
                        total_functions=total_functions,
                        covered_functions=covered_functions,
                        source=random.choice(sources),
                        measurement_date=measurement_date,
                        report_url=f"https://coverage.example.com/{project.name.lower()}/{random.randint(1000, 9999)}",
                        raw_data={"tool_version": "1.0.0"}
                    )
                    
                    db.add(metric)
            
            await db.commit()
            print("✅ 创建了覆盖率指标数据")
            
        return True
        
    except Exception as e:
        print(f"❌ 数据创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 开始简化初始化...")
    
    # 1. 测试导入
    if not await test_imports():
        return False
    
    # 2. 创建表
    if not await create_tables():
        return False
    
    # 3. 创建数据
    if not await create_sample_data():
        return False
    
    print("\n🎉 初始化完成！")
    return True

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 异常: {e}")
        sys.exit(1)
