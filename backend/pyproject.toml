[project]
name = "quality-dashboard-backend"
version = "1.0.0"
description = "质量大盘后端API"
requires-python = ">=3.8.1"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.0",
    "aiosqlite>=0.19.0",
    "python-dateutil>=2.8.2",
    "requests>=2.32.3",
    "aiohttp>=3.10.11",
    "httpx>=0.28.1",
    "pyyaml>=6.0.1",
    "pandas>=2.0.3",
    "openpyxl>=3.1.5",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
]


