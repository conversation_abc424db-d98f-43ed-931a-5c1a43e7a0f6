"""
预警系统数据模型
"""
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.base import BaseModel


class AlertLevel(Enum):
    """预警级别"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class AlertType(Enum):
    """预警类型"""
    DEFECT_SPIKE = "defect_spike"
    COVERAGE_DROP = "coverage_drop"
    QUALITY_DECLINE = "quality_decline"
    PERFORMANCE_ISSUE = "performance_issue"
    TEST_FAILURE = "test_failure"


class AlertStatus(Enum):
    """预警状态"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    DISMISSED = "dismissed"


class AlertRule(BaseModel):
    """预警规则模型"""
    __tablename__ = "alert_rules"

    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    alert_type = Column(String(50), nullable=False)
    level = Column(String(20), nullable=False)
    
    # 规则配置
    threshold_value = Column(Float, nullable=False)
    threshold_operator = Column(String(10), nullable=False)  # >, <, >=, <=, ==
    window_minutes = Column(Integer, default=60)  # 时间窗口（分钟）
    
    # 目标配置
    project_id = Column(Integer, ForeignKey("projects.id"))
    team_id = Column(Integer, ForeignKey("teams.id"))
    
    # 规则状态
    is_enabled = Column(Boolean, default=True)
    
    # 通知配置
    notification_config = Column(JSON)  # 通知配置（邮件、钉钉等）
    
    # 关联关系
    project = relationship("Project", back_populates="alert_rules")
    team = relationship("Team", back_populates="alert_rules")
    alerts = relationship("Alert", back_populates="rule")


class Alert(BaseModel):
    """预警记录模型"""
    __tablename__ = "alerts"

    title = Column(String(200), nullable=False)
    description = Column(Text)
    alert_type = Column(String(50), nullable=False)
    level = Column(String(20), nullable=False)
    status = Column(String(20), default=AlertStatus.ACTIVE.value)
    
    # 关联规则
    rule_id = Column(Integer, ForeignKey("alert_rules.id"))
    
    # 目标信息
    project_id = Column(Integer, ForeignKey("projects.id"))
    team_id = Column(Integer, ForeignKey("teams.id"))
    
    # 预警数据
    current_value = Column(Float)
    threshold_value = Column(Float)
    previous_value = Column(Float)
    change_rate = Column(Float)  # 变化率
    
    # 预警详情
    details = Column(JSON)  # 详细数据
    suggested_actions = Column(JSON)  # 建议操作
    
    # 处理信息
    acknowledged_at = Column(DateTime)
    acknowledged_by = Column(String(100))
    resolved_at = Column(DateTime)
    resolved_by = Column(String(100))
    resolution_notes = Column(Text)
    
    # 关联关系
    rule = relationship("AlertRule", back_populates="alerts")
    project = relationship("Project", back_populates="alerts")
    team = relationship("Team", back_populates="alerts")
    notifications = relationship("AlertNotification", back_populates="alert")


class AlertNotification(BaseModel):
    """预警通知记录模型"""
    __tablename__ = "alert_notifications"

    alert_id = Column(Integer, ForeignKey("alerts.id"), nullable=False)
    notification_type = Column(String(50), nullable=False)  # email, webhook, dingtalk
    recipient = Column(String(200), nullable=False)
    
    # 发送状态
    sent_at = Column(DateTime)
    is_sent = Column(Boolean, default=False)
    send_attempts = Column(Integer, default=0)
    last_attempt_at = Column(DateTime)
    error_message = Column(Text)
    
    # 通知内容
    subject = Column(String(200))
    content = Column(Text)
    
    # 关联关系
    alert = relationship("Alert", back_populates="notifications")


class QualityMetric(BaseModel):
    """质量指标模型"""
    __tablename__ = "quality_metrics"

    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    metric_type = Column(String(50), nullable=False)  # defect_count, coverage, test_pass_rate
    metric_value = Column(Float, nullable=False)
    measurement_date = Column(DateTime, default=func.now())
    
    # 元数据
    extra_data = Column(JSON)  # 额外的指标数据
    
    # 关联关系
    project = relationship("Project", back_populates="quality_metrics")



