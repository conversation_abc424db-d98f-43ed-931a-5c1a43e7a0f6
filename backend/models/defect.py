"""
缺陷管理相关数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from .base import BaseModel


class DefectSeverity(enum.Enum):
    """缺陷严重程度枚举"""
    CRITICAL = "critical"  # 严重
    HIGH = "high"         # 高
    MEDIUM = "medium"     # 中
    LOW = "low"          # 低


class DefectPriority(enum.Enum):
    """缺陷优先级枚举"""
    URGENT = "urgent"     # 紧急
    HIGH = "high"        # 高
    MEDIUM = "medium"    # 中
    LOW = "low"         # 低


class DefectStatus(enum.Enum):
    """缺陷状态枚举"""
    OPEN = "open"                # 打开
    IN_PROGRESS = "in_progress"  # 处理中
    RESOLVED = "resolved"        # 已解决
    CLOSED = "closed"           # 已关闭
    REOPENED = "reopened"       # 重新打开


class DefectType(enum.Enum):
    """缺陷类型枚举"""
    FUNCTIONAL = "functional"    # 功能缺陷
    PERFORMANCE = "performance"  # 性能缺陷
    UI = "ui"                   # 界面缺陷
    COMPATIBILITY = "compatibility"  # 兼容性缺陷
    SECURITY = "security"       # 安全缺陷
    DATA = "data"              # 数据缺陷


class Defect(BaseModel):
    """缺陷模型"""
    __tablename__ = "defects"
    
    # 基本信息
    title = Column(String(200), nullable=False, index=True)
    description = Column(Text)
    
    # 分类信息
    severity = Column(Enum(DefectSeverity), default=DefectSeverity.MEDIUM, nullable=False)
    priority = Column(Enum(DefectPriority), default=DefectPriority.MEDIUM, nullable=False)
    status = Column(Enum(DefectStatus), default=DefectStatus.OPEN, nullable=False)
    defect_type = Column(Enum(DefectType), default=DefectType.FUNCTIONAL, nullable=False)
    
    # 关联信息
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    assignee_id = Column(Integer, ForeignKey("users.id"))
    reporter_id = Column(Integer, ForeignKey("users.id"))
    
    # 时间信息
    found_date = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    resolved_date = Column(DateTime(timezone=True))
    
    # 附加信息
    environment = Column(String(100))  # 发现环境
    version = Column(String(50))       # 发现版本
    steps_to_reproduce = Column(Text)  # 重现步骤
    expected_result = Column(Text)     # 期望结果
    actual_result = Column(Text)       # 实际结果
    
    # 关联关系
    project = relationship("Project", back_populates="defects")
    assignee = relationship("User", foreign_keys=[assignee_id])
    reporter = relationship("User", foreign_keys=[reporter_id])
    
    def __repr__(self):
        return f"<Defect(id={self.id}, title='{self.title}', severity='{self.severity.value}', status='{self.status.value}')>"
    
    @property
    def resolution_time_days(self):
        """计算解决时间（天数）"""
        if self.resolved_date and self.found_date:
            return (self.resolved_date - self.found_date).days
        return None
    
    @property
    def is_overdue(self):
        """判断是否超期（根据优先级设定不同的处理时限）"""
        if self.status in [DefectStatus.RESOLVED, DefectStatus.CLOSED]:
            return False
        
        from datetime import datetime, timedelta
        now = datetime.now()
        
        # 根据优先级设定处理时限
        time_limits = {
            DefectPriority.URGENT: 1,   # 1天
            DefectPriority.HIGH: 3,     # 3天
            DefectPriority.MEDIUM: 7,   # 7天
            DefectPriority.LOW: 14      # 14天
        }
        
        limit_days = time_limits.get(self.priority, 7)
        deadline = self.found_date + timedelta(days=limit_days)
        
        return now > deadline



