"""
测试覆盖率数据模型
定义覆盖率相关的数据结构和业务逻辑
"""

from sqlalchemy import Column, Integer, Float, String, DateTime, Text, ForeignKey, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timedelta
from enum import Enum
from typing import Optional, Dict, List
from pydantic import BaseModel as PydanticBaseModel, Field

from .base import BaseModel


class CoverageType(str, Enum):
    """覆盖率类型枚举"""
    LINE = "line"              # 行覆盖率
    BRANCH = "branch"          # 分支覆盖率
    FUNCTION = "function"      # 函数覆盖率
    STATEMENT = "statement"    # 语句覆盖率
    CONDITION = "condition"    # 条件覆盖率


class CoverageSource(str, Enum):
    """覆盖率数据来源枚举"""
    JEST = "jest"              # Jest测试框架
    SONARQUBE = "sonarqube"    # SonarQube代码质量平台
    JACOCO = "jacoco"          # JaCoCo Java覆盖率工具
    PYTEST = "pytest"          # Pytest Python测试框架
    ISTANBUL = "istanbul"      # Istanbul JavaScript覆盖率工具
    MANUAL = "manual"          # 手动录入


class CoverageLevel(str, Enum):
    """覆盖率等级枚举"""
    EXCELLENT = "excellent"    # 优秀 (>= 90%)
    GOOD = "good"             # 良好 (>= 80%)
    FAIR = "fair"             # 一般 (>= 70%)
    POOR = "poor"             # 较差 (>= 60%)
    CRITICAL = "critical"     # 危险 (< 60%)


class CoverageMetric(BaseModel):
    """覆盖率指标模型"""
    __tablename__ = "coverage_metrics"
    
    # 基础信息
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False, comment="项目ID")
    branch_name = Column(String(100), default="main", comment="分支名称")
    commit_hash = Column(String(40), comment="提交哈希")
    build_number = Column(String(50), comment="构建编号")
    
    # 覆盖率数据
    line_coverage = Column(Float, default=0.0, comment="行覆盖率")
    branch_coverage = Column(Float, default=0.0, comment="分支覆盖率")
    function_coverage = Column(Float, default=0.0, comment="函数覆盖率")
    statement_coverage = Column(Float, default=0.0, comment="语句覆盖率")
    condition_coverage = Column(Float, default=0.0, comment="条件覆盖率")
    
    # 统计数据
    total_lines = Column(Integer, default=0, comment="总行数")
    covered_lines = Column(Integer, default=0, comment="覆盖行数")
    total_branches = Column(Integer, default=0, comment="总分支数")
    covered_branches = Column(Integer, default=0, comment="覆盖分支数")
    total_functions = Column(Integer, default=0, comment="总函数数")
    covered_functions = Column(Integer, default=0, comment="覆盖函数数")
    
    # 元数据
    source = Column(String(20), default=CoverageSource.MANUAL, comment="数据来源")
    measurement_date = Column(DateTime, default=func.now(), comment="测量时间")
    report_url = Column(String(500), comment="报告链接")
    raw_data = Column(JSON, comment="原始数据")
    
    # 关联关系
    project = relationship("Project", back_populates="coverage_metrics")
    file_coverages = relationship("FileCoverage", back_populates="coverage_metric", cascade="all, delete-orphan")
    
    @property
    def overall_coverage(self) -> float:
        """计算综合覆盖率"""
        coverages = [
            self.line_coverage,
            self.branch_coverage,
            self.function_coverage
        ]
        valid_coverages = [c for c in coverages if c > 0]
        return sum(valid_coverages) / len(valid_coverages) if valid_coverages else 0.0
    
    @property
    def coverage_level(self) -> CoverageLevel:
        """获取覆盖率等级"""
        overall = self.overall_coverage
        if overall >= 90:
            return CoverageLevel.EXCELLENT
        elif overall >= 80:
            return CoverageLevel.GOOD
        elif overall >= 70:
            return CoverageLevel.FAIR
        elif overall >= 60:
            return CoverageLevel.POOR
        else:
            return CoverageLevel.CRITICAL
    
    @property
    def is_improving(self) -> Optional[bool]:
        """判断覆盖率是否在改善"""
        # 需要与历史数据比较
        # 这里返回None表示需要额外查询
        return None
    
    def get_coverage_by_type(self, coverage_type: CoverageType) -> float:
        """根据类型获取覆盖率"""
        coverage_map = {
            CoverageType.LINE: self.line_coverage,
            CoverageType.BRANCH: self.branch_coverage,
            CoverageType.FUNCTION: self.function_coverage,
            CoverageType.STATEMENT: self.statement_coverage,
            CoverageType.CONDITION: self.condition_coverage
        }
        return coverage_map.get(coverage_type, 0.0)


class FileCoverage(BaseModel):
    """文件覆盖率模型"""
    __tablename__ = "file_coverages"
    
    # 关联信息
    coverage_metric_id = Column(Integer, ForeignKey("coverage_metrics.id"), nullable=False)
    
    # 文件信息
    file_path = Column(String(500), nullable=False, comment="文件路径")
    file_name = Column(String(200), nullable=False, comment="文件名")
    package_name = Column(String(200), comment="包名/模块名")
    
    # 覆盖率数据
    line_coverage = Column(Float, default=0.0, comment="行覆盖率")
    branch_coverage = Column(Float, default=0.0, comment="分支覆盖率")
    function_coverage = Column(Float, default=0.0, comment="函数覆盖率")
    
    # 统计数据
    total_lines = Column(Integer, default=0, comment="总行数")
    covered_lines = Column(Integer, default=0, comment="覆盖行数")
    uncovered_lines = Column(Text, comment="未覆盖行号列表")
    
    # 关联关系
    coverage_metric = relationship("CoverageMetric", back_populates="file_coverages")
    
    @property
    def coverage_level(self) -> CoverageLevel:
        """获取文件覆盖率等级"""
        if self.line_coverage >= 90:
            return CoverageLevel.EXCELLENT
        elif self.line_coverage >= 80:
            return CoverageLevel.GOOD
        elif self.line_coverage >= 70:
            return CoverageLevel.FAIR
        elif self.line_coverage >= 60:
            return CoverageLevel.POOR
        else:
            return CoverageLevel.CRITICAL


class CoverageTarget(BaseModel):
    """覆盖率目标模型"""
    __tablename__ = "coverage_targets"
    
    # 关联信息
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    
    # 目标设置
    target_line_coverage = Column(Float, default=80.0, comment="目标行覆盖率")
    target_branch_coverage = Column(Float, default=75.0, comment="目标分支覆盖率")
    target_function_coverage = Column(Float, default=85.0, comment="目标函数覆盖率")
    
    # 配置信息
    is_active = Column(Boolean, default=True, comment="是否启用")
    alert_threshold = Column(Float, default=5.0, comment="告警阈值(百分比)")
    
    # 关联关系
    project = relationship("Project", back_populates="coverage_targets")
    
    def check_target_achievement(self, metric: CoverageMetric) -> Dict[str, bool]:
        """检查目标达成情况"""
        return {
            "line_coverage": metric.line_coverage >= self.target_line_coverage,
            "branch_coverage": metric.branch_coverage >= self.target_branch_coverage,
            "function_coverage": metric.function_coverage >= self.target_function_coverage,
            "overall": (
                metric.line_coverage >= self.target_line_coverage and
                metric.branch_coverage >= self.target_branch_coverage and
                metric.function_coverage >= self.target_function_coverage
            )
        }
    
    def get_coverage_gaps(self, metric: CoverageMetric) -> Dict[str, float]:
        """获取覆盖率差距"""
        return {
            "line_coverage": max(0, self.target_line_coverage - metric.line_coverage),
            "branch_coverage": max(0, self.target_branch_coverage - metric.branch_coverage),
            "function_coverage": max(0, self.target_function_coverage - metric.function_coverage)
        }


# Pydantic模型用于API序列化
class CoverageMetricResponse(PydanticBaseModel):
    """覆盖率指标响应模型"""
    id: int
    project_id: int
    branch_name: str
    line_coverage: float
    branch_coverage: float
    function_coverage: float
    overall_coverage: float
    coverage_level: str
    measurement_date: datetime
    source: str
    
    class Config:
        from_attributes = True


class CoverageTrendData(PydanticBaseModel):
    """覆盖率趋势数据模型"""
    date: str
    line_coverage: float
    branch_coverage: float
    function_coverage: float
    overall_coverage: float
    project_name: Optional[str] = None


class CoverageDistributionData(PydanticBaseModel):
    """覆盖率分布数据模型"""
    level: str
    count: int
    percentage: float
    projects: List[str] = Field(default_factory=list)


class CoverageStatsData(PydanticBaseModel):
    """覆盖率统计数据模型"""
    total_projects: int
    average_line_coverage: float
    average_branch_coverage: float
    average_function_coverage: float
    excellent_projects: int
    good_projects: int
    fair_projects: int
    poor_projects: int
    critical_projects: int
    latest_measurement_date: Optional[datetime] = None
