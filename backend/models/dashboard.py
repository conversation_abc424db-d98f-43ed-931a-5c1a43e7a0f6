"""
质量大盘相关数据模型
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, Enum, Boolean, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from .base import BaseModel


class ProjectStatus(enum.Enum):
    """项目状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ARCHIVED = "archived"


class QualityLevel(enum.Enum):
    """质量等级枚举"""
    A = "A"
    B = "B"
    C = "C"
    D = "D"


class ChangeType(enum.Enum):
    """变化类型枚举"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"


class Project(BaseModel):
    """项目模型"""
    __tablename__ = "projects"

    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text)
    status = Column(Enum(ProjectStatus), default=ProjectStatus.ACTIVE)
    team_id = Column(Integer, ForeignKey("teams.id"))

    # 关联关系
    team = relationship("Team", back_populates="projects")
    metric_cards = relationship("MetricCard", back_populates="project")
    quality_trends = relationship("QualityTrend", back_populates="project")
    defects = relationship("Defect", back_populates="project")
    coverage_metrics = relationship("CoverageMetric", back_populates="project")
    coverage_targets = relationship("CoverageTarget", back_populates="project")

    # 预警系统关联关系
    alert_rules = relationship("AlertRule", back_populates="project")
    alerts = relationship("Alert", back_populates="project")
    quality_metrics = relationship("QualityMetric", back_populates="project")


class Team(BaseModel):
    """团队模型"""
    __tablename__ = "teams"
    
    name = Column(String(100), nullable=False, unique=True, index=True)
    description = Column(Text)
    lead_name = Column(String(50))
    member_count = Column(Integer, default=0)
    
    # 质量指标
    interface_coverage = Column(Float, default=0.0)
    main_path_coverage = Column(Float, default=0.0)
    quality_gate_pass_rate = Column(Float, default=0.0)
    avg_response_time = Column(Float, default=0.0)  # 毫秒
    quality_score = Column(Enum(QualityLevel), default=QualityLevel.C)
    
    # 关联关系
    projects = relationship("Project", back_populates="team")

    # 预警系统关联关系
    alert_rules = relationship("AlertRule", back_populates="team")
    alerts = relationship("Alert", back_populates="team")


class MetricCard(BaseModel):
    """指标卡片模型"""
    __tablename__ = "metric_cards"
    
    project_id = Column(Integer, ForeignKey("projects.id"))
    title = Column(String(100), nullable=False)
    value = Column(String(50), nullable=False)
    progress = Column(Float, nullable=False)  # 0-100
    target = Column(String(50))
    change = Column(String(50))
    change_type = Column(Enum(ChangeType), default=ChangeType.NEUTRAL)
    metric_type = Column(String(50))  # coverage, performance, quality_gate, efficiency
    
    # 关联关系
    project = relationship("Project", back_populates="metric_cards")


class QualityTrend(BaseModel):
    """质量趋势模型"""
    __tablename__ = "quality_trends"

    project_id = Column(Integer, ForeignKey("projects.id"))
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    record_date = Column(DateTime(timezone=True), server_default=func.now())
    period_type = Column(String(20), default="daily")  # daily, weekly, monthly

    # 关联关系
    project = relationship("Project", back_populates="quality_trends")


class User(BaseModel):
    """用户模型"""
    __tablename__ = "users"

    username = Column(String(50), nullable=False, unique=True, index=True)
    email = Column(String(100), nullable=False, unique=True)
    full_name = Column(String(100))
    role = Column(String(50), default="user")  # admin, manager, user, developer, tester
    is_active = Column(Boolean, default=True)

    # 关联关系
    dashboard_configs = relationship("DashboardConfig", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"


class DashboardConfig(BaseModel):
    """仪表板配置模型"""
    __tablename__ = "dashboard_configs"

    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    config_name = Column(String(100), nullable=False)  # 配置名称
    is_default = Column(Boolean, default=False)  # 是否为默认配置
    layout_config = Column(JSON)  # 布局配置JSON
    widget_configs = Column(JSON)  # 组件配置JSON
    focused_metric_ids = Column(JSON)  # 用户关注的指标ID列表，例如 ["coverage_rate", "defect_density"]

    # 关联关系
    user = relationship("User", back_populates="dashboard_configs")


class DashboardWidget(BaseModel):
    """仪表板组件模型"""
    __tablename__ = "dashboard_widgets"

    widget_type = Column(String(50), nullable=False)  # metric-card, trend-chart, etc.
    widget_title = Column(String(100), nullable=False)
    widget_config = Column(JSON)  # 组件特定配置
    is_active = Column(Boolean, default=True)
    category = Column(String(50))  # 组件分类
    description = Column(Text)  # 组件描述
