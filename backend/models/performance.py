"""
性能监控相关数据模型
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from .base import BaseModel


class MetricStatus(enum.Enum):
    """指标状态枚举"""
    EXCELLENT = "excellent"
    GOOD = "good"
    WARNING = "warning"
    DANGER = "danger"


class TrendDirection(enum.Enum):
    """趋势方向枚举"""
    UP = "up"
    DOWN = "down"
    STABLE = "stable"


class PerformanceMetric(BaseModel):
    """性能指标模型"""
    __tablename__ = "performance_metrics"
    
    project_id = Column(Integer, ForeignKey("projects.id"))
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20), nullable=False)  # ms, %, req/s, MB, etc.
    
    # 指标状态和趋势
    status = Column(Enum(MetricStatus), default=MetricStatus.GOOD)
    trend = Column(Enum(TrendDirection), default=TrendDirection.STABLE)
    threshold = Column(String(50))  # 阈值描述
    
    # 时间维度
    record_time = Column(DateTime(timezone=True), server_default=func.now())
    period_type = Column(String(20), default="realtime")  # realtime, hourly, daily


class ServiceMetric(BaseModel):
    """服务性能指标模型"""
    __tablename__ = "service_metrics"
    
    service_name = Column(String(100), nullable=False, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    
    # 响应时间指标
    avg_response_time = Column(Float, nullable=False)  # 平均响应时间(ms)
    p50_response_time = Column(Float)  # 50分位响应时间
    p95_response_time = Column(Float)  # 95分位响应时间
    p99_response_time = Column(Float)  # 99分位响应时间
    
    # 吞吐量和错误率
    throughput = Column(Float, nullable=False)  # 吞吐量(req/s)
    error_rate = Column(Float, default=0.0)     # 错误率(%)
    availability = Column(Float, default=100.0) # 可用性(%)
    
    # 时间维度
    record_time = Column(DateTime(timezone=True), server_default=func.now())


class SystemMetric(BaseModel):
    """系统性能指标模型"""
    __tablename__ = "system_metrics"
    
    host_name = Column(String(100), nullable=False, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    
    # CPU指标
    cpu_usage = Column(Float, nullable=False)      # CPU使用率(%)
    cpu_load_1m = Column(Float)                    # 1分钟负载
    cpu_load_5m = Column(Float)                    # 5分钟负载
    cpu_load_15m = Column(Float)                   # 15分钟负载
    
    # 内存指标
    memory_usage = Column(Float, nullable=False)   # 内存使用率(%)
    memory_total = Column(Float)                   # 总内存(MB)
    memory_used = Column(Float)                    # 已用内存(MB)
    memory_available = Column(Float)               # 可用内存(MB)
    
    # 磁盘指标
    disk_usage = Column(Float)                     # 磁盘使用率(%)
    disk_io_read = Column(Float)                   # 磁盘读取速率(MB/s)
    disk_io_write = Column(Float)                  # 磁盘写入速率(MB/s)
    
    # 网络指标
    network_in = Column(Float)                     # 网络入流量(MB/s)
    network_out = Column(Float)                    # 网络出流量(MB/s)
    
    # 时间维度
    record_time = Column(DateTime(timezone=True), server_default=func.now())
