"""
质量门禁相关数据模型
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, Enum, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from .base import BaseModel


class RuleStatus(enum.Enum):
    """规则状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"


class GateStatus(enum.Enum):
    """门禁状态枚举"""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    PENDING = "pending"


class OperatorType(enum.Enum):
    """操作符类型枚举"""
    GREATER_THAN = ">="
    LESS_THAN = "<="
    GREATER = ">"
    LESS = "<"
    EQUAL = "=="
    NOT_EQUAL = "!="


class QualityGateRule(BaseModel):
    """质量门禁规则模型"""
    __tablename__ = "quality_gate_rules"
    
    rule_name = Column(String(100), nullable=False)
    description = Column(Text)
    project_id = Column(Integer, ForeignKey("projects.id"))
    
    # 规则配置
    metric_name = Column(String(100), nullable=False)  # 指标名称
    operator = Column(Enum(OperatorType), nullable=False)  # 操作符
    threshold = Column(Float, nullable=False)  # 阈值
    weight = Column(Float, default=1.0)  # 权重
    
    # 规则状态
    status = Column(Enum(RuleStatus), default=RuleStatus.ACTIVE)
    is_blocking = Column(Boolean, default=True)  # 是否阻塞
    
    # 关联关系
    executions = relationship("QualityGateExecution", back_populates="rule")


class QualityGateExecution(BaseModel):
    """质量门禁执行模型"""
    __tablename__ = "quality_gate_executions"
    
    project_id = Column(Integer, ForeignKey("projects.id"))
    execution_id = Column(String(100), nullable=False, index=True)  # 批次执行ID
    
    # 执行信息
    trigger_type = Column(String(50))  # manual, scheduled, webhook
    trigger_user = Column(String(100))
    execution_time = Column(DateTime(timezone=True), server_default=func.now())
    
    # 执行结果
    overall_status = Column(Enum(GateStatus), nullable=False)
    overall_score = Column(Float, default=0.0)  # 总体得分
    total_rules = Column(Integer, default=0)
    passed_rules = Column(Integer, default=0)
    failed_rules = Column(Integer, default=0)
    warning_rules = Column(Integer, default=0)
    
    # 执行环境
    environment = Column(String(50))  # dev, test, staging, prod
    build_number = Column(String(100))
    commit_hash = Column(String(100))
    
    # 关联关系
    rule_id = Column(Integer, ForeignKey("quality_gate_rules.id"))
    rule = relationship("QualityGateRule", back_populates="executions")
    results = relationship("QualityGateResult", back_populates="execution")


class QualityGateResult(BaseModel):
    """质量门禁结果详情模型"""
    __tablename__ = "quality_gate_results"
    
    execution_id = Column(Integer, ForeignKey("quality_gate_executions.id"))
    rule_id = Column(Integer, ForeignKey("quality_gate_rules.id"))
    
    # 结果详情
    actual_value = Column(Float, nullable=False)  # 实际值
    expected_value = Column(Float, nullable=False)  # 期望值
    status = Column(Enum(GateStatus), nullable=False)
    score = Column(Float, default=0.0)  # 单项得分
    
    # 详细信息
    message = Column(Text)  # 结果描述
    details = Column(Text)  # JSON格式的详细信息
    
    # 关联关系
    execution = relationship("QualityGateExecution", back_populates="results")
