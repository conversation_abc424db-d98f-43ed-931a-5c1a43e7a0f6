"""
自动化测试相关数据模型
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Text, ForeignKey, Enum, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum
from .base import BaseModel


class TestCaseType(enum.Enum):
    """测试用例类型枚举"""
    API = "api"
    UI = "ui"
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"


class TestCaseStatus(enum.Enum):
    """测试用例状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"


class ExecutionStatus(enum.Enum):
    """执行状态枚举"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class TestCase(BaseModel):
    """测试用例模型"""
    __tablename__ = "test_cases"
    
    name = Column(String(200), nullable=False)
    description = Column(Text)
    test_type = Column(Enum(TestCaseType), nullable=False)
    status = Column(Enum(TestCaseStatus), default=TestCaseStatus.ACTIVE)
    project_id = Column(Integer, ForeignKey("projects.id"))
    
    # 测试用例属性
    is_automated = Column(Boolean, default=False)
    priority = Column(String(20), default="medium")  # high, medium, low
    tags = Column(Text)  # JSON格式存储标签
    estimated_duration = Column(Integer)  # 预估执行时间（秒）
    
    # 关联关系
    executions = relationship("TestExecution", back_populates="test_case")


class TestExecution(BaseModel):
    """测试执行模型"""
    __tablename__ = "test_executions"
    
    test_case_id = Column(Integer, ForeignKey("test_cases.id"))
    execution_id = Column(String(100), nullable=False)  # 批次执行ID
    status = Column(Enum(ExecutionStatus), nullable=False)
    
    # 执行详情
    start_time = Column(DateTime(timezone=True))
    end_time = Column(DateTime(timezone=True))
    duration = Column(Float)  # 执行时长（秒）
    error_message = Column(Text)
    
    # 环境信息
    environment = Column(String(50))  # dev, test, staging, prod
    executor = Column(String(100))  # 执行者
    
    # 关联关系
    test_case = relationship("TestCase", back_populates="executions")
    results = relationship("TestResult", back_populates="execution")


class TestResult(BaseModel):
    """测试结果详情模型"""
    __tablename__ = "test_results"
    
    execution_id = Column(Integer, ForeignKey("test_executions.id"))
    step_name = Column(String(200))
    step_status = Column(Enum(ExecutionStatus))
    step_duration = Column(Float)
    assertion_details = Column(Text)  # JSON格式存储断言详情
    screenshot_path = Column(String(500))
    
    # 关联关系
    execution = relationship("TestExecution", back_populates="results")


class AutomationMetric(BaseModel):
    """自动化测试指标模型"""
    __tablename__ = "automation_metrics"
    
    project_id = Column(Integer, ForeignKey("projects.id"))
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20))  # %, ms, count, etc.
    
    # 指标状态
    status = Column(String(20))  # excellent, good, warning, danger
    trend = Column(String(20))   # up, down, stable
    threshold = Column(Float)
    
    # 时间维度
    record_date = Column(DateTime(timezone=True), server_default=func.now())
    period_type = Column(String(20), default="daily")  # daily, weekly, monthly
