#!/usr/bin/env python3
"""
测试修复后的API端点
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_api_endpoint(endpoint, params=None):
    """测试单个API端点"""
    url = f"{BASE_URL}{endpoint}"
    try:
        response = requests.get(url, params=params)
        status = response.status_code
        
        if status == 200:
            data = response.json()
            print(f"✅ {endpoint}")
            print(f"   状态码: {status}")
            print(f"   成功: {data.get('success', False)}")
            if 'data' in data:
                print(f"   数据条数: {len(data['data']) if isinstance(data['data'], list) else 1}")
            print()
            return True
        else:
            print(f"❌ {endpoint}")
            print(f"   状态码: {status}")
            try:
                error_data = response.json()
                print(f"   错误: {error_data}")
            except:
                print(f"   错误: {response.text}")
            print()
            return False
            
    except Exception as e:
        print(f"❌ {endpoint}")
        print(f"   连接错误: {str(e)}")
        print()
        return False

def main():
    """主测试函数"""
    print("🚀 测试修复后的API端点...")
    print("=" * 50)
    
    # 测试参数（模拟前端发送的参数）
    test_params = {
        "page": 1,
        "pageSize": 20,
        "sortBy": "created_at",
        "sortOrder": "desc",
        "dateRange": "7d"
    }
    
    # 要测试的关键API端点
    test_endpoints = [
        "/api/projects",
        "/api/teams",
        "/api/projects/1",
        "/api/teams/1"
    ]
    
    success_count = 0
    total_count = len(test_endpoints)
    
    for endpoint in test_endpoints:
        # 对于详情接口不传递查询参数
        params = test_params if not endpoint.split('/')[-1].isdigit() else None
        success = test_api_endpoint(endpoint, params)
        if success:
            success_count += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 个端点正常工作")
    
    if success_count == total_count:
        print("🎉 所有关键API端点修复成功！")
    else:
        print(f"⚠️  还有 {total_count - success_count} 个端点需要进一步修复")

if __name__ == "__main__":
    print("请确保后端服务已启动 (http://localhost:8000)")
    print()
    
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
