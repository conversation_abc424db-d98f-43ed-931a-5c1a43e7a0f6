#!/usr/bin/env python3
"""
日志配置测试脚本
用于验证日志配置是否正常工作
"""

import os
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.logging_utils import setup_logging, get_logger, log_api_access


def test_basic_logging():
    """测试基本日志功能"""
    print("=== 测试基本日志功能 ===")
    
    # 初始化日志配置
    setup_logging()
    
    # 获取不同的日志记录器
    main_logger = get_logger('main')
    db_logger = get_logger('database')
    service_logger = get_logger('services')
    
    # 测试不同级别的日志
    main_logger.debug("这是调试信息 - 通常在开发环境显示")
    main_logger.info("这是信息日志 - 记录正常操作")
    main_logger.warning("这是警告信息 - 记录潜在问题")
    main_logger.error("这是错误信息 - 记录运行错误")
    
    # 测试不同模块的日志
    db_logger.info("数据库连接成功")
    service_logger.info("服务初始化完成")
    
    print("基本日志测试完成，请检查控制台输出和日志文件")


def test_api_access_logging():
    """测试API访问日志"""
    print("\n=== 测试API访问日志 ===")
    
    # 模拟API访问
    test_requests = [
        ("GET", "/api/dashboard/overview", 200, 45.67),
        ("POST", "/api/data/upload", 201, 123.45),
        ("GET", "/api/dashboard/trends", 200, 78.90),
        ("DELETE", "/api/data/123", 404, 12.34),
        ("PUT", "/api/config/settings", 500, 234.56),
    ]
    
    for method, path, status_code, response_time in test_requests:
        log_api_access(
            method=method,
            path=path,
            status_code=status_code,
            response_time=response_time,
            client_ip="127.0.0.1",
            user_agent="TestAgent/1.0"
        )
        time.sleep(0.1)  # 短暂延迟
    
    print("API访问日志测试完成，请检查access.log文件")


def test_error_logging():
    """测试错误日志"""
    print("\n=== 测试错误日志 ===")
    
    logger = get_logger('test')
    
    try:
        # 故意触发异常
        result = 1 / 0
    except ZeroDivisionError as e:
        logger.error(f"捕获到除零错误: {str(e)}")
        logger.exception("详细异常信息:")
    
    try:
        # 故意触发另一个异常
        data = {"key": "value"}
        value = data["nonexistent_key"]
    except KeyError as e:
        logger.error(f"捕获到键错误: {str(e)}")
    
    print("错误日志测试完成，请检查error.log文件")


def test_different_log_levels():
    """测试不同日志级别"""
    print("\n=== 测试不同日志级别 ===")
    
    # 测试DEBUG级别
    print("设置日志级别为DEBUG...")
    os.environ['LOG_LEVEL'] = 'DEBUG'
    setup_logging()
    
    debug_logger = get_logger('debug_test')
    debug_logger.debug("DEBUG级别日志 - 应该显示")
    debug_logger.info("INFO级别日志 - 应该显示")
    debug_logger.warning("WARNING级别日志 - 应该显示")
    
    # 测试WARNING级别
    print("设置日志级别为WARNING...")
    os.environ['LOG_LEVEL'] = 'WARNING'
    setup_logging()
    
    warning_logger = get_logger('warning_test')
    warning_logger.debug("DEBUG级别日志 - 不应该显示")
    warning_logger.info("INFO级别日志 - 不应该显示")
    warning_logger.warning("WARNING级别日志 - 应该显示")
    warning_logger.error("ERROR级别日志 - 应该显示")
    
    # 恢复默认级别
    os.environ['LOG_LEVEL'] = 'INFO'
    setup_logging()
    
    print("日志级别测试完成")


def check_log_files():
    """检查日志文件是否创建"""
    print("\n=== 检查日志文件 ===")
    
    log_dir = Path(__file__).parent / "logs"
    expected_files = ["app.log", "error.log", "debug.log", "access.log"]
    
    print(f"日志目录: {log_dir}")
    
    for filename in expected_files:
        filepath = log_dir / filename
        if filepath.exists():
            size = filepath.stat().st_size
            print(f"✓ {filename} - 存在 ({size} bytes)")
        else:
            print(f"✗ {filename} - 不存在")
    
    print("日志文件检查完成")


def main():
    """主测试函数"""
    print("质量大盘后端日志配置测试")
    print("=" * 50)
    
    try:
        test_basic_logging()
        test_api_access_logging()
        test_error_logging()
        test_different_log_levels()
        check_log_files()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("请检查以下位置的日志文件:")
        print(f"- 日志目录: {Path(__file__).parent / 'logs'}")
        print("- 控制台输出应该显示相应的日志信息")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
