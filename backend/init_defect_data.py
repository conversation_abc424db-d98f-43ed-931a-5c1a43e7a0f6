"""
初始化缺陷管理模拟数据
"""

import asyncio
from datetime import datetime, timedelta
import random
from database import AsyncSessionLocal, init_db
from models.defect import Defect, DefectSeverity, DefectPriority, DefectStatus, DefectType
from models.dashboard import User
from models.dashboard import Project, Team, ProjectStatus


async def create_sample_users():
    """创建示例用户"""
    users_data = [
        {"username": "zhang_san", "email": "<EMAIL>", "full_name": "张三", "role": "developer"},
        {"username": "li_si", "email": "<EMAIL>", "full_name": "李四", "role": "tester"},
        {"username": "wang_wu", "email": "<EMAIL>", "full_name": "王五", "role": "developer"},
        {"username": "zhao_liu", "email": "<EMAIL>", "full_name": "赵六", "role": "manager"},
        {"username": "qian_qi", "email": "<EMAIL>", "full_name": "钱七", "role": "tester"},
    ]
    
    async with AsyncSessionLocal() as session:
        for user_data in users_data:
            user = User(**user_data)
            session.add(user)
        
        await session.commit()
        print(f"创建了 {len(users_data)} 个用户")


async def create_sample_teams_and_projects():
    """创建示例团队和项目"""
    teams_data = [
        {"name": "用户中心团队", "description": "负责用户管理相关功能", "lead_name": "张三", "member_count": 8},
        {"name": "订单系统团队", "description": "负责订单处理相关功能", "lead_name": "李四", "member_count": 6},
        {"name": "支付系统团队", "description": "负责支付处理相关功能", "lead_name": "王五", "member_count": 5},
    ]
    
    projects_data = [
        {"name": "用户中心", "description": "用户注册、登录、个人信息管理", "status": ProjectStatus.ACTIVE},
        {"name": "订单管理", "description": "订单创建、查询、状态管理", "status": ProjectStatus.ACTIVE},
        {"name": "支付网关", "description": "支付接口集成和处理", "status": ProjectStatus.ACTIVE},
        {"name": "数据分析", "description": "业务数据分析和报表", "status": ProjectStatus.ACTIVE},
    ]
    
    async with AsyncSessionLocal() as session:
        # 创建团队
        teams = []
        for team_data in teams_data:
            team = Team(**team_data)
            session.add(team)
            teams.append(team)
        
        await session.flush()  # 获取团队ID
        
        # 创建项目
        for i, project_data in enumerate(projects_data):
            team_id = teams[i % len(teams)].id if i < len(teams) else teams[0].id
            project = Project(**project_data, team_id=team_id)
            session.add(project)
        
        await session.commit()
        print(f"创建了 {len(teams_data)} 个团队和 {len(projects_data)} 个项目")


async def create_sample_defects():
    """创建示例缺陷数据"""
    async with AsyncSessionLocal() as session:
        # 获取项目和用户ID
        from sqlalchemy import select
        
        projects_result = await session.execute(select(Project.id))
        project_ids = [row[0] for row in projects_result.fetchall()]
        
        users_result = await session.execute(select(User.id))
        user_ids = [row[0] for row in users_result.fetchall()]
        
        if not project_ids or not user_ids:
            print("请先创建项目和用户数据")
            return
        
        # 缺陷模板数据
        defect_templates = [
            {
                "title": "用户登录页面响应缓慢",
                "description": "用户在登录页面输入用户名密码后，点击登录按钮响应时间超过5秒",
                "severity": DefectSeverity.HIGH,
                "priority": DefectPriority.HIGH,
                "defect_type": DefectType.PERFORMANCE,
                "environment": "生产环境",
                "version": "v2.1.0"
            },
            {
                "title": "订单列表页面显示异常",
                "description": "订单列表页面在某些情况下显示空白，无法加载订单数据",
                "severity": DefectSeverity.CRITICAL,
                "priority": DefectPriority.URGENT,
                "defect_type": DefectType.FUNCTIONAL,
                "environment": "测试环境",
                "version": "v2.0.5"
            },
            {
                "title": "支付按钮样式错乱",
                "description": "在移动端浏览器中，支付按钮的样式显示错乱，影响用户体验",
                "severity": DefectSeverity.MEDIUM,
                "priority": DefectPriority.MEDIUM,
                "defect_type": DefectType.UI,
                "environment": "移动端",
                "version": "v2.1.0"
            },
            {
                "title": "数据导出功能失效",
                "description": "用户点击数据导出按钮后，无法下载Excel文件",
                "severity": DefectSeverity.HIGH,
                "priority": DefectPriority.HIGH,
                "defect_type": DefectType.FUNCTIONAL,
                "environment": "生产环境",
                "version": "v2.0.8"
            },
            {
                "title": "用户头像上传失败",
                "description": "用户尝试上传头像时，系统提示上传失败，但没有具体错误信息",
                "severity": DefectSeverity.MEDIUM,
                "priority": DefectPriority.MEDIUM,
                "defect_type": DefectType.FUNCTIONAL,
                "environment": "测试环境",
                "version": "v2.1.0"
            },
            {
                "title": "搜索功能返回结果不准确",
                "description": "用户搜索特定关键词时，返回的结果与关键词不匹配",
                "severity": DefectSeverity.LOW,
                "priority": DefectPriority.LOW,
                "defect_type": DefectType.FUNCTIONAL,
                "environment": "测试环境",
                "version": "v2.0.9"
            },
            {
                "title": "密码重置邮件发送延迟",
                "description": "用户申请密码重置后，邮件发送存在明显延迟，有时需要等待10分钟以上",
                "severity": DefectSeverity.MEDIUM,
                "priority": DefectPriority.MEDIUM,
                "defect_type": DefectType.PERFORMANCE,
                "environment": "生产环境",
                "version": "v2.0.7"
            },
            {
                "title": "数据库连接超时",
                "description": "在高并发情况下，系统出现数据库连接超时错误",
                "severity": DefectSeverity.CRITICAL,
                "priority": DefectPriority.URGENT,
                "defect_type": DefectType.PERFORMANCE,
                "environment": "生产环境",
                "version": "v2.1.0"
            }
        ]
        
        # 生成缺陷数据
        defects = []
        now = datetime.now()
        
        for i in range(50):  # 创建50个缺陷
            template = random.choice(defect_templates)
            
            # 随机生成发现时间（过去30天内）
            found_date = now - timedelta(days=random.randint(0, 30))
            
            # 随机决定是否已解决
            is_resolved = random.choice([True, False, False])  # 1/3概率已解决
            
            if is_resolved:
                status = random.choice([DefectStatus.RESOLVED, DefectStatus.CLOSED])
                resolved_date = found_date + timedelta(days=random.randint(1, 10))
            else:
                status = random.choice([DefectStatus.OPEN, DefectStatus.IN_PROGRESS, DefectStatus.REOPENED])
                resolved_date = None
            
            defect = Defect(
                title=f"{template['title']} #{i+1}",
                description=template['description'],
                severity=template['severity'],
                priority=template['priority'],
                status=status,
                defect_type=template['defect_type'],
                project_id=random.choice(project_ids),
                assignee_id=random.choice(user_ids) if random.random() > 0.2 else None,  # 80%概率有处理人
                reporter_id=random.choice(user_ids),
                found_date=found_date,
                resolved_date=resolved_date,
                environment=template['environment'],
                version=template['version'],
                steps_to_reproduce="1. 打开页面\n2. 执行相关操作\n3. 观察问题现象",
                expected_result="系统应该正常工作",
                actual_result="出现了异常行为"
            )
            
            defects.append(defect)
        
        # 批量插入
        session.add_all(defects)
        await session.commit()
        
        print(f"创建了 {len(defects)} 个缺陷记录")


async def main():
    """主函数"""
    print("开始初始化缺陷管理数据...")
    
    # 初始化数据库表
    await init_db()
    print("数据库表初始化完成")
    
    # 创建示例数据
    await create_sample_users()
    await create_sample_teams_and_projects()
    await create_sample_defects()
    
    print("缺陷管理数据初始化完成！")


if __name__ == "__main__":
    asyncio.run(main())
