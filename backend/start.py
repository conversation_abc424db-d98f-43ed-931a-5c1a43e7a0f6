#!/usr/bin/env python3
"""
质量大盘后端启动脚本
使用 uv 包管理器启动 FastAPI 应用
"""

import subprocess
import sys
import os
from pathlib import Path

def check_uv_installed():
    """检查 uv 是否已安装"""
    try:
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ uv 已安装: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_uv():
    """安装 uv 包管理器"""
    print("正在安装 uv 包管理器...")
    try:
        if os.name == 'nt':  # Windows
            subprocess.run([
                "powershell", "-Command",
                "irm https://astral.sh/uv/install.ps1 | iex"
            ], check=True)
        else:  # Unix/Linux/macOS
            subprocess.run([
                "curl", "-LsSf", "https://astral.sh/uv/install.sh", "|", "sh"
            ], shell=True, check=True)
        print("✓ uv 安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ uv 安装失败: {e}")
        return False

def setup_project():
    """设置项目环境"""
    print("正在设置项目环境...")
    
    # 确保在正确的目录
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)
    
    try:
        # 同步依赖
        print("正在同步项目依赖...")
        subprocess.run(["uv", "sync"], check=True)
        print("✓ 依赖同步完成")
        
        # 安装开发依赖
        print("正在安装开发依赖...")
        subprocess.run(["uv", "sync", "--extra", "dev"], check=True)
        print("✓ 开发依赖安装完成")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 项目设置失败: {e}")
        return False

def start_server():
    """启动 FastAPI 服务器"""
    print("正在启动 FastAPI 服务器...")
    print("服务器将在 http://localhost:8000 启动")
    print("API 文档: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        # 使用 uv 运行应用
        subprocess.run([
            "uv", "run", "uvicorn", "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--reload-dir", ".",
            "--log-level", "info"
        ], check=True)
    except subprocess.CalledProcessError as e:
        print(f"✗ 服务器启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n✓ 服务器已停止")
        return True

def main():
    """主函数"""
    print("=" * 60)
    print("质量大盘后端服务启动器")
    print("=" * 60)
    
    # 检查 uv 是否安装
    if not check_uv_installed():
        print("✗ uv 未安装")
        install_choice = input("是否自动安装 uv? (y/n): ").lower().strip()
        if install_choice in ['y', 'yes', '是']:
            if not install_uv():
                print("请手动安装 uv: https://docs.astral.sh/uv/getting-started/installation/")
                sys.exit(1)
        else:
            print("请手动安装 uv: https://docs.astral.sh/uv/getting-started/installation/")
            sys.exit(1)
    
    # 设置项目
    if not setup_project():
        sys.exit(1)
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
