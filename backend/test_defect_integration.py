#!/usr/bin/env python3
"""
缺陷管理模块集成测试脚本
测试前后端联调和API功能
"""

import urllib.request
import urllib.error
import json
from datetime import datetime
import time

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_ENDPOINTS = [
    "/api/defects?page=1&pageSize=10",
    "/api/defects/trends?date_range=30d&group_by=day",
    "/api/defects/distribution?dimension=severity",
    "/api/defects/stats"
]

def test_api_endpoint(endpoint):
    """测试单个API端点"""
    url = f"{BASE_URL}{endpoint}"
    print(f"\n🔍 测试端点: {endpoint}")

    try:
        with urllib.request.urlopen(url, timeout=10) as response:
            status = response.getcode()
            content_type = response.headers.get('content-type', '')

            if status == 200:
                if 'application/json' in content_type:
                    data = json.loads(response.read().decode('utf-8'))
                    print(f"✅ 状态码: {status}")
                    print(f"📄 响应类型: {content_type}")

                    # 检查响应结构
                    if isinstance(data, dict):
                        if 'success' in data:
                            print(f"🎯 成功标志: {data.get('success')}")
                        if 'data' in data:
                            data_content = data['data']
                            if isinstance(data_content, dict):
                                print(f"📊 数据字段: {list(data_content.keys())}")
                            elif isinstance(data_content, list):
                                print(f"📊 数据数量: {len(data_content)}")

                        # 打印部分响应数据（用于调试）
                        print(f"📋 响应预览: {json.dumps(data, indent=2, ensure_ascii=False, default=str)[:500]}...")
                    else:
                        print(f"📋 响应数据: {str(data)[:200]}...")
                else:
                    text = response.read().decode('utf-8')
                    print(f"⚠️  非JSON响应: {text[:200]}...")
            else:
                print(f"❌ 状态码: {status}")

    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} - {e.reason}")
        try:
            error_text = e.read().decode('utf-8')
            print(f"💥 错误信息: {error_text}")
        except:
            pass
    except urllib.error.URLError as e:
        print(f"🔌 连接错误: {str(e)}")
    except json.JSONDecodeError as e:
        print(f"📄 JSON解析错误: {str(e)}")
    except Exception as e:
        print(f"💥 未知错误: {str(e)}")

def test_server_health():
    """测试服务器健康状态"""
    print("🏥 检查服务器健康状态...")

    try:
        with urllib.request.urlopen(f"{BASE_URL}/", timeout=5) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode('utf-8'))
                print(f"✅ 服务器运行正常")
                print(f"📋 服务器信息: {data}")
                return True
            else:
                print(f"❌ 服务器响应异常: {response.getcode()}")
                return False
    except Exception as e:
        print(f"💥 服务器连接失败: {str(e)}")
        return False

def test_defect_apis():
    """测试所有缺陷管理API端点"""
    print("🚀 开始缺陷管理API集成测试")
    print("=" * 60)

    # 检查服务器健康状态
    if not test_server_health():
        print("❌ 服务器不可用，测试终止")
        return

    print("\n📡 开始API端点测试...")

    # 测试所有端点
    for endpoint in TEST_ENDPOINTS:
        test_api_endpoint(endpoint)
        time.sleep(0.5)  # 避免请求过快

    print("\n" + "=" * 60)
    print("🎉 缺陷管理API集成测试完成")

def test_frontend_integration():
    """测试前端集成相关的API调用"""
    print("\n🎨 测试前端集成场景...")

    # 模拟前端的典型API调用序列
    test_scenarios = [
        {
            "name": "页面初始化",
            "endpoints": [
                "/api/defects/stats",
                "/api/defects?page=1&pageSize=20&sortBy=found_date&sortOrder=desc"
            ]
        },
        {
            "name": "图表数据加载",
            "endpoints": [
                "/api/defects/trends?date_range=30d&group_by=day",
                "/api/defects/distribution?dimension=severity"
            ]
        },
        {
            "name": "筛选功能",
            "endpoints": [
                "/api/defects?page=1&pageSize=20&severity=critical",
                "/api/defects?page=1&pageSize=20&status=open"
            ]
        }
    ]

    for scenario in test_scenarios:
        print(f"\n📋 测试场景: {scenario['name']}")
        for endpoint in scenario['endpoints']:
            test_api_endpoint(endpoint)
            time.sleep(0.3)

def print_test_summary():
    """打印测试总结"""
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    print("✅ 测试项目:")
    print("   - 服务器健康检查")
    print("   - 缺陷列表API")
    print("   - 缺陷趋势API")
    print("   - 缺陷分布API")
    print("   - 缺陷统计API")
    print("   - 前端集成场景")
    print("\n💡 注意事项:")
    print("   - 确保后端服务器在 http://localhost:8000 运行")
    print("   - 检查数据库连接和数据初始化")
    print("   - 验证API响应格式与前端期望一致")
    print("\n🔧 如果测试失败:")
    print("   1. 检查后端服务器是否启动")
    print("   2. 检查数据库连接配置")
    print("   3. 运行数据初始化脚本")
    print("   4. 查看服务器日志获取详细错误信息")

def main():
    """主测试函数"""
    print("🧪 缺陷管理模块集成测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 基础API测试
        test_defect_apis()

        # 前端集成测试
        test_frontend_integration()

        # 打印测试总结
        print_test_summary()

    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    # 运行测试
    main()
