from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import re

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from database import get_db
from models.defect import Defect
from app.schemas.search import (
    SearchRequest, SearchResponse, SearchResult,
    SearchSuggestion, QuickSearch, SearchStats
)

router = APIRouter(prefix="/api/search", tags=["search"])

@router.get("/suggestions", response_model=List[SearchSuggestion])
async def get_search_suggestions(
    query: str = Query(..., min_length=1, description="搜索查询"),
    type: str = Query("all", description="搜索类型"),
    limit: int = Query(8, ge=1, le=20, description="建议数量限制"),
    db: Session = Depends(get_db)
):
    """获取搜索建议"""
    suggestions = []
    
    try:
        # 缺陷建议
        if type in ["all", "defects"]:
            defect_suggestions = await get_defect_suggestions(db, query, limit // 4)
            suggestions.extend(defect_suggestions)
        
        # 项目建议
        if type in ["all", "projects"]:
            project_suggestions = await get_project_suggestions(db, query, limit // 4)
            suggestions.extend(project_suggestions)
        
        # 用户建议
        if type in ["all", "users"]:
            user_suggestions = await get_user_suggestions(db, query, limit // 4)
            suggestions.extend(user_suggestions)
        
        # 覆盖率建议
        if type in ["all", "coverage"]:
            coverage_suggestions = await get_coverage_suggestions(db, query, limit // 4)
            suggestions.extend(coverage_suggestions)
        
        # 按相关性排序并限制数量
        suggestions = sorted(suggestions, key=lambda x: x.relevance, reverse=True)[:limit]
        
        return suggestions
        
    except Exception as e:
        # 返回空建议而不是错误，保证用户体验
        return []

@router.get("", response_model=SearchResponse)
async def search(
    query: str = Query(..., min_length=1, description="搜索查询"),
    type: str = Query("all", description="搜索类型"),
    scopes: Optional[List[str]] = Query(None, description="搜索范围"),
    time_range: Optional[str] = Query(None, description="时间范围"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    project_id: Optional[int] = Query(None, description="项目ID"),
    statuses: Optional[List[str]] = Query(None, description="状态筛选"),
    sort_by: str = Query("relevance", description="排序字段"),
    sort_order: str = Query("desc", description="排序顺序"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """执行搜索"""
    try:
        results = []
        total_count = 0
        
        # 解析时间范围
        date_filter = parse_time_range(time_range, start_date, end_date)
        
        # 根据类型执行搜索
        if type in ["all", "defects"]:
            defect_results, defect_count = await search_defects(
                db, query, date_filter, project_id, statuses, page, page_size
            )
            results.extend(defect_results)
            total_count += defect_count
        
        if type in ["all", "projects"]:
            project_results, project_count = await search_projects(
                db, query, date_filter, page, page_size
            )
            results.extend(project_results)
            total_count += project_count
        
        if type in ["all", "users"]:
            user_results, user_count = await search_users(
                db, query, page, page_size
            )
            results.extend(user_results)
            total_count += user_count
        
        if type in ["all", "coverage"]:
            coverage_results, coverage_count = await search_coverage(
                db, query, date_filter, project_id, page, page_size
            )
            results.extend(coverage_results)
            total_count += coverage_count
        
        # 排序结果
        results = sort_search_results(results, sort_by, sort_order)
        
        # 如果是全部搜索，需要重新分页
        if type == "all":
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            results = results[start_idx:end_idx]
        
        return SearchResponse(
            results=results,
            total=total_count,
            page=page,
            page_size=page_size,
            query=query,
            search_time=0  # 实际应该计算搜索耗时
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/quick-searches", response_model=List[QuickSearch])
async def get_quick_searches(db: Session = Depends(get_db)):
    """获取快捷搜索"""
    try:
        quick_searches = [
            QuickSearch(
                id=1,
                label="未解决的缺陷",
                query="status:open",
                type="defects",
                icon="fas fa-bug",
                count=await count_open_defects(db)
            ),
            QuickSearch(
                id=2,
                label="高优先级问题",
                query="priority:high",
                type="defects",
                icon="fas fa-exclamation-triangle",
                count=await count_high_priority_defects(db)
            ),
            QuickSearch(
                id=3,
                label="活跃项目",
                query="status:active",
                type="projects",
                icon="fas fa-project-diagram",
                count=await count_active_projects(db)
            ),
            QuickSearch(
                id=4,
                label="低覆盖率文件",
                query="coverage:<60",
                type="coverage",
                icon="fas fa-chart-line",
                count=await count_low_coverage_files(db)
            )
        ]
        
        return quick_searches
        
    except Exception as e:
        # 返回默认快捷搜索
        return []

@router.get("/stats", response_model=SearchStats)
async def get_search_stats(db: Session = Depends(get_db)):
    """获取搜索统计"""
    try:
        # 这里应该从数据库或缓存中获取真实的搜索统计
        # 暂时返回模拟数据
        return SearchStats(
            total_searches=1234,
            popular_queries=[
                {"query": "登录", "count": 45, "type": "defects"},
                {"query": "性能", "count": 32, "type": "all"},
                {"query": "API", "count": 28, "type": "projects"},
            ],
            search_trends=[]
        )
        
    except Exception as e:
        return SearchStats(total_searches=0, popular_queries=[], search_trends=[])

# 辅助函数
async def get_defect_suggestions(db: Session, query: str, limit: int) -> List[SearchSuggestion]:
    """获取缺陷相关建议"""
    suggestions = []
    
    # 搜索缺陷标题
    defects = db.query(Defect).filter(
        Defect.title.ilike(f"%{query}%")
    ).limit(limit).all()
    
    for defect in defects:
        suggestions.append(SearchSuggestion(
            text=defect.title,
            type="defect",
            category="缺陷管理",
            relevance=calculate_relevance(query, defect.title)
        ))
    
    return suggestions

async def get_project_suggestions(db: Session, query: str, limit: int) -> List[SearchSuggestion]:
    """获取项目相关建议"""
    suggestions = []

    # 暂时返回模拟建议
    if "项目" in query or "project" in query.lower():
        suggestions.append(SearchSuggestion(
            text=f"{query} 项目",
            type="project",
            category="项目管理",
            relevance=0.8
        ))

    return suggestions

async def get_user_suggestions(db: Session, query: str, limit: int) -> List[SearchSuggestion]:
    """获取用户相关建议"""
    suggestions = []
    
    # 搜索用户名
    users = db.query(User).filter(
        User.username.ilike(f"%{query}%") | 
        User.email.ilike(f"%{query}%")
    ).limit(limit).all()
    
    for user in users:
        suggestions.append(SearchSuggestion(
            text=user.username,
            type="user",
            category="用户管理",
            relevance=calculate_relevance(query, user.username)
        ))
    
    return suggestions

async def get_coverage_suggestions(db: Session, query: str, limit: int) -> List[SearchSuggestion]:
    """获取覆盖率相关建议"""
    suggestions = []
    
    # 这里可以搜索覆盖率相关的文件名、项目等
    # 暂时返回一些通用建议
    if "覆盖率" in query or "coverage" in query.lower():
        suggestions.append(SearchSuggestion(
            text=f"{query} 覆盖率报告",
            type="coverage",
            category="测试覆盖率",
            relevance=0.8
        ))
    
    return suggestions

async def search_defects(db: Session, query: str, date_filter: Dict, project_id: Optional[int], 
                        statuses: Optional[List[str]], page: int, page_size: int):
    """搜索缺陷"""
    query_obj = db.query(Defect)
    
    # 文本搜索
    query_obj = query_obj.filter(
        Defect.title.ilike(f"%{query}%") | 
        Defect.description.ilike(f"%{query}%")
    )
    
    # 项目筛选
    if project_id:
        query_obj = query_obj.filter(Defect.project_id == project_id)
    
    # 状态筛选
    if statuses:
        query_obj = query_obj.filter(Defect.status.in_(statuses))
    
    # 时间筛选
    if date_filter:
        if date_filter.get("start_date"):
            query_obj = query_obj.filter(Defect.created_at >= date_filter["start_date"])
        if date_filter.get("end_date"):
            query_obj = query_obj.filter(Defect.created_at <= date_filter["end_date"])
    
    total_count = query_obj.count()
    
    # 分页
    offset = (page - 1) * page_size
    defects = query_obj.offset(offset).limit(page_size).all()
    
    results = []
    for defect in defects:
        results.append(SearchResult(
            id=defect.id,
            type="defect",
            title=defect.title,
            description=defect.description or "",
            updated_at=defect.updated_at,
            project={"id": defect.project_id, "name": defect.project.name if defect.project else ""},
            status=defect.status.value if defect.status else "",
            priority=defect.priority.value if defect.priority else "",
            assignee={"id": defect.assignee_id, "name": defect.assignee.username if defect.assignee else ""}
        ))
    
    return results, total_count

async def search_projects(db: Session, query: str, date_filter: Dict, page: int, page_size: int):
    """搜索项目"""
    query_obj = db.query(Project)
    
    # 文本搜索
    query_obj = query_obj.filter(
        Project.name.ilike(f"%{query}%") | 
        Project.description.ilike(f"%{query}%")
    )
    
    total_count = query_obj.count()
    
    # 分页
    offset = (page - 1) * page_size
    projects = query_obj.offset(offset).limit(page_size).all()
    
    results = []
    for project in projects:
        results.append(SearchResult(
            id=project.id,
            type="project",
            title=project.name,
            description=project.description or "",
            updated_at=project.updated_at,
            status=project.status if hasattr(project, 'status') else "active"
        ))
    
    return results, total_count

async def search_users(db: Session, query: str, page: int, page_size: int):
    """搜索用户"""
    query_obj = db.query(User)
    
    # 文本搜索
    query_obj = query_obj.filter(
        User.username.ilike(f"%{query}%") | 
        User.email.ilike(f"%{query}%")
    )
    
    total_count = query_obj.count()
    
    # 分页
    offset = (page - 1) * page_size
    users = query_obj.offset(offset).limit(page_size).all()
    
    results = []
    for user in users:
        results.append(SearchResult(
            id=user.id,
            type="user",
            title=user.username,
            description=user.email or "",
            updated_at=user.updated_at if hasattr(user, 'updated_at') else datetime.now()
        ))
    
    return results, total_count

async def search_coverage(db: Session, query: str, date_filter: Dict, project_id: Optional[int], 
                         page: int, page_size: int):
    """搜索覆盖率"""
    # 这里应该搜索覆盖率相关数据
    # 暂时返回空结果
    return [], 0

def parse_time_range(time_range: Optional[str], start_date: Optional[datetime], 
                    end_date: Optional[datetime]) -> Dict:
    """解析时间范围"""
    if start_date and end_date:
        return {"start_date": start_date, "end_date": end_date}
    
    if not time_range:
        return {}
    
    now = datetime.now()
    if time_range == "1d":
        return {"start_date": now - timedelta(days=1)}
    elif time_range == "7d":
        return {"start_date": now - timedelta(days=7)}
    elif time_range == "30d":
        return {"start_date": now - timedelta(days=30)}
    elif time_range == "90d":
        return {"start_date": now - timedelta(days=90)}
    
    return {}

def calculate_relevance(query: str, text: str) -> float:
    """计算相关性分数"""
    if not query or not text:
        return 0.0
    
    query_lower = query.lower()
    text_lower = text.lower()
    
    # 完全匹配
    if query_lower == text_lower:
        return 1.0
    
    # 开头匹配
    if text_lower.startswith(query_lower):
        return 0.9
    
    # 包含匹配
    if query_lower in text_lower:
        return 0.7
    
    # 模糊匹配（简单实现）
    words = query_lower.split()
    matches = sum(1 for word in words if word in text_lower)
    return matches / len(words) * 0.5

def sort_search_results(results: List[SearchResult], sort_by: str, sort_order: str) -> List[SearchResult]:
    """排序搜索结果"""
    reverse = sort_order.lower() == "desc"
    
    if sort_by == "relevance":
        # 按类型和相关性排序
        return sorted(results, key=lambda x: (x.type, x.title), reverse=reverse)
    elif sort_by == "date":
        return sorted(results, key=lambda x: x.updated_at, reverse=reverse)
    elif sort_by == "title":
        return sorted(results, key=lambda x: x.title, reverse=reverse)
    elif sort_by == "type":
        return sorted(results, key=lambda x: x.type, reverse=reverse)
    
    return results

# 统计函数
async def count_open_defects(db: Session) -> int:
    """统计未解决的缺陷数量"""
    try:
        return db.query(Defect).filter(Defect.status == "open").count()
    except:
        return 0

async def count_high_priority_defects(db: Session) -> int:
    """统计高优先级缺陷数量"""
    try:
        return db.query(Defect).filter(Defect.priority == "high").count()
    except:
        return 0

async def count_active_projects(db: Session) -> int:
    """统计活跃项目数量"""
    try:
        return db.query(Project).count()
    except:
        return 0

async def count_low_coverage_files(db: Session) -> int:
    """统计低覆盖率文件数量"""
    try:
        return db.query(CoverageMetric).filter(CoverageMetric.line_coverage < 60).count()
    except:
        return 0
