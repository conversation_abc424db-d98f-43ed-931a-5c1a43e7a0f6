from fastapi import APIRouter, Query
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

router = APIRouter(prefix="/api/search", tags=["search"])

# 简化的数据模型
class SearchSuggestion(BaseModel):
    text: str
    type: str
    category: str
    relevance: float

class SearchResult(BaseModel):
    id: int
    type: str
    title: str
    description: str
    updated_at: datetime

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    page: int
    page_size: int
    query: str
    search_time: float

class QuickSearch(BaseModel):
    id: int
    label: str
    query: str
    type: str
    icon: str
    count: int

class SearchStats(BaseModel):
    total_searches: int
    popular_queries: List[dict]
    search_trends: List[dict]

@router.get("/suggestions", response_model=List[SearchSuggestion])
async def get_search_suggestions(
    query: str = Query(..., min_length=1, description="搜索查询"),
    type: str = Query("all", description="搜索类型"),
    limit: int = Query(8, ge=1, le=20, description="建议数量限制")
):
    """获取搜索建议"""
    suggestions = []
    
    # 模拟建议数据
    if "缺陷" in query or "bug" in query.lower():
        suggestions.append(SearchSuggestion(
            text=f"{query} 相关缺陷",
            type="defect",
            category="缺陷管理",
            relevance=0.9
        ))
    
    if "项目" in query or "project" in query.lower():
        suggestions.append(SearchSuggestion(
            text=f"{query} 项目",
            type="project", 
            category="项目管理",
            relevance=0.8
        ))
    
    if "用户" in query or "user" in query.lower():
        suggestions.append(SearchSuggestion(
            text=f"{query} 用户",
            type="user",
            category="用户管理", 
            relevance=0.7
        ))
    
    return suggestions[:limit]

@router.get("", response_model=SearchResponse)
async def search(
    query: str = Query(..., min_length=1, description="搜索查询"),
    type: str = Query("all", description="搜索类型"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """执行搜索"""
    # 模拟搜索结果
    results = []
    
    if type in ["all", "defects"]:
        results.extend([
            SearchResult(
                id=1,
                type="defect",
                title=f"缺陷 #001: {query} 相关问题",
                description="这是一个与搜索查询相关的缺陷描述...",
                updated_at=datetime.now()
            ),
            SearchResult(
                id=2,
                type="defect", 
                title=f"缺陷 #002: {query} 功能异常",
                description="另一个相关的缺陷描述...",
                updated_at=datetime.now()
            )
        ])
    
    if type in ["all", "projects"]:
        results.append(SearchResult(
            id=3,
            type="project",
            title=f"项目: {query} 管理系统",
            description="项目描述信息...",
            updated_at=datetime.now()
        ))
    
    # 分页处理
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    paginated_results = results[start_idx:end_idx]
    
    return SearchResponse(
        results=paginated_results,
        total=len(results),
        page=page,
        page_size=page_size,
        query=query,
        search_time=50.0  # 模拟搜索时间
    )

@router.get("/quick-searches", response_model=List[QuickSearch])
async def get_quick_searches():
    """获取快捷搜索"""
    return [
        QuickSearch(
            id=1,
            label="未解决的缺陷",
            query="status:open",
            type="defects",
            icon="fas fa-bug",
            count=23
        ),
        QuickSearch(
            id=2,
            label="高优先级问题", 
            query="priority:high",
            type="defects",
            icon="fas fa-exclamation-triangle",
            count=8
        ),
        QuickSearch(
            id=3,
            label="活跃项目",
            query="status:active",
            type="projects", 
            icon="fas fa-project-diagram",
            count=12
        ),
        QuickSearch(
            id=4,
            label="低覆盖率文件",
            query="coverage:<60",
            type="coverage",
            icon="fas fa-chart-line", 
            count=15
        )
    ]

@router.get("/stats", response_model=SearchStats)
async def get_search_stats():
    """获取搜索统计"""
    return SearchStats(
        total_searches=1234,
        popular_queries=[
            {"query": "登录", "count": 45, "type": "defects"},
            {"query": "性能", "count": 32, "type": "all"},
            {"query": "API", "count": 28, "type": "projects"},
        ],
        search_trends=[]
    )
