from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime

class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., min_length=1, description="搜索查询")
    type: str = Field("all", description="搜索类型: all, defects, projects, users, coverage")
    scopes: Optional[List[str]] = Field(None, description="搜索范围")
    time_range: Optional[str] = Field(None, description="时间范围: 1d, 7d, 30d, 90d")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    project_id: Optional[int] = Field(None, description="项目ID")
    statuses: Optional[List[str]] = Field(None, description="状态筛选")
    sort_by: str = Field("relevance", description="排序字段: relevance, date, title, type")
    sort_order: str = Field("desc", description="排序顺序: asc, desc")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")

class SearchResult(BaseModel):
    """搜索结果项模型"""
    id: int = Field(..., description="结果ID")
    type: str = Field(..., description="结果类型")
    title: str = Field(..., description="标题")
    description: str = Field("", description="描述")
    updated_at: datetime = Field(..., description="更新时间")
    
    # 可选字段，根据类型不同而不同
    project: Optional[Dict[str, Any]] = Field(None, description="关联项目")
    assignee: Optional[Dict[str, Any]] = Field(None, description="负责人")
    status: Optional[str] = Field(None, description="状态")
    priority: Optional[str] = Field(None, description="优先级")
    tags: Optional[List[str]] = Field(None, description="标签")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外元数据")

    class Config:
        from_attributes = True

class SearchResponse(BaseModel):
    """搜索响应模型"""
    results: List[SearchResult] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="总结果数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    query: str = Field(..., description="搜索查询")
    search_time: float = Field(..., description="搜索耗时(毫秒)")
    
    # 可选的聚合信息
    aggregations: Optional[Dict[str, Any]] = Field(None, description="聚合统计")
    facets: Optional[Dict[str, List[Dict[str, Any]]]] = Field(None, description="分面统计")

class SearchSuggestion(BaseModel):
    """搜索建议模型"""
    text: str = Field(..., description="建议文本")
    type: str = Field(..., description="建议类型")
    category: str = Field(..., description="建议分类")
    relevance: float = Field(..., ge=0, le=1, description="相关性分数")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外信息")

class QuickSearch(BaseModel):
    """快捷搜索模型"""
    id: int = Field(..., description="快捷搜索ID")
    label: str = Field(..., description="显示标签")
    query: str = Field(..., description="搜索查询")
    type: str = Field(..., description="搜索类型")
    icon: str = Field(..., description="图标类名")
    count: int = Field(0, description="结果数量")
    description: Optional[str] = Field(None, description="描述")

class SearchHistory(BaseModel):
    """搜索历史模型"""
    id: int = Field(..., description="历史记录ID")
    query: str = Field(..., description="搜索查询")
    type: str = Field(..., description="搜索类型")
    filters: Optional[Dict[str, Any]] = Field(None, description="筛选条件")
    result_count: int = Field(..., description="结果数量")
    timestamp: datetime = Field(..., description="搜索时间")
    user_id: Optional[int] = Field(None, description="用户ID")

class PopularQuery(BaseModel):
    """热门查询模型"""
    query: str = Field(..., description="查询内容")
    count: int = Field(..., description="搜索次数")
    type: str = Field(..., description="搜索类型")
    trend: Optional[str] = Field(None, description="趋势: up, down, stable")

class SearchTrend(BaseModel):
    """搜索趋势模型"""
    date: datetime = Field(..., description="日期")
    search_count: int = Field(..., description="搜索次数")
    unique_queries: int = Field(..., description="唯一查询数")
    avg_results: float = Field(..., description="平均结果数")

class SearchStats(BaseModel):
    """搜索统计模型"""
    total_searches: int = Field(..., description="总搜索次数")
    popular_queries: List[PopularQuery] = Field(..., description="热门查询")
    search_trends: List[SearchTrend] = Field(..., description="搜索趋势")
    
    # 可选的详细统计
    type_distribution: Optional[Dict[str, int]] = Field(None, description="类型分布")
    avg_search_time: Optional[float] = Field(None, description="平均搜索时间")
    success_rate: Optional[float] = Field(None, description="搜索成功率")

class AdvancedSearchFilter(BaseModel):
    """高级搜索筛选器模型"""
    field: str = Field(..., description="筛选字段")
    operator: str = Field(..., description="操作符: eq, ne, gt, lt, gte, lte, in, like")
    value: Union[str, int, float, List[str]] = Field(..., description="筛选值")
    type: str = Field("string", description="值类型: string, number, date, boolean")

class AdvancedSearchRequest(SearchRequest):
    """高级搜索请求模型"""
    filters: Optional[List[AdvancedSearchFilter]] = Field(None, description="高级筛选条件")
    highlight: bool = Field(True, description="是否高亮匹配文本")
    fuzzy: bool = Field(False, description="是否启用模糊搜索")
    boost_fields: Optional[Dict[str, float]] = Field(None, description="字段权重提升")

class SearchAnalytics(BaseModel):
    """搜索分析模型"""
    query: str = Field(..., description="搜索查询")
    result_count: int = Field(..., description="结果数量")
    click_through_rate: float = Field(..., description="点击率")
    avg_position: float = Field(..., description="平均点击位置")
    bounce_rate: float = Field(..., description="跳出率")
    search_time: float = Field(..., description="搜索耗时")

class SearchExport(BaseModel):
    """搜索导出模型"""
    format: str = Field(..., description="导出格式: csv, excel, json")
    fields: Optional[List[str]] = Field(None, description="导出字段")
    filters: Optional[Dict[str, Any]] = Field(None, description="导出筛选条件")
    max_records: int = Field(10000, description="最大记录数")

class SearchIndexStatus(BaseModel):
    """搜索索引状态模型"""
    index_name: str = Field(..., description="索引名称")
    document_count: int = Field(..., description="文档数量")
    last_updated: datetime = Field(..., description="最后更新时间")
    status: str = Field(..., description="状态: healthy, warning, error")
    size_bytes: int = Field(..., description="索引大小(字节)")

class SearchConfiguration(BaseModel):
    """搜索配置模型"""
    max_results_per_page: int = Field(100, description="每页最大结果数")
    default_page_size: int = Field(20, description="默认每页数量")
    suggestion_count: int = Field(8, description="建议数量")
    highlight_fragment_size: int = Field(150, description="高亮片段大小")
    search_timeout: int = Field(30, description="搜索超时时间(秒)")
    enable_fuzzy_search: bool = Field(True, description="启用模糊搜索")
    enable_autocomplete: bool = Field(True, description="启用自动完成")
    enable_analytics: bool = Field(True, description="启用搜索分析")

# 搜索结果类型枚举
class SearchResultType:
    DEFECT = "defect"
    PROJECT = "project"
    USER = "user"
    COVERAGE = "coverage"
    REPORT = "report"
    FILE = "file"
    COMMENT = "comment"
    
    @classmethod
    def all_types(cls):
        return [cls.DEFECT, cls.PROJECT, cls.USER, cls.COVERAGE, cls.REPORT, cls.FILE, cls.COMMENT]

# 搜索操作符枚举
class SearchOperator:
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    LESS_THAN = "lt"
    GREATER_THAN_OR_EQUAL = "gte"
    LESS_THAN_OR_EQUAL = "lte"
    IN = "in"
    NOT_IN = "not_in"
    LIKE = "like"
    NOT_LIKE = "not_like"
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    
    @classmethod
    def all_operators(cls):
        return [
            cls.EQUALS, cls.NOT_EQUALS, cls.GREATER_THAN, cls.LESS_THAN,
            cls.GREATER_THAN_OR_EQUAL, cls.LESS_THAN_OR_EQUAL, cls.IN, cls.NOT_IN,
            cls.LIKE, cls.NOT_LIKE, cls.CONTAINS, cls.STARTS_WITH, cls.ENDS_WITH
        ]

# 排序字段枚举
class SearchSortField:
    RELEVANCE = "relevance"
    DATE = "date"
    TITLE = "title"
    TYPE = "type"
    PRIORITY = "priority"
    STATUS = "status"
    CREATED_AT = "created_at"
    UPDATED_AT = "updated_at"
    
    @classmethod
    def all_fields(cls):
        return [
            cls.RELEVANCE, cls.DATE, cls.TITLE, cls.TYPE,
            cls.PRIORITY, cls.STATUS, cls.CREATED_AT, cls.UPDATED_AT
        ]
