#!/usr/bin/env python3
"""
覆盖率模块功能验证测试脚本
验证覆盖率数据准确性、图表展示效果和用户体验
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any

class CoverageVerificationTest:
    """覆盖率模块验证测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        self.test_results = []
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        if data and not success:
            print(f"   详细信息: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    async def test_server_health(self) -> bool:
        """测试服务器健康状态"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_test("服务器健康检查", True, f"服务器状态正常: {data.get('status', 'unknown')}")
                    return True
                else:
                    self.log_test("服务器健康检查", False, f"服务器响应异常: {response.status}")
                    return False
        except Exception as e:
            self.log_test("服务器健康检查", False, f"连接失败: {str(e)}")
            return False
    
    async def test_coverage_api_endpoints(self) -> bool:
        """测试覆盖率API端点"""
        endpoints = [
            ("/api/coverage/", "覆盖率列表API"),
            ("/api/coverage/stats", "覆盖率统计API"),
            ("/api/coverage/trends", "覆盖率趋势API"),
            ("/api/coverage/distribution", "覆盖率分布API")
        ]
        
        all_success = True
        
        for endpoint, name in endpoints:
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success", False):
                            self.log_test(name, True, f"API响应正常，返回数据: {len(data.get('data', []))} 条记录")
                        else:
                            self.log_test(name, False, f"API返回失败: {data.get('message', 'Unknown error')}")
                            all_success = False
                    else:
                        self.log_test(name, False, f"HTTP状态码异常: {response.status}")
                        all_success = False
            except Exception as e:
                self.log_test(name, False, f"请求异常: {str(e)}")
                all_success = False
        
        return all_success
    
    async def test_coverage_data_accuracy(self) -> bool:
        """测试覆盖率数据准确性"""
        try:
            # 获取覆盖率统计数据
            async with self.session.get(f"{self.base_url}/api/coverage/stats") as response:
                if response.status != 200:
                    self.log_test("数据准确性验证", False, f"统计API请求失败: {response.status}")
                    return False
                
                stats_data = await response.json()
                if not stats_data.get("success"):
                    self.log_test("数据准确性验证", False, f"统计API返回失败: {stats_data.get('message')}")
                    return False
                
                stats = stats_data.get("data", {})
                
            # 获取覆盖率列表数据
            async with self.session.get(f"{self.base_url}/api/coverage/") as response:
                if response.status != 200:
                    self.log_test("数据准确性验证", False, f"列表API请求失败: {response.status}")
                    return False
                
                list_data = await response.json()
                if not list_data.get("success"):
                    self.log_test("数据准确性验证", False, f"列表API返回失败: {list_data.get('message')}")
                    return False
                
                metrics = list_data.get("data", [])
            
            # 验证数据一致性
            total_metrics = len(metrics)
            stats_total = stats.get("total_metrics", 0)
            
            if total_metrics == stats_total:
                self.log_test("数据一致性验证", True, f"统计数据与列表数据一致: {total_metrics} 条记录")
            else:
                self.log_test("数据一致性验证", False, 
                            f"数据不一致: 统计显示{stats_total}条，列表显示{total_metrics}条")
                return False
            
            # 验证覆盖率数值范围
            invalid_coverage = []
            for metric in metrics:
                for coverage_type in ['line_coverage', 'branch_coverage', 'function_coverage']:
                    value = metric.get(coverage_type, 0)
                    if not (0 <= value <= 100):
                        invalid_coverage.append(f"{metric.get('id')}: {coverage_type}={value}")
            
            if not invalid_coverage:
                self.log_test("覆盖率数值验证", True, "所有覆盖率数值都在有效范围内 (0-100%)")
            else:
                self.log_test("覆盖率数值验证", False, 
                            f"发现无效覆盖率数值: {', '.join(invalid_coverage[:5])}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test("数据准确性验证", False, f"验证过程异常: {str(e)}")
            return False
    
    async def test_chart_data_format(self) -> bool:
        """测试图表数据格式"""
        try:
            # 测试趋势图数据格式
            async with self.session.get(f"{self.base_url}/api/coverage/trends?date_range=30d") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        trends = data.get("data", {})
                        if "trends" in trends and isinstance(trends["trends"], list):
                            self.log_test("趋势图数据格式", True, f"趋势数据格式正确，包含 {len(trends['trends'])} 个数据点")
                        else:
                            self.log_test("趋势图数据格式", False, "趋势数据格式不正确")
                            return False
                    else:
                        self.log_test("趋势图数据格式", False, f"趋势API返回失败: {data.get('message')}")
                        return False
                else:
                    self.log_test("趋势图数据格式", False, f"趋势API请求失败: {response.status}")
                    return False
            
            # 测试分布图数据格式
            async with self.session.get(f"{self.base_url}/api/coverage/distribution") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        distribution = data.get("data", {})
                        if "distribution" in distribution and isinstance(distribution["distribution"], list):
                            self.log_test("分布图数据格式", True, f"分布数据格式正确，包含 {len(distribution['distribution'])} 个分类")
                        else:
                            self.log_test("分布图数据格式", False, "分布数据格式不正确")
                            return False
                    else:
                        self.log_test("分布图数据格式", False, f"分布API返回失败: {data.get('message')}")
                        return False
                else:
                    self.log_test("分布图数据格式", False, f"分布API请求失败: {response.status}")
                    return False
            
            return True
            
        except Exception as e:
            self.log_test("图表数据格式验证", False, f"验证过程异常: {str(e)}")
            return False
    
    async def test_api_parameters(self) -> bool:
        """测试API参数处理"""
        test_cases = [
            # 分页参数测试
            ("/api/coverage/?page=1&pageSize=5", "分页参数测试"),
            # 排序参数测试
            ("/api/coverage/?sortBy=line_coverage&sortOrder=desc", "排序参数测试"),
            # 筛选参数测试
            ("/api/coverage/?project_id=1", "项目筛选测试"),
            # 日期范围测试
            ("/api/coverage/trends?date_range=7d&coverage_type=line", "日期范围参数测试"),
            # 分布维度测试
            ("/api/coverage/distribution?dimension=level", "分布维度参数测试")
        ]
        
        all_success = True
        
        for endpoint, test_name in test_cases:
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("success", False):
                            self.log_test(test_name, True, "参数处理正常")
                        else:
                            self.log_test(test_name, False, f"API返回失败: {data.get('message')}")
                            all_success = False
                    else:
                        self.log_test(test_name, False, f"HTTP状态码异常: {response.status}")
                        all_success = False
            except Exception as e:
                self.log_test(test_name, False, f"请求异常: {str(e)}")
                all_success = False
        
        return all_success
    
    async def test_error_handling(self) -> bool:
        """测试错误处理"""
        error_test_cases = [
            # 无效参数测试
            ("/api/coverage/?page=-1", "负数页码处理"),
            ("/api/coverage/?pageSize=1000", "过大页面大小处理"),
            ("/api/coverage/?sortBy=invalid_field", "无效排序字段处理"),
            ("/api/coverage/trends?date_range=invalid", "无效日期范围处理"),
            # 不存在的资源
            ("/api/coverage/files/99999", "不存在资源处理")
        ]
        
        all_success = True
        
        for endpoint, test_name in error_test_cases:
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as response:
                    # 错误情况应该返回4xx状态码或者success=false
                    if response.status >= 400 or (response.status == 200):
                        if response.status == 200:
                            data = await response.json()
                            if not data.get("success", True):
                                self.log_test(test_name, True, f"正确处理错误: {data.get('message', 'Unknown error')}")
                            else:
                                self.log_test(test_name, False, "应该返回错误但返回了成功")
                                all_success = False
                        else:
                            self.log_test(test_name, True, f"正确返回错误状态码: {response.status}")
                    else:
                        self.log_test(test_name, False, f"错误处理不当，状态码: {response.status}")
                        all_success = False
            except Exception as e:
                # 网络异常也算是正确的错误处理
                self.log_test(test_name, True, f"正确抛出异常: {str(e)}")
        
        return all_success
    
    def generate_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": round((passed_tests / total_tests) * 100, 2) if total_tests > 0 else 0,
                "test_time": datetime.now().isoformat()
            },
            "test_results": self.test_results,
            "recommendations": []
        }
        
        # 生成建议
        if failed_tests == 0:
            report["recommendations"].append("🎉 所有测试通过！覆盖率模块功能正常。")
        else:
            report["recommendations"].append(f"⚠️  发现 {failed_tests} 个问题，需要修复后再进行下一步开发。")
            
        if passed_tests / total_tests >= 0.8:
            report["recommendations"].append("✅ 大部分功能正常，可以考虑进行用户体验优化。")
        else:
            report["recommendations"].append("❌ 核心功能存在问题，建议优先修复基础功能。")
        
        return report

async def main():
    """主测试函数"""
    print("🚀 开始覆盖率模块功能验证测试...")
    print("=" * 60)
    
    async with CoverageVerificationTest() as tester:
        # 1. 服务器健康检查
        print("\n📋 1. 服务器健康检查")
        server_ok = await tester.test_server_health()
        
        if not server_ok:
            print("❌ 服务器连接失败，请确保后端服务正在运行")
            return
        
        # 2. API端点测试
        print("\n📋 2. API端点功能测试")
        await tester.test_coverage_api_endpoints()
        
        # 3. 数据准确性验证
        print("\n📋 3. 数据准确性验证")
        await tester.test_coverage_data_accuracy()
        
        # 4. 图表数据格式测试
        print("\n📋 4. 图表数据格式测试")
        await tester.test_chart_data_format()
        
        # 5. API参数处理测试
        print("\n📋 5. API参数处理测试")
        await tester.test_api_parameters()
        
        # 6. 错误处理测试
        print("\n📋 6. 错误处理测试")
        await tester.test_error_handling()
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("📊 测试报告生成中...")
        
        report = tester.generate_report()
        
        print(f"\n📈 测试总结:")
        print(f"   总测试数: {report['summary']['total_tests']}")
        print(f"   通过测试: {report['summary']['passed_tests']}")
        print(f"   失败测试: {report['summary']['failed_tests']}")
        print(f"   成功率: {report['summary']['success_rate']}%")
        
        print(f"\n💡 建议:")
        for recommendation in report['recommendations']:
            print(f"   {recommendation}")
        
        # 保存详细报告
        report_file = f"coverage_verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 返回测试结果
        return report['summary']['success_rate'] >= 80

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行异常: {str(e)}")
        sys.exit(1)
