# 质量大盘后端日志配置文档

## 概述

本文档描述了质量大盘后端应用的日志配置和使用方法。日志系统支持多种输出方式、日志级别控制和文件轮转策略。

## 日志配置文件

### 1. 主要配置文件

- `config/logging.yaml` - 完整的YAML格式日志配置
- `config/logging_simple.yaml` - 简化版YAML配置
- `config/logging_working.py` - Python代码版本的日志配置（推荐使用）
- `config/logging_utils.py` - 日志工具函数

### 2. 当前使用的配置

项目当前使用 `config/logging_working.py` 中的Python代码配置，因为它更稳定且易于调试。

## 日志文件结构

```
backend/logs/
├── README.md          # 日志目录说明
├── app.log           # 应用主日志文件
├── error.log         # 错误日志文件
└── access.log        # API访问日志文件
```

## 日志级别

支持以下日志级别（按严重程度排序）：

1. **DEBUG** - 调试信息，仅在开发环境使用
2. **INFO** - 一般信息，记录应用的正常运行状态
3. **WARNING** - 警告信息，记录可能的问题但不影响正常运行
4. **ERROR** - 错误信息，记录应用运行中的错误
5. **CRITICAL** - 严重错误，记录可能导致应用崩溃的严重问题

## 日志格式

### 控制台输出格式
```
时间 - 级别 - 记录器名称 - 消息
例：10:09:28 - INFO - quality_dashboard.main - 这是信息日志
```

### 文件输出格式
```
时间戳 - 记录器名称 - 级别 - 模块:函数:行号 - 消息
例：2025-06-04 10:09:28 - quality_dashboard.main - INFO - main:get_dashboard_overview:229 - 获取质量大盘概览数据
```

## 环境变量控制

可以通过以下环境变量控制日志行为：

- `LOG_LEVEL` - 设置日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- `ENVIRONMENT` - 设置运行环境（development, production）

### 设置示例

```bash
# 设置为调试级别
export LOG_LEVEL=DEBUG

# 设置为生产环境
export ENVIRONMENT=production
```

## 日志记录器层次结构

```
root
├── quality_dashboard (主应用日志)
│   ├── quality_dashboard.main (主模块)
│   ├── quality_dashboard.database (数据库模块)
│   ├── quality_dashboard.services (服务模块)
│   └── quality_dashboard.access (API访问日志)
├── fastapi (FastAPI框架日志)
├── uvicorn (Uvicorn服务器日志)
└── sqlalchemy (SQLAlchemy数据库日志)
```

## 使用方法

### 1. 在代码中使用日志

```python
from config.logging_working import get_working_logger

# 获取日志记录器
logger = get_working_logger('module_name')

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")

# 记录异常信息
try:
    # 一些可能出错的代码
    pass
except Exception as e:
    logger.error(f"操作失败: {str(e)}")
    logger.exception("详细异常信息:")  # 包含堆栈跟踪
```

### 2. 记录API访问日志

```python
from config.logging_working import log_working_api_access

# 记录API访问
log_working_api_access(
    method="GET",
    path="/api/dashboard/overview",
    status_code=200,
    response_time=45.67,
    client_ip="127.0.0.1",
    user_agent="Mozilla/5.0..."
)
```

### 3. 初始化日志配置

```python
from config.logging_working import setup_working_logging

# 使用默认配置
setup_working_logging()

# 指定日志级别
setup_working_logging("DEBUG")
```

## 日志轮转策略

### 文件大小轮转
- 当日志文件大小超过10MB时自动轮转
- 保留指定数量的备份文件：
  - app.log: 保留5个备份
  - error.log: 保留5个备份
  - access.log: 保留10个备份

### 轮转文件命名
```
app.log          # 当前日志文件
app.log.1        # 第一个备份文件
app.log.2        # 第二个备份文件
...
app.log.5        # 最后一个备份文件
```

## 性能考虑

1. **日志级别控制** - 生产环境建议使用INFO或WARNING级别
2. **异步日志** - 大量日志输出时考虑使用异步处理
3. **日志文件清理** - 定期清理旧的日志文件
4. **敏感信息** - 避免在日志中记录密码、令牌等敏感信息

## 故障排除

### 常见问题

1. **日志文件无法创建**
   - 检查logs目录是否存在且有写权限
   - 检查磁盘空间是否充足

2. **日志级别不生效**
   - 检查环境变量LOG_LEVEL设置
   - 确认日志记录器的级别配置

3. **日志格式异常**
   - 检查格式化器配置
   - 确认日志消息中没有特殊字符

### 调试方法

```python
# 启用调试模式
import logging
logging.basicConfig(level=logging.DEBUG)

# 查看当前日志配置
logger = logging.getLogger('quality_dashboard')
print(f"Logger level: {logger.level}")
print(f"Handlers: {logger.handlers}")
```

## 最佳实践

1. **结构化日志** - 使用一致的日志格式和字段
2. **上下文信息** - 包含足够的上下文信息便于调试
3. **错误处理** - 在异常处理中记录详细的错误信息
4. **性能监控** - 记录关键操作的执行时间
5. **安全考虑** - 不在日志中记录敏感信息

## 测试

运行日志配置测试：

```bash
cd backend
python3 config/logging_working.py
```

检查日志文件：

```bash
ls -la logs/
cat logs/app.log
cat logs/access.log
cat logs/error.log
```
