"""
API端点集成测试
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient


@pytest.mark.integration
class TestHealthEndpoints:
    """健康检查端点测试"""
    
    def test_health_check_success(self, test_client):
        """测试健康检查成功"""
        with patch('backend.main.engine') as mock_engine, \
             patch('backend.main.get_redis') as mock_redis, \
             patch('psutil.cpu_percent', return_value=45.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk:
            
            # 模拟数据库连接成功
            mock_conn = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value = mock_conn
            
            # 模拟Redis连接成功
            mock_redis_client = AsyncMock()
            mock_redis_client.ping.return_value = True
            mock_redis.return_value = mock_redis_client
            
            # 模拟系统资源
            mock_memory.return_value.percent = 65.0
            mock_disk.return_value.percent = 40.0
            
            response = test_client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert "timestamp" in data
            assert "version" in data
            assert data["services"]["database"] == "healthy"
            assert data["services"]["redis"] == "healthy"
            assert data["system"]["cpu_percent"] == 45.0
    
    def test_health_check_failure(self, test_client):
        """测试健康检查失败"""
        with patch('backend.main.engine') as mock_engine, \
             patch('backend.main.get_redis') as mock_redis:
            
            # 模拟数据库连接失败
            mock_engine.begin.side_effect = Exception("Database connection failed")
            
            # 模拟Redis连接失败
            mock_redis.side_effect = Exception("Redis connection failed")
            
            response = test_client.get("/health")
            
            assert response.status_code == 503
            data = response.json()
            assert data["status"] == "unhealthy"
    
    def test_readiness_check(self, test_client):
        """测试就绪检查"""
        with patch('backend.main.engine') as mock_engine, \
             patch('backend.main.get_redis') as mock_redis, \
             patch('backend.main.data_sync_service') as mock_sync:
            
            # 模拟服务就绪
            mock_conn = AsyncMock()
            mock_engine.begin.return_value.__aenter__.return_value = mock_conn
            
            mock_redis_client = AsyncMock()
            mock_redis_client.ping.return_value = True
            mock_redis.return_value = mock_redis_client
            
            mock_sync.is_running = True
            
            response = test_client.get("/health/ready")
            
            assert response.status_code == 200
            data = response.json()
            assert data["ready"] is True
            assert data["checks"]["database"] is True
            assert data["checks"]["redis"] is True
            assert data["checks"]["sync_service"] is True
    
    def test_liveness_check(self, test_client):
        """测试存活检查"""
        response = test_client.get("/health/live")
        
        assert response.status_code == 200
        data = response.json()
        assert data["alive"] is True
        assert "timestamp" in data
        assert "uptime" in data


@pytest.mark.integration
class TestIntegrationEndpoints:
    """集成管理端点测试"""
    
    @patch('backend.services.integrations.jira_integration.jira_integration')
    @patch('backend.services.integrations.sonarqube_integration.sonarqube_integration')
    @patch('backend.services.integrations.jenkins_integration.jenkins_integration')
    def test_get_integrations_status(self, mock_jenkins, mock_sonar, mock_jira, test_client):
        """测试获取集成状态"""
        # 模拟集成连接状态
        mock_jira.__aenter__.return_value.test_connection.return_value = True
        mock_sonar.__aenter__.return_value.test_connection.return_value = True
        mock_jenkins.__aenter__.return_value.test_connection.return_value = False
        
        response = test_client.get("/api/integrations/status")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["jira"]["connected"] is True
        assert data["data"]["sonarqube"]["connected"] is True
        assert data["data"]["jenkins"]["connected"] is False
    
    @patch('backend.main.data_sync_service')
    def test_get_sync_tasks(self, mock_sync_service, test_client):
        """测试获取同步任务"""
        # 模拟同步任务状态
        mock_sync_service.get_all_tasks_status.return_value = [
            {
                "id": "jira_defects_sync",
                "name": "JIRA缺陷数据同步",
                "status": "success",
                "enabled": True,
                "is_running": False
            },
            {
                "id": "sonarqube_metrics_sync",
                "name": "SonarQube代码质量指标同步",
                "status": "pending",
                "enabled": True,
                "is_running": True
            }
        ]
        
        response = test_client.get("/api/sync/tasks")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["total"] == 2
        assert data["data"]["running"] == 1
        assert data["data"]["enabled"] == 2
    
    @patch('backend.main.data_sync_service')
    def test_execute_sync_task(self, mock_sync_service, test_client):
        """测试执行同步任务"""
        # 模拟同步任务执行结果
        from backend.services.data_sync_service import SyncResult, SyncStatus
        from datetime import datetime
        
        mock_result = SyncResult(
            task_id="jira_defects_sync",
            status=SyncStatus.SUCCESS,
            start_time=datetime.now(),
            end_time=datetime.now(),
            duration=2.5,
            records_processed=10,
            records_success=10,
            records_failed=0
        )
        mock_sync_service.execute_task.return_value = mock_result
        
        response = test_client.post("/api/sync/tasks/jira_defects_sync/execute")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["task_id"] == "jira_defects_sync"
        assert data["data"]["status"] == "success"
        assert data["data"]["records_processed"] == 10
    
    @patch('backend.main.data_sync_service')
    def test_get_sync_history(self, mock_sync_service, test_client):
        """测试获取同步历史"""
        # 模拟同步历史
        mock_sync_service.get_sync_history.return_value = [
            {
                "task_id": "jira_defects_sync",
                "status": "success",
                "start_time": "2024-01-01T10:00:00",
                "duration": 2.5,
                "records_processed": 10
            }
        ]
        
        response = test_client.get("/api/sync/history")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["data"]["history"]) == 1


@pytest.mark.integration
class TestPerformanceEndpoints:
    """性能监控端点测试"""
    
    @patch('backend.main.performance_monitor')
    def test_get_performance_metrics(self, mock_monitor, test_client):
        """测试获取性能指标"""
        # 模拟性能报告
        mock_monitor.get_performance_report.return_value = {
            "timestamp": "2024-01-01T10:00:00",
            "period_hours": 24,
            "system_metrics": {
                "cpu_usage": 45.0,
                "memory_usage": 65.0,
                "disk_usage": 40.0
            },
            "api_performance": {
                "avg_response_time": 0.25,
                "success_rate": 99.5,
                "requests_per_hour": 1200
            },
            "alerts": [],
            "summary": {
                "total_alerts": 0,
                "critical_alerts": 0,
                "system_health": "good"
            }
        }
        
        response = test_client.get("/api/performance/metrics")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "system_metrics" in data["data"]
        assert "api_performance" in data["data"]
        assert data["data"]["summary"]["system_health"] == "good"


@pytest.mark.integration
class TestDashboardEndpoints:
    """仪表板端点测试"""
    
    def test_dashboard_overview_with_cache(self, test_client):
        """测试仪表板概览（带缓存）"""
        # 第一次请求
        response1 = test_client.get("/api/dashboard/overview")
        assert response1.status_code == 200
        assert "X-Cache" in response1.headers
        
        # 第二次请求应该命中缓存
        response2 = test_client.get("/api/dashboard/overview")
        assert response2.status_code == 200
        
        # 验证响应数据结构
        data = response1.json()
        assert data["success"] is True
        assert "data" in data
        assert "metric_cards" in data["data"]
        assert "last_updated" in data["data"]
    
    def test_dashboard_trends(self, test_client):
        """测试质量趋势"""
        response = test_client.get("/api/dashboard/trends")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "coverage_trend" in data["data"]
        assert "gate_trend" in data["data"]
    
    def test_team_comparison(self, test_client):
        """测试团队对比"""
        response = test_client.get("/api/dashboard/teams")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "teams" in data["data"]
        assert isinstance(data["data"]["teams"], list)


@pytest.mark.integration
class TestErrorHandling:
    """错误处理测试"""
    
    def test_404_error(self, test_client):
        """测试404错误"""
        response = test_client.get("/api/nonexistent")
        assert response.status_code == 404
    
    def test_method_not_allowed(self, test_client):
        """测试方法不允许错误"""
        response = test_client.post("/api/dashboard/overview")
        assert response.status_code == 405
    
    @patch('backend.main.data_sync_service')
    def test_sync_task_not_found(self, mock_sync_service, test_client):
        """测试同步任务不存在"""
        mock_sync_service.execute_task.side_effect = ValueError("任务不存在")
        
        response = test_client.post("/api/sync/tasks/nonexistent/execute")
        assert response.status_code == 500
    
    def test_invalid_json_request(self, test_client):
        """测试无效JSON请求"""
        response = test_client.post(
            "/api/sync/tasks/test/execute",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        # FastAPI会自动处理JSON解析错误
        assert response.status_code in [400, 422]
