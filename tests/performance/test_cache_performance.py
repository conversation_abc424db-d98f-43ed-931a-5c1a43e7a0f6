"""
缓存性能测试
"""

import pytest
import asyncio
import time
from unittest.mock import patch
from backend.services.cache_service import CacheService


@pytest.mark.performance
class TestCachePerformance:
    """缓存性能测试类"""
    
    @pytest.fixture
    async def cache_service(self, test_redis):
        """创建缓存服务实例"""
        service = CacheService()
        service.redis_client = test_redis
        return service
    
    async def test_cache_hit_performance(self, cache_service):
        """测试缓存命中性能"""
        key = "performance_test"
        data = {"large_data": list(range(1000))}
        
        # 设置缓存
        await cache_service.set(key, data)
        
        # 测试多次获取的性能
        start_time = time.time()
        for _ in range(100):
            result = await cache_service.get(key)
            assert result == data
        end_time = time.time()
        
        # 100次缓存命中应该在1秒内完成
        assert end_time - start_time < 1.0
        print(f"100次缓存命中耗时: {end_time - start_time:.3f}秒")
    
    async def test_concurrent_cache_access(self, cache_service):
        """测试并发缓存访问"""
        key = "concurrent_test"
        data = {"test": "concurrent_data"}
        
        # 设置缓存
        await cache_service.set(key, data)
        
        async def cache_access():
            """单次缓存访问"""
            result = await cache_service.get(key)
            assert result == data
            return result
        
        # 并发访问测试
        start_time = time.time()
        tasks = [cache_access() for _ in range(50)]
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # 验证所有结果正确
        assert len(results) == 50
        assert all(r == data for r in results)
        
        # 50个并发访问应该在2秒内完成
        assert end_time - start_time < 2.0
        print(f"50个并发缓存访问耗时: {end_time - start_time:.3f}秒")
    
    async def test_large_data_caching(self, cache_service):
        """测试大数据缓存性能"""
        # 创建大数据集（约1MB）
        large_data = {
            "users": [
                {
                    "id": i,
                    "name": f"User {i}",
                    "email": f"user{i}@example.com",
                    "data": list(range(100))
                }
                for i in range(1000)
            ]
        }
        
        key = "large_data_test"
        
        # 测试设置大数据的性能
        start_time = time.time()
        result = await cache_service.set(key, large_data)
        set_time = time.time() - start_time
        
        assert result is True
        assert set_time < 1.0  # 设置应该在1秒内完成
        print(f"大数据缓存设置耗时: {set_time:.3f}秒")
        
        # 测试获取大数据的性能
        start_time = time.time()
        cached_data = await cache_service.get(key)
        get_time = time.time() - start_time
        
        assert cached_data == large_data
        assert get_time < 0.5  # 获取应该在0.5秒内完成
        print(f"大数据缓存获取耗时: {get_time:.3f}秒")
    
    async def test_cache_pattern_clearing_performance(self, cache_service):
        """测试缓存模式清除性能"""
        # 设置多个相关缓存
        keys = [f"pattern_test:{i}" for i in range(100)]
        data = {"test": "pattern_data"}
        
        # 批量设置缓存
        start_time = time.time()
        for key in keys:
            await cache_service.set(key, data)
        set_time = time.time() - start_time
        
        print(f"设置100个缓存耗时: {set_time:.3f}秒")
        
        # 测试模式清除性能
        start_time = time.time()
        result = await cache_service.clear_pattern("pattern_test:*")
        clear_time = time.time() - start_time
        
        assert result is True
        assert clear_time < 2.0  # 清除应该在2秒内完成
        print(f"模式清除100个缓存耗时: {clear_time:.3f}秒")
        
        # 验证缓存已清除
        for key in keys[:10]:  # 检查前10个
            cached_data = await cache_service.get(key)
            assert cached_data is None
    
    async def test_cache_memory_efficiency(self, cache_service):
        """测试缓存内存效率"""
        import sys
        
        # 创建不同大小的数据
        small_data = {"small": "data"}
        medium_data = {"medium": list(range(1000))}
        large_data = {"large": list(range(10000))}
        
        # 测试小数据
        await cache_service.set("small", small_data)
        small_size = sys.getsizeof(str(small_data))
        
        # 测试中等数据
        await cache_service.set("medium", medium_data)
        medium_size = sys.getsizeof(str(medium_data))
        
        # 测试大数据
        await cache_service.set("large", large_data)
        large_size = sys.getsizeof(str(large_data))
        
        print(f"小数据大小: {small_size} bytes")
        print(f"中等数据大小: {medium_size} bytes")
        print(f"大数据大小: {large_size} bytes")
        
        # 验证数据完整性
        assert await cache_service.get("small") == small_data
        assert await cache_service.get("medium") == medium_data
        assert await cache_service.get("large") == large_data
    
    async def test_cache_ttl_performance(self, cache_service):
        """测试TTL性能"""
        key = "ttl_test"
        data = {"ttl": "test_data"}
        
        # 测试短TTL
        start_time = time.time()
        await cache_service.set(key, data, 1)  # 1秒TTL
        set_time = time.time() - start_time
        
        # 立即获取
        start_time = time.time()
        result = await cache_service.get(key)
        get_time = time.time() - start_time
        
        assert result == data
        assert set_time < 0.1
        assert get_time < 0.1
        
        # 等待过期
        await asyncio.sleep(1.1)
        
        # 过期后获取
        start_time = time.time()
        result = await cache_service.get(key)
        expired_get_time = time.time() - start_time
        
        assert result is None
        assert expired_get_time < 0.1
        
        print(f"TTL设置耗时: {set_time:.3f}秒")
        print(f"TTL获取耗时: {get_time:.3f}秒")
        print(f"过期获取耗时: {expired_get_time:.3f}秒")


@pytest.mark.performance
class TestAPIPerformance:
    """API性能测试类"""
    
    def test_dashboard_overview_performance(self, test_client):
        """测试仪表板概览性能"""
        # 第一次请求（缓存未命中）
        start_time = time.time()
        response1 = test_client.get("/api/dashboard/overview")
        first_request_time = time.time() - start_time
        
        assert response1.status_code == 200
        assert first_request_time < 2.0  # 首次请求应在2秒内完成
        
        # 第二次请求（缓存命中）
        start_time = time.time()
        response2 = test_client.get("/api/dashboard/overview")
        second_request_time = time.time() - start_time
        
        assert response2.status_code == 200
        assert second_request_time < 0.5  # 缓存命中应在0.5秒内完成
        
        # 缓存应该显著提升性能
        improvement = (first_request_time - second_request_time) / first_request_time * 100
        assert improvement > 50  # 至少50%的性能提升
        
        print(f"首次请求耗时: {first_request_time:.3f}秒")
        print(f"缓存命中耗时: {second_request_time:.3f}秒")
        print(f"性能提升: {improvement:.1f}%")
    
    def test_concurrent_api_requests(self, test_client):
        """测试并发API请求性能"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            """发送API请求"""
            start_time = time.time()
            response = test_client.get("/api/dashboard/overview")
            end_time = time.time()
            results.put({
                "status_code": response.status_code,
                "response_time": end_time - start_time
            })
        
        # 创建10个并发线程
        threads = []
        start_time = time.time()
        
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 收集结果
        request_results = []
        while not results.empty():
            request_results.append(results.get())
        
        # 验证结果
        assert len(request_results) == 10
        assert all(r["status_code"] == 200 for r in request_results)
        
        # 计算性能指标
        response_times = [r["response_time"] for r in request_results]
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        # 性能要求
        assert total_time < 5.0  # 总时间应在5秒内
        assert avg_response_time < 2.0  # 平均响应时间应在2秒内
        assert max_response_time < 3.0  # 最大响应时间应在3秒内
        
        print(f"10个并发请求总耗时: {total_time:.3f}秒")
        print(f"平均响应时间: {avg_response_time:.3f}秒")
        print(f"最大响应时间: {max_response_time:.3f}秒")
    
    @pytest.mark.slow
    def test_sustained_load_performance(self, test_client):
        """测试持续负载性能"""
        request_count = 100
        response_times = []
        
        start_time = time.time()
        
        for i in range(request_count):
            request_start = time.time()
            response = test_client.get("/api/dashboard/overview")
            request_end = time.time()
            
            assert response.status_code == 200
            response_times.append(request_end - request_start)
            
            # 每10个请求打印进度
            if (i + 1) % 10 == 0:
                print(f"完成 {i + 1}/{request_count} 个请求")
        
        total_time = time.time() - start_time
        
        # 计算性能指标
        avg_response_time = sum(response_times) / len(response_times)
        p95_response_time = sorted(response_times)[int(0.95 * len(response_times))]
        throughput = request_count / total_time
        
        # 性能要求
        assert avg_response_time < 1.0  # 平均响应时间应在1秒内
        assert p95_response_time < 2.0  # 95%响应时间应在2秒内
        assert throughput > 10  # 吞吐量应大于10 RPS
        
        print(f"持续负载测试结果:")
        print(f"总请求数: {request_count}")
        print(f"总耗时: {total_time:.3f}秒")
        print(f"平均响应时间: {avg_response_time:.3f}秒")
        print(f"95%响应时间: {p95_response_time:.3f}秒")
        print(f"吞吐量: {throughput:.1f} RPS")
