"""
测试配置文件
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import AsyncGenerator, Generator
import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import StaticPool
import redis.asyncio as redis

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.main import app
from backend.database import Base, get_db, get_redis
from backend.config.settings import Settings


# 测试数据库配置
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"
TEST_REDIS_URL = "redis://localhost:6379/15"  # 使用测试数据库


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine(
        TEST_DATABASE_URL,
        echo=False,
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
        },
    )
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    # 清理
    await engine.dispose()


@pytest.fixture
async def test_db_session(test_engine) -> AsyncGenerator[AsyncSession, None]:
    """创建测试数据库会话"""
    async_session = async_sessionmaker(
        test_engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()


@pytest.fixture
async def test_redis():
    """创建测试Redis连接"""
    try:
        redis_client = redis.from_url(TEST_REDIS_URL, decode_responses=True)
        await redis_client.ping()
        yield redis_client
        # 清理测试数据
        await redis_client.flushdb()
        await redis_client.close()
    except Exception:
        # 如果Redis不可用，使用Mock
        from unittest.mock import AsyncMock
        mock_redis = AsyncMock()
        mock_redis.ping.return_value = True
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        mock_redis.delete.return_value = True
        mock_redis.keys.return_value = []
        yield mock_redis


@pytest.fixture
def test_client(test_db_session, test_redis):
    """创建测试客户端"""
    # 覆盖依赖
    app.dependency_overrides[get_db] = lambda: test_db_session
    app.dependency_overrides[get_redis] = lambda: test_redis
    
    with TestClient(app) as client:
        yield client
    
    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture
def test_settings():
    """测试设置"""
    return Settings(
        DATABASE_URL=TEST_DATABASE_URL,
        REDIS_URL=TEST_REDIS_URL,
        DEBUG=True,
        JIRA_URL="https://test-jira.com",
        JIRA_USERNAME="test_user",
        JIRA_TOKEN="test_token",
        SONARQUBE_URL="https://test-sonar.com",
        SONARQUBE_TOKEN="test_token",
        JENKINS_URL="https://test-jenkins.com",
        JENKINS_USERNAME="test_user",
        JENKINS_TOKEN="test_token",
    )


@pytest.fixture
def mock_jira_response():
    """模拟JIRA响应数据"""
    return {
        "issues": [
            {
                "key": "TEST-1",
                "fields": {
                    "summary": "测试缺陷1",
                    "description": "这是一个测试缺陷",
                    "issuetype": {"name": "Bug"},
                    "status": {"name": "Open"},
                    "priority": {"name": "High"},
                    "assignee": {"displayName": "测试用户"},
                    "reporter": {"displayName": "报告用户"},
                    "created": "2024-01-01T10:00:00.000+0000",
                    "updated": "2024-01-01T10:00:00.000+0000",
                    "resolutiondate": None,
                    "project": {"key": "TEST"},
                    "labels": ["bug", "urgent"],
                    "components": [{"name": "frontend"}],
                    "fixVersions": []
                }
            }
        ]
    }


@pytest.fixture
def mock_sonarqube_response():
    """模拟SonarQube响应数据"""
    return {
        "component": {
            "measures": [
                {"metric": "ncloc", "value": "1000"},
                {"metric": "coverage", "value": "85.5"},
                {"metric": "duplicated_lines_density", "value": "2.1"},
                {"metric": "bugs", "value": "5"},
                {"metric": "vulnerabilities", "value": "2"},
                {"metric": "code_smells", "value": "15"},
                {"metric": "security_hotspots", "value": "3"},
                {"metric": "sqale_index", "value": "120"},
                {"metric": "reliability_rating", "value": "A"},
                {"metric": "security_rating", "value": "A"},
                {"metric": "sqale_rating", "value": "A"}
            ]
        }
    }


@pytest.fixture
def mock_jenkins_response():
    """模拟Jenkins响应数据"""
    return {
        "jobs": [
            {
                "name": "test-job",
                "url": "https://test-jenkins.com/job/test-job/",
                "color": "blue",
                "buildable": True,
                "lastBuild": {
                    "number": 100,
                    "url": "https://test-jenkins.com/job/test-job/100/",
                    "result": "SUCCESS",
                    "duration": 120000,
                    "timestamp": 1704110400000,
                    "building": False,
                    "description": "测试构建",
                    "displayName": "#100",
                    "fullDisplayName": "test-job #100"
                },
                "lastSuccessfulBuild": {
                    "number": 100,
                    "url": "https://test-jenkins.com/job/test-job/100/",
                    "result": "SUCCESS",
                    "duration": 120000,
                    "timestamp": 1704110400000,
                    "building": False,
                    "description": "测试构建",
                    "displayName": "#100",
                    "fullDisplayName": "test-job #100"
                },
                "lastFailedBuild": None
            }
        ]
    }


# 测试标记
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.performance = pytest.mark.performance
pytest.mark.slow = pytest.mark.slow
