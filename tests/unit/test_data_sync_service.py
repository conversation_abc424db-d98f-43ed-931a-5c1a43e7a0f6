"""
数据同步服务单元测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from backend.services.data_sync_service import (
    DataSyncService, SyncTask, SyncResult, SyncStatus, SyncType
)


@pytest.mark.unit
class TestDataSyncService:
    """数据同步服务测试类"""
    
    @pytest.fixture
    def sync_service(self):
        """创建数据同步服务实例"""
        service = DataSyncService()
        # 清空默认任务，避免干扰测试
        service.tasks.clear()
        service.sync_history.clear()
        return service
    
    def test_add_task(self, sync_service):
        """测试添加同步任务"""
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=AsyncMock(),
            schedule_interval=300
        )
        
        sync_service.add_task(task)
        
        assert "test_task" in sync_service.tasks
        assert sync_service.tasks["test_task"] == task
        assert task.next_sync is not None
    
    def test_remove_task(self, sync_service):
        """测试移除同步任务"""
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=AsyncMock(),
            schedule_interval=300
        )
        
        sync_service.add_task(task)
        assert "test_task" in sync_service.tasks
        
        result = sync_service.remove_task("test_task")
        assert result is True
        assert "test_task" not in sync_service.tasks
        
        # 测试移除不存在的任务
        result = sync_service.remove_task("nonexistent")
        assert result is False
    
    def test_enable_disable_task(self, sync_service):
        """测试启用/禁用任务"""
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=AsyncMock(),
            schedule_interval=300,
            enabled=False
        )
        
        sync_service.add_task(task)
        
        # 测试启用任务
        result = sync_service.enable_task("test_task")
        assert result is True
        assert task.enabled is True
        assert task.next_sync is not None
        
        # 测试禁用任务
        result = sync_service.disable_task("test_task")
        assert result is True
        assert task.enabled is False
        assert task.next_sync is None
    
    async def test_execute_task_success(self, sync_service):
        """测试成功执行同步任务"""
        # 创建模拟同步函数
        mock_sync_function = AsyncMock()
        mock_sync_function.return_value = {
            "success": True,
            "data": {"items": [1, 2, 3]}
        }
        
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=mock_sync_function,
            schedule_interval=300
        )
        
        sync_service.add_task(task)
        
        # 执行任务
        result = await sync_service.execute_task("test_task")
        
        # 验证结果
        assert isinstance(result, SyncResult)
        assert result.status == SyncStatus.SUCCESS
        assert result.records_processed == 3
        assert result.records_success == 3
        assert result.error_message is None
        assert task.status == SyncStatus.SUCCESS
        assert task.retry_count == 0
        
        # 验证历史记录
        assert len(sync_service.sync_history) == 1
        assert sync_service.sync_history[0] == result
    
    async def test_execute_task_failure(self, sync_service):
        """测试执行同步任务失败"""
        # 创建模拟同步函数
        mock_sync_function = AsyncMock()
        mock_sync_function.return_value = {
            "success": False,
            "error": "同步失败"
        }
        
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=mock_sync_function,
            schedule_interval=300
        )
        
        sync_service.add_task(task)
        
        # 执行任务
        result = await sync_service.execute_task("test_task")
        
        # 验证结果
        assert result.status == SyncStatus.FAILED
        assert result.error_message == "同步失败"
        assert task.status == SyncStatus.FAILED
        assert task.error_message == "同步失败"
    
    async def test_execute_task_exception(self, sync_service):
        """测试执行同步任务异常"""
        # 创建模拟同步函数
        mock_sync_function = AsyncMock()
        mock_sync_function.side_effect = Exception("同步异常")
        
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=mock_sync_function,
            schedule_interval=300
        )
        
        sync_service.add_task(task)
        
        # 执行任务
        result = await sync_service.execute_task("test_task")
        
        # 验证结果
        assert result.status == SyncStatus.FAILED
        assert result.error_message == "同步异常"
        assert task.retry_count == 1
    
    async def test_execute_nonexistent_task(self, sync_service):
        """测试执行不存在的任务"""
        with pytest.raises(ValueError, match="任务不存在"):
            await sync_service.execute_task("nonexistent")
    
    async def test_execute_running_task(self, sync_service):
        """测试执行正在运行的任务"""
        # 创建长时间运行的同步函数
        mock_sync_function = AsyncMock()
        
        async def long_running_sync(config):
            await asyncio.sleep(1)
            return {"success": True}
        
        mock_sync_function.side_effect = long_running_sync
        
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=mock_sync_function,
            schedule_interval=300
        )
        
        sync_service.add_task(task)
        
        # 启动第一个任务
        task1 = asyncio.create_task(sync_service.execute_task("test_task"))
        
        # 等待任务开始运行
        await asyncio.sleep(0.1)
        
        # 尝试启动第二个相同任务
        with pytest.raises(RuntimeError, match="任务正在运行"):
            await sync_service.execute_task("test_task")
        
        # 等待第一个任务完成
        await task1
    
    def test_get_task_status(self, sync_service):
        """测试获取任务状态"""
        task = SyncTask(
            id="test_task",
            name="测试任务",
            source="Test Source",
            target="Test Target",
            sync_function=AsyncMock(),
            schedule_interval=300,
            last_sync=datetime.now(),
            next_sync=datetime.now() + timedelta(minutes=5)
        )
        
        sync_service.add_task(task)
        
        status = sync_service.get_task_status("test_task")
        
        assert status is not None
        assert status["id"] == "test_task"
        assert status["name"] == "测试任务"
        assert status["source"] == "Test Source"
        assert status["target"] == "Test Target"
        assert status["enabled"] is True
        assert status["is_running"] is False
        assert "last_sync" in status
        assert "next_sync" in status
        
        # 测试不存在的任务
        status = sync_service.get_task_status("nonexistent")
        assert status is None
    
    def test_get_all_tasks_status(self, sync_service):
        """测试获取所有任务状态"""
        # 添加多个任务
        for i in range(3):
            task = SyncTask(
                id=f"test_task_{i}",
                name=f"测试任务{i}",
                source="Test Source",
                target="Test Target",
                sync_function=AsyncMock(),
                schedule_interval=300
            )
            sync_service.add_task(task)
        
        all_status = sync_service.get_all_tasks_status()
        
        assert len(all_status) == 3
        assert all(isinstance(status, dict) for status in all_status)
        assert all("id" in status for status in all_status)
    
    def test_get_sync_history(self, sync_service):
        """测试获取同步历史"""
        # 添加历史记录
        for i in range(5):
            result = SyncResult(
                task_id=f"task_{i}",
                status=SyncStatus.SUCCESS,
                start_time=datetime.now() - timedelta(minutes=i),
                end_time=datetime.now() - timedelta(minutes=i) + timedelta(seconds=30),
                duration=30.0
            )
            sync_service.sync_history.append(result)
        
        # 测试获取所有历史
        history = sync_service.get_sync_history()
        assert len(history) == 5
        
        # 测试限制数量
        history = sync_service.get_sync_history(limit=3)
        assert len(history) == 3
        
        # 验证返回格式
        assert all(isinstance(record, dict) for record in history)
        assert all("task_id" in record for record in history)
        assert all("status" in record for record in history)
    
    @patch('backend.services.data_sync_service.sync_jira_defects')
    async def test_sync_jira_defects(self, mock_sync_jira, sync_service):
        """测试JIRA缺陷同步"""
        # 模拟JIRA同步结果
        mock_sync_jira.return_value = {
            "success": True,
            "data": {"issues": [{"key": "TEST-1"}]}
        }
        
        config = {"project_keys": ["TEST"]}
        result = await sync_service._sync_jira_defects(config)
        
        assert result["success"] is True
        assert result["data"]["projects_synced"] == 1
        assert result["data"]["total_projects"] == 1
        mock_sync_jira.assert_called_once_with("TEST")
    
    @patch('backend.services.data_sync_service.sync_sonarqube_metrics')
    async def test_sync_sonarqube_metrics(self, mock_sync_sonar, sync_service):
        """测试SonarQube指标同步"""
        # 模拟SonarQube同步结果
        mock_sync_sonar.return_value = {
            "success": True,
            "data": {"metrics": {"coverage": 85.5}}
        }
        
        config = {"project_keys": ["test-project"]}
        result = await sync_service._sync_sonarqube_metrics(config)
        
        assert result["success"] is True
        assert result["data"]["projects_synced"] == 1
        mock_sync_sonar.assert_called_once_with("test-project")
    
    @patch('backend.services.data_sync_service.sync_jenkins_builds')
    async def test_sync_jenkins_builds(self, mock_sync_jenkins, sync_service):
        """测试Jenkins构建同步"""
        # 模拟Jenkins同步结果
        mock_sync_jenkins.return_value = {
            "success": True,
            "data": {"statistics": {"total_builds": 10}}
        }
        
        config = {"job_names": ["test-job"]}
        result = await sync_service._sync_jenkins_builds(config)
        
        assert result["success"] is True
        assert result["data"]["jobs_synced"] == 1
        mock_sync_jenkins.assert_called_once_with("test-job")
    
    async def test_scheduler_lifecycle(self, sync_service):
        """测试调度器生命周期"""
        # 测试启动调度器
        assert sync_service.is_running is False
        
        # 启动调度器（不等待完成）
        scheduler_task = asyncio.create_task(sync_service.start_scheduler())
        await asyncio.sleep(0.1)  # 让调度器启动
        
        assert sync_service.is_running is True
        
        # 停止调度器
        await sync_service.stop_scheduler()
        assert sync_service.is_running is False
        
        # 取消调度器任务
        scheduler_task.cancel()
        try:
            await scheduler_task
        except asyncio.CancelledError:
            pass
