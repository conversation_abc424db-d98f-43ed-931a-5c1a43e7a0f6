"""
第三方集成服务单元测试
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
import aiohttp

from backend.services.integrations.jira_integration import JiraIntegration, JiraIssue
from backend.services.integrations.sonarqube_integration import SonarQubeIntegration, CodeMetrics
from backend.services.integrations.jenkins_integration import JenkinsIntegration, JenkinsBuild


@pytest.mark.unit
class TestJiraIntegration:
    """JIRA集成测试类"""
    
    @pytest.fixture
    def jira_integration(self, test_settings):
        """创建JIRA集成实例"""
        with patch('backend.services.integrations.jira_integration.settings', test_settings):
            return JiraIntegration()
    
    def test_create_auth_header(self, jira_integration):
        """测试认证头创建"""
        auth_header = jira_integration._create_auth_header()
        assert auth_header.startswith("Basic ")
        assert len(auth_header) > 10
    
    @patch('aiohttp.ClientSession.get')
    async def test_test_connection_success(self, mock_get, jira_integration):
        """测试连接成功"""
        # 模拟成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"displayName": "Test User"}
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jira_integration as jira:
            result = await jira.test_connection()
            assert result is True
    
    @patch('aiohttp.ClientSession.get')
    async def test_test_connection_failure(self, mock_get, jira_integration):
        """测试连接失败"""
        # 模拟失败响应
        mock_response = AsyncMock()
        mock_response.status = 401
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jira_integration as jira:
            result = await jira.test_connection()
            assert result is False
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_projects(self, mock_get, jira_integration):
        """测试获取项目列表"""
        # 模拟项目响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = [
            {
                "key": "TEST",
                "name": "Test Project",
                "description": "Test Description",
                "lead": {"displayName": "Test Lead"},
                "projectTypeKey": "software"
            }
        ]
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jira_integration as jira:
            projects = await jira.get_projects()
            assert len(projects) == 1
            assert projects[0]["key"] == "TEST"
            assert projects[0]["name"] == "Test Project"
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_issues(self, mock_get, jira_integration, mock_jira_response):
        """测试获取问题列表"""
        # 模拟问题响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_jira_response
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jira_integration as jira:
            issues = await jira.get_issues("TEST")
            assert len(issues) == 1
            assert isinstance(issues[0], JiraIssue)
            assert issues[0].key == "TEST-1"
            assert issues[0].summary == "测试缺陷1"
    
    def test_parse_issue(self, jira_integration, mock_jira_response):
        """测试问题数据解析"""
        issue_data = mock_jira_response["issues"][0]
        issue = jira_integration._parse_issue(issue_data)
        
        assert isinstance(issue, JiraIssue)
        assert issue.key == "TEST-1"
        assert issue.summary == "测试缺陷1"
        assert issue.issue_type == "Bug"
        assert issue.status == "Open"
        assert issue.priority == "High"
        assert issue.project_key == "TEST"
        assert "bug" in issue.labels
    
    @patch('aiohttp.ClientSession.post')
    async def test_create_issue(self, mock_post, jira_integration):
        """测试创建问题"""
        # 模拟创建响应
        mock_response = AsyncMock()
        mock_response.status = 201
        mock_response.json.return_value = {"key": "TEST-2"}
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async with jira_integration as jira:
            issue_key = await jira.create_issue(
                project_key="TEST",
                summary="新测试问题",
                description="这是一个新的测试问题",
                issue_type="Bug",
                priority="High"
            )
            assert issue_key == "TEST-2"


@pytest.mark.unit
class TestSonarQubeIntegration:
    """SonarQube集成测试类"""
    
    @pytest.fixture
    def sonarqube_integration(self, test_settings):
        """创建SonarQube集成实例"""
        with patch('backend.services.integrations.sonarqube_integration.settings', test_settings):
            return SonarQubeIntegration()
    
    @patch('aiohttp.ClientSession.get')
    async def test_test_connection(self, mock_get, sonarqube_integration):
        """测试连接"""
        # 模拟成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"status": "UP"}
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with sonarqube_integration as sonar:
            result = await sonar.test_connection()
            assert result is True
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_project_metrics(self, mock_get, sonarqube_integration, mock_sonarqube_response):
        """测试获取项目指标"""
        # 模拟指标响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_sonarqube_response
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with sonarqube_integration as sonar:
            metrics = await sonar.get_project_metrics("test-project")
            
            assert isinstance(metrics, CodeMetrics)
            assert metrics.project_key == "test-project"
            assert metrics.lines_of_code == 1000
            assert metrics.coverage == 85.5
            assert metrics.bugs == 5
            assert metrics.vulnerabilities == 2
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_quality_gate_status(self, mock_get, sonarqube_integration):
        """测试获取质量门禁状态"""
        # 模拟质量门禁响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "projectStatus": {
                "status": "OK",
                "conditions": [
                    {
                        "status": "OK",
                        "metricKey": "coverage",
                        "actualValue": "85.5"
                    }
                ],
                "analysisId": "test-analysis-id"
            }
        }
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with sonarqube_integration as sonar:
            quality_gate = await sonar.get_quality_gate_status("test-project")
            
            assert quality_gate.status == "OK"
            assert quality_gate.project_key == "test-project"
            assert len(quality_gate.conditions) == 1


@pytest.mark.unit
class TestJenkinsIntegration:
    """Jenkins集成测试类"""
    
    @pytest.fixture
    def jenkins_integration(self, test_settings):
        """创建Jenkins集成实例"""
        with patch('backend.services.integrations.jenkins_integration.settings', test_settings):
            return JenkinsIntegration()
    
    @patch('aiohttp.ClientSession.get')
    async def test_test_connection(self, mock_get, jenkins_integration):
        """测试连接"""
        # 模拟成功响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"version": "2.401.3"}
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jenkins_integration as jenkins:
            result = await jenkins.test_connection()
            assert result is True
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_jobs(self, mock_get, jenkins_integration, mock_jenkins_response):
        """测试获取任务列表"""
        # 模拟任务响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_jenkins_response
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jenkins_integration as jenkins:
            jobs = await jenkins.get_jobs()
            
            assert len(jobs) == 1
            assert jobs[0].name == "test-job"
            assert jobs[0].buildable is True
            assert jobs[0].last_build is not None
            assert isinstance(jobs[0].last_build, JenkinsBuild)
    
    def test_parse_build(self, jenkins_integration):
        """测试构建数据解析"""
        build_data = {
            "number": 100,
            "url": "https://test-jenkins.com/job/test-job/100/",
            "result": "SUCCESS",
            "duration": 120000,
            "timestamp": 1704110400000,
            "building": False,
            "description": "测试构建",
            "displayName": "#100",
            "fullDisplayName": "test-job #100"
        }
        
        build = jenkins_integration._parse_build(build_data)
        
        assert isinstance(build, JenkinsBuild)
        assert build.number == 100
        assert build.result == "SUCCESS"
        assert build.duration == 120000
        assert build.building is False
    
    @patch('aiohttp.ClientSession.get')
    async def test_get_build_statistics(self, mock_get, jenkins_integration):
        """测试获取构建统计"""
        # 模拟构建历史响应
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "builds": [
                {
                    "number": 100,
                    "url": "https://test-jenkins.com/job/test-job/100/",
                    "result": "SUCCESS",
                    "duration": 120000,
                    "timestamp": 1704110400000,
                    "building": False,
                    "description": "测试构建",
                    "displayName": "#100",
                    "fullDisplayName": "test-job #100"
                },
                {
                    "number": 99,
                    "url": "https://test-jenkins.com/job/test-job/99/",
                    "result": "FAILURE",
                    "duration": 60000,
                    "timestamp": 1704024000000,
                    "building": False,
                    "description": "失败构建",
                    "displayName": "#99",
                    "fullDisplayName": "test-job #99"
                }
            ]
        }
        mock_get.return_value.__aenter__.return_value = mock_response
        
        async with jenkins_integration as jenkins:
            stats = await jenkins.get_build_statistics("test-job", days=30)
            
            assert "summary" in stats
            assert stats["summary"]["total_builds"] == 2
            assert stats["summary"]["successful_builds"] == 1
            assert stats["summary"]["failed_builds"] == 1
            assert stats["summary"]["success_rate"] == 50.0
    
    @patch('aiohttp.ClientSession.post')
    async def test_trigger_build(self, mock_post, jenkins_integration):
        """测试触发构建"""
        # 模拟触发响应
        mock_response = AsyncMock()
        mock_response.status = 201
        mock_post.return_value.__aenter__.return_value = mock_response
        
        async with jenkins_integration as jenkins:
            result = await jenkins.trigger_build("test-job")
            assert result is True
            
            # 测试带参数的构建
            result = await jenkins.trigger_build("test-job", {"BRANCH": "main"})
            assert result is True
