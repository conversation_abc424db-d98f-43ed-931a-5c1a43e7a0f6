"""
缓存服务单元测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from backend.services.cache_service import CacheService


@pytest.mark.unit
class TestCacheService:
    """缓存服务测试类"""
    
    @pytest.fixture
    async def cache_service(self, test_redis):
        """创建缓存服务实例"""
        service = CacheService()
        service.redis_client = test_redis
        return service
    
    async def test_generate_cache_key(self, cache_service):
        """测试缓存键生成"""
        # 测试无参数
        key1 = cache_service._generate_cache_key("test_prefix")
        assert key1 == "test_prefix"
        
        # 测试有参数
        params = {"user_id": 123, "type": "dashboard"}
        key2 = cache_service._generate_cache_key("test_prefix", params)
        assert key2.startswith("test_prefix:")
        assert len(key2.split(":")) == 2
        
        # 测试相同参数生成相同键
        key3 = cache_service._generate_cache_key("test_prefix", params)
        assert key2 == key3
        
        # 测试不同参数生成不同键
        params2 = {"user_id": 456, "type": "dashboard"}
        key4 = cache_service._generate_cache_key("test_prefix", params2)
        assert key2 != key4
    
    async def test_basic_cache_operations(self, cache_service):
        """测试基本缓存操作"""
        key = "test_key"
        value = {"data": "test_value", "count": 42}
        
        # 测试设置缓存
        result = await cache_service.set(key, value, 60)
        assert result is True
        
        # 测试获取缓存
        cached_value = await cache_service.get(key)
        assert cached_value == value
        
        # 测试删除缓存
        result = await cache_service.delete(key)
        assert result is True
        
        # 测试获取已删除的缓存
        cached_value = await cache_service.get(key)
        assert cached_value is None
    
    async def test_cache_expiration(self, cache_service):
        """测试缓存过期"""
        key = "expire_test"
        value = {"data": "expire_test"}
        
        # 设置短期缓存
        await cache_service.set(key, value, 1)
        
        # 立即获取应该成功
        cached_value = await cache_service.get(key)
        assert cached_value == value
        
        # 等待过期
        await asyncio.sleep(1.1)
        
        # 过期后获取应该返回None
        cached_value = await cache_service.get(key)
        assert cached_value is None
    
    async def test_dashboard_overview_cache(self, cache_service):
        """测试仪表板概览缓存"""
        params = {"project_id": 1, "date_range": "30d"}
        data = {
            "metric_cards": [
                {"title": "总缺陷数", "value": 42},
                {"title": "覆盖率", "value": "85%"}
            ]
        }
        
        # 测试设置缓存
        result = await cache_service.set_dashboard_overview(data, params)
        assert result is True
        
        # 测试获取缓存
        cached_data = await cache_service.get_dashboard_overview(params)
        assert cached_data == data
        
        # 测试不同参数获取不到缓存
        different_params = {"project_id": 2, "date_range": "30d"}
        cached_data = await cache_service.get_dashboard_overview(different_params)
        assert cached_data is None
    
    async def test_project_stats_cache(self, cache_service):
        """测试项目统计缓存"""
        project_id = 123
        params = {"include_history": True}
        data = {
            "defect_count": 15,
            "coverage_rate": 88.5,
            "quality_score": 92
        }
        
        # 测试设置缓存
        result = await cache_service.set_project_stats(project_id, data, params)
        assert result is True
        
        # 测试获取缓存
        cached_data = await cache_service.get_project_stats(project_id, params)
        assert cached_data == data
    
    async def test_clear_pattern(self, cache_service):
        """测试模式清除"""
        # 设置多个相关缓存
        await cache_service.set("project:1:stats", {"data": "test1"})
        await cache_service.set("project:2:stats", {"data": "test2"})
        await cache_service.set("dashboard:overview", {"data": "test3"})
        
        # 清除项目相关缓存
        result = await cache_service.clear_pattern("project:*")
        assert result is True
        
        # 验证项目缓存已清除
        assert await cache_service.get("project:1:stats") is None
        assert await cache_service.get("project:2:stats") is None
        
        # 验证其他缓存仍存在
        assert await cache_service.get("dashboard:overview") is not None
    
    async def test_invalidate_project_cache(self, cache_service):
        """测试项目缓存失效"""
        project_id = 456
        
        # 设置相关缓存
        await cache_service.set_project_stats(project_id, {"data": "test"})
        await cache_service.set_dashboard_overview({"data": "test"})
        await cache_service.set_defect_trends({"data": "test"})
        
        # 失效项目缓存
        await cache_service.invalidate_project_cache(project_id)
        
        # 验证相关缓存已清除
        assert await cache_service.get_project_stats(project_id) is None
        assert await cache_service.get_dashboard_overview() is None
    
    async def test_cache_error_handling(self, cache_service):
        """测试缓存错误处理"""
        # 模拟Redis连接错误
        cache_service.redis_client = None
        
        # 测试获取缓存时的错误处理
        result = await cache_service.get("test_key")
        assert result is None
        
        # 测试设置缓存时的错误处理
        result = await cache_service.set("test_key", {"data": "test"})
        assert result is False
        
        # 测试删除缓存时的错误处理
        result = await cache_service.delete("test_key")
        assert result is False
    
    @patch('backend.services.cache_service.get_redis')
    async def test_redis_connection_retry(self, mock_get_redis, cache_service):
        """测试Redis连接重试"""
        # 模拟连接失败然后成功
        mock_redis = AsyncMock()
        mock_get_redis.return_value = mock_redis
        
        # 第一次调用失败
        mock_redis.get.side_effect = [ConnectionError("Connection failed"), '{"data": "test"}']
        
        # 应该能够处理连接错误
        result = await cache_service.get("test_key")
        assert result is None
    
    async def test_cache_serialization(self, cache_service):
        """测试缓存序列化"""
        # 测试复杂数据结构
        complex_data = {
            "string": "test",
            "number": 42,
            "float": 3.14,
            "boolean": True,
            "null": None,
            "list": [1, 2, 3],
            "nested": {
                "key": "value",
                "array": ["a", "b", "c"]
            }
        }
        
        # 设置和获取复杂数据
        await cache_service.set("complex_data", complex_data)
        cached_data = await cache_service.get("complex_data")
        
        assert cached_data == complex_data
        assert isinstance(cached_data["nested"], dict)
        assert isinstance(cached_data["list"], list)
    
    async def test_ttl_configuration(self, cache_service):
        """测试TTL配置"""
        # 验证默认TTL配置
        assert cache_service.ttl_config["dashboard_overview"] == 300
        assert cache_service.ttl_config["dashboard_trends"] == 1800
        assert cache_service.ttl_config["project_stats"] == 600
        
        # 测试使用配置的TTL
        data = {"test": "data"}
        await cache_service.set_dashboard_overview(data)
        
        # 验证缓存已设置（无法直接测试TTL，但可以验证缓存存在）
        cached_data = await cache_service.get_dashboard_overview()
        assert cached_data == data
