# 质量大盘第四阶段迁移指南

## 📋 迁移概述

本指南详细说明了从传统pip管理方式迁移到uv包管理系统，以及第四阶段性能优化和第三方集成功能的迁移步骤。

### 🎯 迁移目标

- **包管理迁移**: 从pip + requirements.txt 迁移到 uv + pyproject.toml
- **启动方式优化**: 使用优化的uvicorn配置和启动脚本
- **新功能集成**: 性能监控、缓存系统、第三方集成
- **测试体系完善**: 单元测试、集成测试、性能测试

---

## 🔄 迁移步骤

### 第一步：备份现有环境

```bash
# 1. 备份当前项目
cp -r quality-dashboard quality-dashboard-backup

# 2. 导出当前依赖
pip freeze > requirements_backup.txt

# 3. 备份配置文件
cp .env .env.backup 2>/dev/null || echo "No .env file found"

# 4. 备份数据库（如果需要）
pg_dump quality_dashboard > db_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 第二步：安装uv包管理器

```bash
# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 重新加载shell配置
source ~/.bashrc  # 或 source ~/.zshrc

# 验证安装
uv --version
```

### 第三步：迁移依赖管理

```bash
# 1. 删除旧的虚拟环境（如果存在）
rm -rf venv/ .venv/

# 2. 运行环境设置脚本
chmod +x scripts/setup.sh
./scripts/setup.sh

# 3. 验证新环境
uv run python --version
uv run pip list
```

### 第四步：配置环境变量

```bash
# 1. 检查新的环境变量模板
cat .env

# 2. 根据备份文件更新配置
# 比较 .env 和 .env.backup，添加缺失的配置

# 3. 新增的第四阶段配置项
cat >> .env << EOF

# === 第四阶段新增配置 ===
# 性能监控
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_ALERT_THRESHOLD_CPU=80
PERFORMANCE_ALERT_THRESHOLD_MEMORY=85

# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_MAX_MEMORY=512mb

# 第三方集成
SYNC_ENABLED=true
SYNC_INTERVAL_JIRA=900
SYNC_INTERVAL_SONARQUBE=3600
SYNC_INTERVAL_JENKINS=1800
EOF
```

### 第五步：数据库迁移

```bash
# 1. 检查数据库连接
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import engine
async def test_db():
    async with engine.begin() as conn:
        result = await conn.execute('SELECT version()')
        print('数据库连接正常:', result.scalar())
asyncio.run(test_db())
"

# 2. 运行数据库迁移（如果有新表结构）
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import init_db
asyncio.run(init_db())
print('数据库初始化完成')
"

# 3. 验证新表结构
psql -h ************* -p 38761 -U postgres -d quality_dashboard -c "\dt"
```

### 第六步：测试新功能

```bash
# 1. 运行基础测试
uv run python scripts/run_tests.py unit

# 2. 测试第三方集成
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.services.integrations.jira_integration import jira_integration

async def test_integrations():
    try:
        async with jira_integration as jira:
            result = await jira.test_connection()
            print('JIRA集成测试:', '成功' if result else '失败')
    except Exception as e:
        print('JIRA集成测试失败:', e)

asyncio.run(test_integrations())
"

# 3. 测试缓存功能
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.services.cache_service import CacheService

async def test_cache():
    cache = CacheService()
    await cache.set('test_key', {'data': 'test'})
    result = await cache.get('test_key')
    print('缓存测试:', '成功' if result else '失败')

asyncio.run(test_cache())
"
```

### 第七步：启动服务验证

```bash
# 1. 使用新的启动脚本
uv run python scripts/start.py --env development &
SERVER_PID=$!

# 2. 等待服务启动
sleep 5

# 3. 测试健康检查
curl -f http://localhost:8000/health || echo "健康检查失败"

# 4. 测试新的API端点
curl -f http://localhost:8000/api/integrations/status || echo "集成状态API失败"
curl -f http://localhost:8000/api/performance/metrics || echo "性能监控API失败"

# 5. 停止测试服务
kill $SERVER_PID
```

---

## 🔧 配置迁移对照表

### 启动方式变更

| 旧方式 | 新方式 | 说明 |
|--------|--------|------|
| `python main.py` | `uv run python scripts/start.py` | 使用优化启动脚本 |
| `uvicorn main:app` | `uv run uvicorn backend.main:app` | 通过uv运行 |
| `pip install -r requirements.txt` | `uv pip install -e .` | 使用uv安装依赖 |

### 环境变量新增项

```bash
# 性能监控相关
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_ALERT_THRESHOLD_CPU=80
PERFORMANCE_ALERT_THRESHOLD_MEMORY=85
PERFORMANCE_METRICS_RETENTION_HOURS=168

# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_DASHBOARD_TTL=300
CACHE_TRENDS_TTL=1800
CACHE_STATS_TTL=600

# 数据同步配置
SYNC_ENABLED=true
SYNC_INTERVAL_JIRA=900
SYNC_INTERVAL_SONARQUBE=3600
SYNC_INTERVAL_JENKINS=1800
SYNC_RETRY_ATTEMPTS=3
SYNC_RETRY_DELAY=60

# 第三方集成配置
JIRA_URL=
JIRA_USERNAME=
JIRA_TOKEN=
JIRA_PROJECT_KEYS=

SONARQUBE_URL=
SONARQUBE_TOKEN=
SONARQUBE_PROJECT_KEYS=

JENKINS_URL=
JENKINS_USERNAME=
JENKINS_TOKEN=
JENKINS_JOB_NAMES=
```

### API端点变更

| 功能 | 新端点 | 说明 |
|------|--------|------|
| 健康检查 | `/health` | 系统健康状态 |
| 就绪检查 | `/health/ready` | 服务就绪状态 |
| 存活检查 | `/health/live` | 服务存活状态 |
| 集成状态 | `/api/integrations/status` | 第三方集成状态 |
| 同步任务 | `/api/sync/tasks` | 数据同步任务管理 |
| 同步历史 | `/api/sync/history` | 同步历史记录 |
| 性能指标 | `/api/performance/metrics` | 系统性能监控 |

---

## 🧪 验证清单

### 功能验证

- [ ] **基础功能**
  - [ ] 应用正常启动
  - [ ] 数据库连接正常
  - [ ] Redis连接正常
  - [ ] API端点响应正常

- [ ] **新增功能**
  - [ ] 缓存系统工作正常
  - [ ] 性能监控数据收集
  - [ ] 第三方集成连接测试
  - [ ] 数据同步任务运行

- [ ] **性能验证**
  - [ ] API响应时间 < 500ms
  - [ ] 缓存命中率 > 80%
  - [ ] 内存使用 < 80%
  - [ ] CPU使用 < 70%

### 测试验证

```bash
# 运行完整测试套件
uv run python scripts/run_tests.py all

# 运行性能测试
uv run python scripts/run_tests.py performance

# 生成测试报告
uv run python scripts/run_tests.py report
```

---

## 🚨 回滚计划

如果迁移过程中遇到问题，可以按以下步骤回滚：

### 快速回滚

```bash
# 1. 停止新服务
pkill -f "python scripts/start.py"

# 2. 恢复备份
rm -rf quality-dashboard
mv quality-dashboard-backup quality-dashboard
cd quality-dashboard

# 3. 恢复旧环境
python -m venv venv
source venv/bin/activate
pip install -r requirements_backup.txt

# 4. 恢复配置
cp .env.backup .env

# 5. 启动旧服务
python main.py
```

### 数据回滚

```bash
# 如果需要恢复数据库
psql -h ************* -p 38761 -U postgres -d quality_dashboard < db_backup_*.sql
```

---

## 📊 迁移后优化建议

### 1. 性能优化

```bash
# 调整缓存配置
# 根据实际使用情况调整TTL值

# 优化数据库连接池
# 在 .env 中调整连接池大小

# 监控系统资源
# 定期检查性能指标
```

### 2. 监控配置

```bash
# 设置性能告警
# 配置邮件通知

# 定期备份
# 设置自动备份脚本

# 日志轮转
# 配置日志文件大小限制
```

### 3. 安全加固

```bash
# 更新密钥
# 生成新的SECRET_KEY

# 配置HTTPS
# 在生产环境启用SSL

# 访问控制
# 配置防火墙规则
```

---

## 📞 技术支持

### 迁移问题排查

1. **依赖安装失败**
   ```bash
   # 清理缓存重试
   uv cache clean
   uv pip install -e . --force-reinstall
   ```

2. **数据库连接问题**
   ```bash
   # 检查网络连接
   telnet ************* 38761
   
   # 检查认证信息
   psql -h ************* -p 38761 -U postgres -d quality_dashboard
   ```

3. **Redis连接问题**
   ```bash
   # 检查Redis连接
   redis-cli -h ************* -p 38762 -a redis_f4tEn5 ping
   ```

4. **第三方集成问题**
   ```bash
   # 检查网络连通性
   curl -I https://your-jira-instance.com
   
   # 验证认证信息
   curl -u username:token https://your-jira-instance.com/rest/api/2/myself
   ```

### 联系方式

如遇到迁移问题，请联系技术团队并提供：
- 错误日志
- 环境配置
- 复现步骤

**迁移状态**: ✅ 迁移指南完成  
**最后更新**: 2024年12月  
**版本**: 1.0.0
