# 质量大盘部署指南

## 📋 部署概述

本指南详细说明了质量大盘项目的部署流程，包括开发环境、测试环境和生产环境的配置。

### 🎯 支持的部署方式

- **开发环境**: 本地开发和调试
- **测试环境**: 集成测试和验收测试
- **生产环境**: 正式生产部署
- **Docker容器**: 容器化部署
- **云平台**: 支持各种云平台部署

---

## 🔧 环境要求

### 基础要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Python | >= 3.11 | 后端运行环境 |
| Node.js | >= 18.0 | 前端构建环境 |
| uv | >= 0.1.0 | Python包管理器 |
| PostgreSQL | >= 13.0 | 主数据库 |
| Redis | >= 6.0 | 缓存和会话存储 |

### 系统资源

| 环境 | CPU | 内存 | 磁盘 | 网络 |
|------|-----|------|------|------|
| 开发环境 | 2核 | 4GB | 20GB | 10Mbps |
| 测试环境 | 4核 | 8GB | 50GB | 100Mbps |
| 生产环境 | 8核 | 16GB | 100GB | 1Gbps |

---

## 🚀 快速部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd quality-dashboard

# 安装uv（如果未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 运行环境设置脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

### 3. 启动服务

```bash
# 开发环境启动
uv run python scripts/start.py --env development

# 或使用uvicorn直接启动
uv run uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000
```

---

## 📝 详细配置

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# === 应用配置 ===
DEBUG=true
SECRET_KEY=your-secret-key-here
ENVIRONMENT=development
PORT=8000
WORKERS=4

# === 数据库配置 ===
DATABASE_URL=***************************************************************************

# === Redis配置 ===
REDIS_URL=redis://:redis_f4tEn5@*************:38762/3
REDIS_PASSWORD=redis_f4tEn5
REDIS_DB=3

# === 第三方集成配置 ===
# JIRA配置
JIRA_URL=https://your-jira-instance.com
JIRA_USERNAME=your-username
JIRA_TOKEN=your-api-token

# SonarQube配置
SONARQUBE_URL=https://your-sonarqube-instance.com
SONARQUBE_TOKEN=your-api-token

# Jenkins配置
JENKINS_URL=https://your-jenkins-instance.com
JENKINS_USERNAME=your-username
JENKINS_TOKEN=your-api-token

# === 邮件配置 ===
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
FROM_EMAIL=<EMAIL>

# === 前端配置 ===
FRONTEND_URL=http://localhost:3000

# === 日志配置 ===
LOG_LEVEL=INFO
LOG_FILE=logs/quality_dashboard.log

# === 安全配置 ===
FORCE_HTTPS=false
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

### 数据库初始化

```bash
# 创建数据库表
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import init_db
asyncio.run(init_db())
"

# 运行数据库迁移（如果有）
uv run alembic upgrade head
```

### Redis配置验证

```bash
# 测试Redis连接
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import get_redis
async def test_redis():
    redis_client = await get_redis()
    await redis_client.ping()
    print('Redis连接正常')
asyncio.run(test_redis())
"
```

---

## 🐳 Docker部署

### Dockerfile

```dockerfile
# 后端Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 复制项目文件
COPY pyproject.toml .
COPY backend/ backend/
COPY scripts/ scripts/

# 安装Python依赖
RUN uv pip install --system -e .

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uv", "run", "python", "scripts/start.py", "--env", "production"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  quality-dashboard:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/quality_dashboard
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=quality_dashboard
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - quality-dashboard
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 构建和运行

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f quality-dashboard

# 停止服务
docker-compose down
```

---

## 🌐 生产环境部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y \
    python3.11 \
    python3.11-venv \
    postgresql-client \
    redis-tools \
    nginx \
    supervisor \
    curl \
    git

# 安装uv
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 应用部署

```bash
# 创建应用用户
sudo useradd -m -s /bin/bash quality-dashboard
sudo su - quality-dashboard

# 克隆代码
git clone <repository-url> /home/<USER>/app
cd /home/<USER>/app

# 设置环境
./scripts/setup.sh

# 配置生产环境变量
cp .env.example .env
# 编辑 .env 文件，设置生产环境配置
```

### 3. Supervisor配置

创建 `/etc/supervisor/conf.d/quality-dashboard.conf`:

```ini
[program:quality-dashboard]
command=/home/<USER>/app/.venv/bin/python scripts/start.py --env production
directory=/home/<USER>/app
user=quality-dashboard
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/quality-dashboard.log
environment=PATH="/home/<USER>/app/.venv/bin"
```

### 4. Nginx配置

创建 `/etc/nginx/sites-available/quality-dashboard`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # 静态文件
    location /static/ {
        alias /home/<USER>/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000/health;
        access_log off;
    }
}
```

### 5. 启动服务

```bash
# 启用Nginx站点
sudo ln -s /etc/nginx/sites-available/quality-dashboard /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# 启动Supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start quality-dashboard

# 检查状态
sudo supervisorctl status quality-dashboard
```

---

## 🔍 监控和维护

### 健康检查

```bash
# 应用健康检查
curl http://localhost:8000/health

# 就绪检查
curl http://localhost:8000/health/ready

# 存活检查
curl http://localhost:8000/health/live
```

### 日志管理

```bash
# 查看应用日志
tail -f logs/quality_dashboard.log

# 查看Supervisor日志
sudo tail -f /var/log/quality-dashboard.log

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 性能监控

```bash
# 系统资源监控
htop
iostat -x 1
free -h

# 应用性能监控
curl http://localhost:8000/api/performance/metrics
```

### 备份策略

```bash
# 数据库备份
pg_dump -h localhost -U postgres quality_dashboard > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis备份
redis-cli BGSAVE

# 应用配置备份
tar -czf config_backup_$(date +%Y%m%d_%H%M%S).tar.gz .env logs/ data/
```

---

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库状态
   pg_isready -h localhost -p 5432
   
   # 检查连接配置
   psql -h localhost -U postgres -d quality_dashboard
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis状态
   redis-cli ping
   
   # 检查Redis配置
   redis-cli info
   ```

3. **应用启动失败**
   ```bash
   # 检查日志
   tail -f logs/quality_dashboard.log
   
   # 检查端口占用
   netstat -tlnp | grep 8000
   ```

4. **第三方集成失败**
   ```bash
   # 测试JIRA连接
   curl -u username:token https://your-jira-instance.com/rest/api/2/myself
   
   # 测试SonarQube连接
   curl -H "Authorization: Bearer token" https://your-sonarqube-instance.com/api/system/status
   ```

### 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 定期执行VACUUM和ANALYZE
   - 监控慢查询

2. **缓存优化**
   - 调整Redis内存配置
   - 优化缓存TTL设置
   - 监控缓存命中率

3. **应用优化**
   - 调整worker进程数
   - 优化数据库连接池
   - 启用gzip压缩

---

## 📞 技术支持

如有部署问题，请联系技术团队或查看项目文档。

**部署状态**: ✅ 生产就绪  
**最后更新**: 2024年12月  
**版本**: 1.0.0
