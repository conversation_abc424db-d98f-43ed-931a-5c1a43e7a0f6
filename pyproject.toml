[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "quality-dashboard"
version = "1.0.0"
description = "智能化质量管理平台"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Quality Dashboard Team", email = "<EMAIL>"}
]
requires-python = ">=3.11"

dependencies = [
    # Web Framework
    "fastapi>=0.104.1,<0.105.0",
    "uvicorn[standard]>=0.24.0,<0.25.0",
    
    # Data Validation
    "pydantic>=2.5.0,<3.0.0",
    "python-multipart>=0.0.6,<0.1.0",
    
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0,<4.0.0",
    "passlib[bcrypt]>=1.7.4,<2.0.0",
    
    # Configuration
    "python-dotenv>=1.0.0,<2.0.0",
    
    # Database
    "sqlalchemy>=2.0.23,<3.0.0",
    "alembic>=1.13.0,<2.0.0",
    "psycopg2-binary>=2.9.9,<3.0.0",
    "asyncpg>=0.29.0,<0.30.0",
    "aiosqlite>=0.19.0,<0.20.0",
    
    # Cache & Message Queue
    "redis>=5.0.1,<6.0.0",
    "celery>=5.3.4,<6.0.0",
    
    # HTTP Client
    "aiohttp>=3.9.0,<4.0.0",
    "httpx>=0.25.2,<0.26.0",
    
    # Data Processing
    "pandas>=2.1.4,<3.0.0",
    "openpyxl>=3.1.2,<4.0.0",
    "xlsxwriter>=3.1.9,<4.0.0",
    
    # Template Engine
    "jinja2>=3.1.2,<4.0.0",
    
    # File Operations
    "aiofiles>=23.2.1,<24.0.0",
    
    # System Monitoring
    "psutil>=5.9.0,<6.0.0",
    
    # Logging
    "structlog>=23.2.0,<24.0.0",
    "rich>=13.7.0,<14.0.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.3,<8.0.0",
    "pytest-asyncio>=0.21.1,<0.22.0",
    "pytest-cov>=4.1.0,<5.0.0",
    "pytest-mock>=3.12.0,<4.0.0",
    "httpx>=0.25.2,<0.26.0",
    
    # Code Quality
    "black>=23.12.0,<24.0.0",
    "isort>=5.13.0,<6.0.0",
    "flake8>=6.1.0,<7.0.0",
    "mypy>=1.8.0,<2.0.0",
    "pre-commit>=3.6.0,<4.0.0",
    
    # Documentation
    "mkdocs>=1.5.3,<2.0.0",
    "mkdocs-material>=9.5.0,<10.0.0",
]

production = [
    # Production ASGI Server
    "gunicorn>=21.2.0,<22.0.0",
    
    # Monitoring
    "prometheus-client>=0.19.0,<0.20.0",
    "sentry-sdk[fastapi]>=1.40.0,<2.0.0",
]

[project.urls]
Homepage = "https://github.com/quality-dashboard/quality-dashboard"
Documentation = "https://quality-dashboard.readthedocs.io"
Repository = "https://github.com/quality-dashboard/quality-dashboard.git"
Issues = "https://github.com/quality-dashboard/quality-dashboard/issues"

[project.scripts]
quality-dashboard = "backend.main:main"

[tool.hatch.build.targets.wheel]
packages = ["backend"]

[tool.hatch.build]
include = [
    "backend/**/*.py",
    "scripts/**/*.py",
    "*.md",
    "LICENSE"
]
exclude = [
    "tests/",
    "frontend/",
    "logs/",
    "reports/",
    "*.pyc",
    "__pycache__/"
]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["backend"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "redis.*",
    "celery.*",
    "psutil.*",
    "aiohttp.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]

[tool.coverage.run]
source = ["backend"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
