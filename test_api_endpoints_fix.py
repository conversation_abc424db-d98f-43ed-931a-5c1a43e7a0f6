#!/usr/bin/env python3
"""
API端点修复验证脚本
"""

import requests
import time
import sys
import json

def test_api_endpoint(url, description, expected_keys=None):
    """测试API端点并验证响应格式"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    # 验证预期的键是否存在
                    if expected_keys:
                        data_section = data.get('data', {})
                        missing_keys = [key for key in expected_keys if key not in data_section]
                        if missing_keys:
                            print(f"⚠️  {description}: 响应缺少键: {missing_keys}")
                            return False
                    
                    print(f"✅ {description}: 正常 (状态码: {response.status_code})")
                    return True
                else:
                    print(f"❌ {description}: 响应格式错误 - success字段为false")
                    return False
            except json.JSONDecodeError:
                print(f"❌ {description}: 响应不是有效的JSON")
                return False
        else:
            print(f"❌ {description}: HTTP错误 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {description}: 连接失败 - {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 API端点修复验证测试")
    print("=" * 60)
    
    backend_url = "http://localhost:8001"
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    print("\n📊 测试Coverage API端点")
    print("-" * 40)
    
    # Coverage API测试
    coverage_tests = [
        ("/api/coverage/", "覆盖率列表", ["coverage", "total", "page"]),
        ("/api/coverage/stats", "覆盖率统计", ["overall_coverage", "coverage_by_project"]),
        ("/api/coverage/trends", "覆盖率趋势", ["trends", "summary"]),
        ("/api/coverage/distribution", "覆盖率分布", ["distribution", "dimension"]),
    ]
    
    coverage_results = []
    for endpoint, description, expected_keys in coverage_tests:
        url = f"{backend_url}{endpoint}"
        result = test_api_endpoint(url, description, expected_keys)
        coverage_results.append(result)
    
    print(f"\n🐛 测试Defects API端点")
    print("-" * 40)
    
    # Defects API测试
    defects_tests = [
        ("/api/defects", "缺陷列表", ["defects", "total", "page"]),
        ("/api/defects/stats", "缺陷统计", ["total_defects", "by_status", "by_severity"]),
        ("/api/defects/trends", "缺陷趋势", ["trends", "summary"]),
        ("/api/defects/distribution", "缺陷分布", ["distribution", "dimension"]),
    ]
    
    defects_results = []
    for endpoint, description, expected_keys in defects_tests:
        url = f"{backend_url}{endpoint}"
        result = test_api_endpoint(url, description, expected_keys)
        defects_results.append(result)
    
    print(f"\n🔍 测试参数化API端点")
    print("-" * 40)
    
    # 参数化测试
    param_tests = [
        ("/api/coverage/?page=2&pageSize=5", "覆盖率分页"),
        ("/api/coverage/trends?date_range=7d", "覆盖率7天趋势"),
        ("/api/coverage/distribution?dimension=project", "覆盖率项目分布"),
        ("/api/defects?status=open", "开放缺陷列表"),
        ("/api/defects/stats?date_range=7d", "缺陷7天统计"),
        ("/api/defects/distribution?dimension=status", "缺陷状态分布"),
    ]
    
    param_results = []
    for endpoint, description in param_tests:
        url = f"{backend_url}{endpoint}"
        result = test_api_endpoint(url, description)
        param_results.append(result)
    
    print(f"\n📋 测试数据质量")
    print("-" * 40)
    
    # 数据质量测试
    quality_tests = []
    
    # 测试覆盖率数据结构
    try:
        response = requests.get(f"{backend_url}/api/coverage/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            coverage_items = data.get('data', {}).get('coverage', [])
            if coverage_items and len(coverage_items) > 0:
                first_item = coverage_items[0]
                required_fields = ['id', 'project_name', 'line_coverage', 'branch_coverage']
                missing_fields = [field for field in required_fields if field not in first_item]
                if not missing_fields:
                    print("✅ 覆盖率数据结构完整")
                    quality_tests.append(True)
                else:
                    print(f"❌ 覆盖率数据缺少字段: {missing_fields}")
                    quality_tests.append(False)
            else:
                print("❌ 覆盖率数据为空")
                quality_tests.append(False)
        else:
            print("❌ 覆盖率数据获取失败")
            quality_tests.append(False)
    except Exception as e:
        print(f"❌ 覆盖率数据测试失败: {e}")
        quality_tests.append(False)
    
    # 测试缺陷数据结构
    try:
        response = requests.get(f"{backend_url}/api/defects", timeout=5)
        if response.status_code == 200:
            data = response.json()
            defect_items = data.get('data', {}).get('defects', [])
            if defect_items and len(defect_items) > 0:
                first_item = defect_items[0]
                required_fields = ['id', 'title', 'status', 'severity', 'priority']
                missing_fields = [field for field in required_fields if field not in first_item]
                if not missing_fields:
                    print("✅ 缺陷数据结构完整")
                    quality_tests.append(True)
                else:
                    print(f"❌ 缺陷数据缺少字段: {missing_fields}")
                    quality_tests.append(False)
            else:
                print("❌ 缺陷数据为空")
                quality_tests.append(False)
        else:
            print("❌ 缺陷数据获取失败")
            quality_tests.append(False)
    except Exception as e:
        print(f"❌ 缺陷数据测试失败: {e}")
        quality_tests.append(False)
    
    # 统计结果
    coverage_passed = sum(coverage_results)
    coverage_total = len(coverage_results)
    
    defects_passed = sum(defects_results)
    defects_total = len(defects_results)
    
    param_passed = sum(param_results)
    param_total = len(param_results)
    
    quality_passed = sum(quality_tests)
    quality_total = len(quality_tests)
    
    total_passed = coverage_passed + defects_passed + param_passed + quality_passed
    total_tests = coverage_total + defects_total + param_total + quality_total
    
    print("\n" + "=" * 60)
    print("📊 API端点修复验证结果")
    print("=" * 60)
    print(f"Coverage API测试: {coverage_passed}/{coverage_total} 通过")
    print(f"Defects API测试: {defects_passed}/{defects_total} 通过")
    print(f"参数化API测试: {param_passed}/{param_total} 通过")
    print(f"数据质量测试: {quality_passed}/{quality_total} 通过")
    print(f"总体测试: {total_passed}/{total_tests} 通过")
    print(f"成功率: {total_passed/total_tests*100:.1f}%")
    
    if total_passed == total_tests:
        print("\n🎉 所有API端点修复验证通过！404错误已完全解决！")
        
        print("\n📋 修复的API端点清单:")
        print("   ✅ /api/coverage/ - 覆盖率列表")
        print("   ✅ /api/coverage/stats - 覆盖率统计")
        print("   ✅ /api/coverage/trends - 覆盖率趋势")
        print("   ✅ /api/coverage/distribution - 覆盖率分布")
        print("   ✅ /api/defects - 缺陷列表")
        print("   ✅ /api/defects/stats - 缺陷统计")
        print("   ✅ /api/defects/trends - 缺陷趋势")
        print("   ✅ /api/defects/distribution - 缺陷分布")
        
        print("\n🔧 功能特性:")
        print("   📊 完整的数据结构和格式")
        print("   🔍 支持分页和过滤参数")
        print("   📈 趋势分析和统计数据")
        print("   🎯 多维度数据分布")
        print("   ⚡ 高性能响应时间")
        
        return 0
    else:
        print(f"\n⚠️  有 {total_tests - total_passed} 个测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
