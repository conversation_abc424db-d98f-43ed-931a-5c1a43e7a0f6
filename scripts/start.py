#!/usr/bin/env python3
"""
质量大盘启动脚本
"""

import os
import sys
import argparse
import uvicorn
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 延迟导入，避免模块路径问题


def main():
    """主启动函数"""
    parser = argparse.ArgumentParser(description="质量大盘服务启动器")
    parser.add_argument(
        "--env",
        choices=["development", "production", "testing"],
        default="development",
        help="运行环境"
    )
    parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="绑定主机地址"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="绑定端口"
    )
    parser.add_argument(
        "--workers",
        type=int,
        help="工作进程数（仅生产环境）"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用热重载（开发环境）"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别"
    )

    args = parser.parse_args()

    # 设置环境变量
    os.environ["ENVIRONMENT"] = args.env
    if args.workers:
        os.environ["WORKERS"] = str(args.workers)

    # 简化配置，直接使用uvicorn参数
    config = {
        "app": "backend.main:app",
        "host": args.host,
        "port": args.port,
        "log_level": args.log_level.lower(),
        "access_log": True,
    }

    # 开发环境启用热重载
    if args.env == "development" or args.reload:
        config.update({
            "reload": True,
            "reload_dirs": ["backend"],
        })

    # 环境检查
    try:
        from backend.config.settings import Settings
        settings = Settings()
        check_environment(settings, args.env)
    except ImportError as e:
        print(f"⚠️  配置模块导入失败: {e}")
        print("使用基础配置启动...")

    print(f"🚀 启动质量大盘服务...")
    print(f"📍 环境: {args.env}")
    print(f"🌐 地址: http://{args.host}:{args.port}")
    print(f"📊 工作进程: {config.get('workers', 1)}")
    print(f"🔄 热重载: {'启用' if config.get('reload', False) else '禁用'}")
    print(f"📝 日志级别: {args.log_level}")

    # 启动服务器
    try:
        uvicorn.run(**config)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


def check_environment(settings, env: str):
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查数据库连接
    if not settings.DATABASE_URL:
        print("⚠️  警告: 数据库URL未配置")
    else:
        print("✅ 数据库配置正常")
    
    # 检查Redis连接
    if not settings.REDIS_URL:
        print("⚠️  警告: Redis URL未配置")
    else:
        print("✅ Redis配置正常")
    
    # 检查第三方集成配置
    integrations = []
    if settings.JIRA_URL and settings.JIRA_USERNAME and settings.JIRA_TOKEN:
        integrations.append("JIRA")
    if settings.SONARQUBE_URL and settings.SONARQUBE_TOKEN:
        integrations.append("SonarQube")
    if settings.JENKINS_URL and settings.JENKINS_USERNAME and settings.JENKINS_TOKEN:
        integrations.append("Jenkins")
    
    if integrations:
        print(f"✅ 第三方集成: {', '.join(integrations)}")
    else:
        print("⚠️  警告: 未配置第三方集成")
    
    # 生产环境额外检查
    if env == "production":
        check_production_requirements(settings)


def check_production_requirements(settings):
    """检查生产环境要求"""
    print("🔒 检查生产环境要求...")
    
    issues = []
    
    # 检查调试模式
    if settings.DEBUG:
        issues.append("DEBUG模式应在生产环境中禁用")
    
    # 检查密钥配置
    if not os.getenv("SECRET_KEY"):
        issues.append("SECRET_KEY环境变量未设置")
    
    # 检查HTTPS配置
    if not os.getenv("FORCE_HTTPS"):
        issues.append("建议在生产环境中启用HTTPS")
    
    # 检查日志配置
    if not os.path.exists("logs"):
        print("📁 创建日志目录...")
        os.makedirs("logs", exist_ok=True)
    
    if issues:
        print("⚠️  生产环境配置问题:")
        for issue in issues:
            print(f"   - {issue}")
        
        response = input("是否继续启动? (y/N): ")
        if response.lower() != 'y':
            print("❌ 启动已取消")
            sys.exit(1)
    else:
        print("✅ 生产环境配置检查通过")


if __name__ == "__main__":
    main()
