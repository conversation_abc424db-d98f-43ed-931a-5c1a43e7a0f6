#!/usr/bin/env python3
"""
测试运行脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_command(command, description):
    """运行命令并处理结果"""
    print(f"\n🔄 {description}")
    print(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=project_root
        )
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            if result.stdout:
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            if result.stdout:
                print("标准输出:")
                print(result.stdout)
            return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False


def run_unit_tests():
    """运行单元测试"""
    command = [
        "uv", "run", "pytest", 
        "tests/unit/",
        "-v",
        "--tb=short",
        "-m", "unit"
    ]
    return run_command(command, "单元测试")


def run_integration_tests():
    """运行集成测试"""
    command = [
        "uv", "run", "pytest", 
        "tests/integration/",
        "-v",
        "--tb=short",
        "-m", "integration"
    ]
    return run_command(command, "集成测试")


def run_performance_tests():
    """运行性能测试"""
    command = [
        "uv", "run", "pytest", 
        "tests/performance/",
        "-v",
        "--tb=short",
        "-m", "performance"
    ]
    return run_command(command, "性能测试")


def run_all_tests():
    """运行所有测试"""
    command = [
        "uv", "run", "pytest", 
        "tests/",
        "-v",
        "--tb=short",
        "--cov=backend",
        "--cov-report=html",
        "--cov-report=term-missing"
    ]
    return run_command(command, "所有测试（包含覆盖率）")


def run_specific_test(test_path):
    """运行特定测试"""
    command = [
        "uv", "run", "pytest", 
        test_path,
        "-v",
        "--tb=short"
    ]
    return run_command(command, f"特定测试: {test_path}")


def run_code_quality_checks():
    """运行代码质量检查"""
    checks = [
        {
            "command": ["uv", "run", "black", "--check", "backend/"],
            "description": "代码格式检查 (Black)"
        },
        {
            "command": ["uv", "run", "isort", "--check-only", "backend/"],
            "description": "导入排序检查 (isort)"
        },
        {
            "command": ["uv", "run", "flake8", "backend/"],
            "description": "代码风格检查 (Flake8)"
        },
        {
            "command": ["uv", "run", "mypy", "backend/"],
            "description": "类型检查 (MyPy)"
        }
    ]
    
    all_passed = True
    for check in checks:
        if not run_command(check["command"], check["description"]):
            all_passed = False
    
    return all_passed


def fix_code_style():
    """修复代码风格"""
    fixes = [
        {
            "command": ["uv", "run", "black", "backend/"],
            "description": "代码格式化 (Black)"
        },
        {
            "command": ["uv", "run", "isort", "backend/"],
            "description": "导入排序 (isort)"
        }
    ]
    
    all_passed = True
    for fix in fixes:
        if not run_command(fix["command"], fix["description"]):
            all_passed = False
    
    return all_passed


def generate_test_report():
    """生成测试报告"""
    command = [
        "uv", "run", "pytest", 
        "tests/",
        "--html=reports/test_report.html",
        "--self-contained-html",
        "--cov=backend",
        "--cov-report=html:reports/coverage",
        "--junit-xml=reports/junit.xml"
    ]
    
    # 创建报告目录
    reports_dir = project_root / "reports"
    reports_dir.mkdir(exist_ok=True)
    
    success = run_command(command, "生成测试报告")
    
    if success:
        print(f"\n📊 测试报告已生成:")
        print(f"   - HTML报告: {reports_dir}/test_report.html")
        print(f"   - 覆盖率报告: {reports_dir}/coverage/index.html")
        print(f"   - JUnit XML: {reports_dir}/junit.xml")
    
    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="质量大盘测试运行器")
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "performance", "all", "quality", "fix", "report", "specific"],
        help="测试类型"
    )
    parser.add_argument(
        "--path",
        help="特定测试路径（仅用于 specific 类型）"
    )
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="详细输出"
    )
    parser.add_argument(
        "--parallel",
        "-p",
        action="store_true",
        help="并行运行测试"
    )
    
    args = parser.parse_args()
    
    print("🧪 质量大盘测试运行器")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    success = False
    
    if args.test_type == "unit":
        success = run_unit_tests()
    elif args.test_type == "integration":
        success = run_integration_tests()
    elif args.test_type == "performance":
        success = run_performance_tests()
    elif args.test_type == "all":
        success = run_all_tests()
    elif args.test_type == "quality":
        success = run_code_quality_checks()
    elif args.test_type == "fix":
        success = fix_code_style()
    elif args.test_type == "report":
        success = generate_test_report()
    elif args.test_type == "specific":
        if not args.path:
            print("❌ 特定测试需要提供 --path 参数")
            sys.exit(1)
        success = run_specific_test(args.path)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试运行成功!")
        sys.exit(0)
    else:
        print("💥 测试运行失败!")
        sys.exit(1)


def check_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查uv是否可用
    try:
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ uv 可用: {result.stdout.strip()}")
        else:
            print("❌ uv 不可用")
            return False
    except FileNotFoundError:
        print("❌ uv 未安装")
        return False
    
    # 检查虚拟环境
    venv_path = project_root / ".venv"
    if venv_path.exists():
        print("✅ 虚拟环境存在")
    else:
        print("⚠️  虚拟环境不存在，请运行 scripts/setup.sh")
        return False
    
    # 检查测试目录
    tests_dir = project_root / "tests"
    if tests_dir.exists():
        print("✅ 测试目录存在")
    else:
        print("❌ 测试目录不存在")
        return False
    
    # 检查环境变量文件
    env_file = project_root / ".env"
    if env_file.exists():
        print("✅ 环境变量文件存在")
    else:
        print("⚠️  环境变量文件不存在，将使用默认配置")
    
    return True


if __name__ == "__main__":
    main()
