#!/bin/bash

# 质量大盘项目环境设置脚本

set -e  # 遇到错误时退出

echo "🚀 质量大盘项目环境设置"
echo "=========================="

# 检查 uv 是否安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv 未安装，请先安装 uv"
    echo "安装命令: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "✅ uv 已安装: $(uv --version)"

# 检查 Python 版本
PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
REQUIRED_VERSION="3.11"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ Python 版本不符合要求"
    echo "当前版本: $PYTHON_VERSION"
    echo "要求版本: >= $REQUIRED_VERSION"
    exit 1
fi

echo "✅ Python 版本符合要求: $PYTHON_VERSION"

# 创建虚拟环境
echo "📦 创建虚拟环境..."
uv venv --python 3.11

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source .venv/bin/activate

# 安装依赖
echo "📥 安装项目依赖..."
uv pip install -e .

# 安装开发依赖
echo "🛠️  安装开发依赖..."
uv pip install -e ".[dev]"

# 创建必要的目录
echo "📁 创建项目目录..."
mkdir -p logs
mkdir -p data
mkdir -p uploads
mkdir -p exports

# 创建环境变量文件模板
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cat > .env << EOF
# 应用配置
DEBUG=true
SECRET_KEY=your-secret-key-here
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=*********************************************************/quality_dashboard

# Redis配置
REDIS_URL=redis://:redis_f4tEn5@*************:38762/3
REDIS_PASSWORD=redis_f4tEn5
REDIS_DB=3

# 第三方集成配置（可选）
JIRA_URL=
JIRA_USERNAME=
JIRA_TOKEN=

SONARQUBE_URL=
SONARQUBE_TOKEN=

JENKINS_URL=
JENKINS_USERNAME=
JENKINS_TOKEN=

# 邮件配置（可选）
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=

# 前端配置
FRONTEND_URL=http://localhost:3000

# 服务器配置
PORT=8000
WORKERS=4
EOF
    echo "✅ 环境变量文件已创建: .env"
    echo "⚠️  请根据实际情况修改 .env 文件中的配置"
else
    echo "✅ 环境变量文件已存在"
fi

# 设置 pre-commit hooks
echo "🔧 设置代码质量检查..."
uv run pre-commit install

# 创建 pre-commit 配置文件
if [ ! -f .pre-commit-config.yaml ]; then
    cat > .pre-commit-config.yaml << EOF
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict

  - repo: https://github.com/psf/black
    rev: 23.12.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
EOF
    echo "✅ pre-commit 配置已创建"
fi

# 初始化数据库（如果需要）
echo "🗄️  检查数据库连接..."
if uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import engine
async def test_db():
    try:
        async with engine.begin() as conn:
            await conn.execute('SELECT 1')
        print('✅ 数据库连接正常')
        return True
    except Exception as e:
        print(f'❌ 数据库连接失败: {e}')
        return False
result = asyncio.run(test_db())
sys.exit(0 if result else 1)
" 2>/dev/null; then
    echo "🔄 初始化数据库表..."
    uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import init_db
asyncio.run(init_db())
print('✅ 数据库初始化完成')
"
else
    echo "⚠️  数据库连接失败，请检查配置"
fi

# 检查 Redis 连接
echo "🔄 检查 Redis 连接..."
uv run python -c "
import asyncio
import sys
sys.path.append('.')
from backend.database import get_redis
async def test_redis():
    try:
        redis_client = await get_redis()
        await redis_client.ping()
        print('✅ Redis 连接正常')
        return True
    except Exception as e:
        print(f'❌ Redis 连接失败: {e}')
        return False
result = asyncio.run(test_redis())
" 2>/dev/null || echo "⚠️  Redis 连接失败，请检查配置"

echo ""
echo "🎉 环境设置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 修改 .env 文件中的配置"
echo "2. 启动开发服务器:"
echo "   uv run python scripts/start.py --env development"
echo ""
echo "3. 或使用 uvicorn 直接启动:"
echo "   uv run uvicorn backend.main:app --reload --host 0.0.0.0 --port 8000"
echo ""
echo "4. 运行测试:"
echo "   uv run pytest"
echo ""
echo "5. 代码格式化:"
echo "   uv run black backend/"
echo "   uv run isort backend/"
echo ""
echo "📚 更多信息请查看 README.md"
