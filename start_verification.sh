#!/bin/bash

# 覆盖率模块功能验证启动脚本
# 自动启动后端和前端服务，然后运行验证测试

echo "🚀 覆盖率模块功能验证启动脚本"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装Node.js"
    exit 1
fi

# 检查npm环境
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装npm"
    exit 1
fi

echo "✅ 环境检查通过"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"

# 检查目录结构
if [ ! -d "$BACKEND_DIR" ]; then
    echo "❌ 后端目录不存在: $BACKEND_DIR"
    exit 1
fi

if [ ! -d "$FRONTEND_DIR" ]; then
    echo "❌ 前端目录不存在: $FRONTEND_DIR"
    exit 1
fi

echo "✅ 项目结构检查通过"

# 安装后端依赖
echo "📦 检查后端依赖..."
cd "$BACKEND_DIR"

if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt 不存在"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv venv
fi

echo "🔧 激活虚拟环境并安装依赖..."
source venv/bin/activate
pip install -r requirements.txt

# 安装前端依赖
echo "📦 检查前端依赖..."
cd "$FRONTEND_DIR"

if [ ! -f "package.json" ]; then
    echo "❌ package.json 不存在"
    exit 1
fi

if [ ! -d "node_modules" ]; then
    echo "🔧 安装前端依赖..."
    npm install
fi

# 启动后端服务
echo "🚀 启动后端服务..."
cd "$BACKEND_DIR"
source venv/bin/activate

# 检查后端服务是否已经运行
if curl -s http://localhost:8000/health > /dev/null 2>&1; then
    echo "✅ 后端服务已在运行"
else
    echo "🔧 启动后端服务..."
    python main.py &
    BACKEND_PID=$!
    echo "后端服务PID: $BACKEND_PID"
    
    # 等待后端服务启动
    echo "⏳ 等待后端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            echo "✅ 后端服务启动成功"
            break
        fi
        sleep 1
        if [ $i -eq 30 ]; then
            echo "❌ 后端服务启动超时"
            kill $BACKEND_PID 2>/dev/null
            exit 1
        fi
    done
fi

# 启动前端服务
echo "🚀 启动前端服务..."
cd "$FRONTEND_DIR"

# 检查前端服务是否已经运行
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo "✅ 前端服务已在运行"
else
    echo "🔧 启动前端服务..."
    npm run dev &
    FRONTEND_PID=$!
    echo "前端服务PID: $FRONTEND_PID"
    
    # 等待前端服务启动
    echo "⏳ 等待前端服务启动..."
    for i in {1..60}; do
        if curl -s http://localhost:5173 > /dev/null 2>&1; then
            echo "✅ 前端服务启动成功"
            break
        fi
        sleep 1
        if [ $i -eq 60 ]; then
            echo "❌ 前端服务启动超时"
            kill $FRONTEND_PID 2>/dev/null
            kill $BACKEND_PID 2>/dev/null
            exit 1
        fi
    done
fi

# 运行验证测试
echo "🔍 运行覆盖率模块功能验证..."
cd "$SCRIPT_DIR"

python3 verify_coverage_module.py

VERIFICATION_RESULT=$?

# 清理进程（如果是脚本启动的）
if [ ! -z "$BACKEND_PID" ]; then
    echo "🧹 清理后端服务进程..."
    kill $BACKEND_PID 2>/dev/null
fi

if [ ! -z "$FRONTEND_PID" ]; then
    echo "🧹 清理前端服务进程..."
    kill $FRONTEND_PID 2>/dev/null
fi

# 显示结果
if [ $VERIFICATION_RESULT -eq 0 ]; then
    echo ""
    echo "🎉 覆盖率模块功能验证完成！"
    echo "✅ 验证通过，可以继续下一阶段开发"
else
    echo ""
    echo "⚠️  覆盖率模块功能验证发现问题"
    echo "❌ 请查看验证报告并修复问题"
fi

echo ""
echo "📄 详细验证报告请查看生成的JSON文件"
echo "🔍 如需手动测试，请访问: http://localhost:5173/coverage-management"

exit $VERIFICATION_RESULT
