#!/usr/bin/env python3
"""
质量大盘第四阶段实施验证脚本
"""

import asyncio
import aiohttp
import time
import json
from datetime import datetime

class QualityDashboardTester:
    """质量大盘测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_api_endpoint(self, endpoint, method="GET", expected_status=200):
        """测试API端点"""
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.request(method, url) as response:
                response_time = time.time() - start_time
                content = await response.text()
                
                success = response.status == expected_status
                
                return {
                    "endpoint": endpoint,
                    "method": method,
                    "status_code": response.status,
                    "response_time": response_time,
                    "success": success,
                    "content_length": len(content)
                }
        except Exception as e:
            response_time = time.time() - start_time
            return {
                "endpoint": endpoint,
                "method": method,
                "status_code": 0,
                "response_time": response_time,
                "success": False,
                "error": str(e)
            }
    
    async def test_performance_endpoints(self):
        """测试性能相关端点"""
        print("🚀 测试性能优化端点...")
        
        endpoints = [
            "/api/dashboard/overview",
            "/api/dashboard/trends", 
            "/api/dashboard/teams",
            "/api/performance/metrics"
        ]
        
        results = []
        for endpoint in endpoints:
            result = await self.test_api_endpoint(endpoint)
            results.append(result)
            
            status = "✅" if result["success"] else "❌"
            print(f"{status} {endpoint}: {result['response_time']:.3f}s (状态码: {result['status_code']})")
        
        return results
    
    async def test_integration_endpoints(self):
        """测试集成相关端点"""
        print("\n🔗 测试第三方集成端点...")
        
        endpoints = [
            "/api/integrations/status",
            "/api/sync/tasks",
            "/api/sync/history"
        ]
        
        results = []
        for endpoint in endpoints:
            result = await self.test_api_endpoint(endpoint)
            results.append(result)
            
            status = "✅" if result["success"] else "❌"
            print(f"{status} {endpoint}: {result['response_time']:.3f}s (状态码: {result['status_code']})")
        
        return results
    
    async def test_cache_performance(self):
        """测试缓存性能"""
        print("\n💾 测试缓存性能...")
        
        endpoint = "/api/dashboard/overview"
        
        # 第一次请求（缓存未命中）
        result1 = await self.test_api_endpoint(endpoint)
        print(f"第一次请求: {result1['response_time']:.3f}s")
        
        # 第二次请求（缓存命中）
        result2 = await self.test_api_endpoint(endpoint)
        print(f"第二次请求: {result2['response_time']:.3f}s")
        
        # 计算性能提升
        if result1["success"] and result2["success"]:
            improvement = ((result1["response_time"] - result2["response_time"]) / result1["response_time"]) * 100
            print(f"缓存性能提升: {improvement:.1f}%")
        
        return [result1, result2]
    
    async def test_concurrent_requests(self, endpoint="/api/dashboard/overview", concurrent_users=10):
        """测试并发请求"""
        print(f"\n⚡ 测试并发性能 ({concurrent_users} 并发用户)...")
        
        start_time = time.time()
        
        # 创建并发任务
        tasks = [self.test_api_endpoint(endpoint) for _ in range(concurrent_users)]
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        # 统计结果
        successful_requests = len([r for r in results if r["success"]])
        avg_response_time = sum(r["response_time"] for r in results) / len(results)
        max_response_time = max(r["response_time"] for r in results)
        min_response_time = min(r["response_time"] for r in results)
        
        print(f"总耗时: {total_time:.3f}s")
        print(f"成功请求: {successful_requests}/{concurrent_users}")
        print(f"平均响应时间: {avg_response_time:.3f}s")
        print(f"最大响应时间: {max_response_time:.3f}s")
        print(f"最小响应时间: {min_response_time:.3f}s")
        print(f"吞吐量: {concurrent_users/total_time:.1f} 请求/秒")
        
        return results
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("=" * 60)
        print("🎯 质量大盘第四阶段实施验证")
        print("=" * 60)
        
        all_results = {}
        
        # 测试性能端点
        all_results["performance"] = await self.test_performance_endpoints()
        
        # 测试集成端点
        all_results["integration"] = await self.test_integration_endpoints()
        
        # 测试缓存性能
        all_results["cache"] = await self.test_cache_performance()
        
        # 测试并发性能
        all_results["concurrent"] = await self.test_concurrent_requests()
        
        # 生成测试报告
        await self.generate_test_report(all_results)
        
        return all_results
    
    async def generate_test_report(self, results):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        # 性能指标汇总
        all_requests = []
        for category, category_results in results.items():
            if isinstance(category_results, list):
                all_requests.extend(category_results)
        
        if all_requests:
            successful_requests = [r for r in all_requests if r.get("success", False)]
            success_rate = (len(successful_requests) / len(all_requests)) * 100
            
            if successful_requests:
                avg_response_time = sum(r["response_time"] for r in successful_requests) / len(successful_requests)
                max_response_time = max(r["response_time"] for r in successful_requests)
                
                print(f"总请求数: {len(all_requests)}")
                print(f"成功率: {success_rate:.1f}%")
                print(f"平均响应时间: {avg_response_time:.3f}s")
                print(f"最大响应时间: {max_response_time:.3f}s")
                
                # 性能等级评估
                if avg_response_time < 0.5 and success_rate >= 99:
                    grade = "A (优秀)"
                elif avg_response_time < 1.0 and success_rate >= 95:
                    grade = "B (良好)"
                elif avg_response_time < 2.0 and success_rate >= 90:
                    grade = "C (一般)"
                else:
                    grade = "D (需要优化)"
                
                print(f"性能等级: {grade}")
        
        # 功能验证结果
        print("\n功能验证结果:")
        
        # 检查性能优化功能
        performance_endpoints = results.get("performance", [])
        performance_success = all(r.get("success", False) for r in performance_endpoints)
        print(f"✅ 性能优化功能: {'正常' if performance_success else '异常'}")
        
        # 检查集成功能
        integration_endpoints = results.get("integration", [])
        integration_success = all(r.get("success", False) for r in integration_endpoints)
        print(f"✅ 第三方集成功能: {'正常' if integration_success else '异常'}")
        
        # 检查缓存功能
        cache_results = results.get("cache", [])
        cache_success = len(cache_results) >= 2 and all(r.get("success", False) for r in cache_results)
        print(f"✅ 缓存功能: {'正常' if cache_success else '异常'}")
        
        # 检查并发处理
        concurrent_results = results.get("concurrent", [])
        concurrent_success = len([r for r in concurrent_results if r.get("success", False)]) >= 8  # 至少80%成功
        print(f"✅ 并发处理: {'正常' if concurrent_success else '异常'}")
        
        print("\n" + "=" * 60)
        print("🎉 第四阶段实施验证完成!")
        print("=" * 60)


async def main():
    """主函数"""
    async with QualityDashboardTester() as tester:
        await tester.run_comprehensive_test()


if __name__ == "__main__":
    print("启动质量大盘第四阶段验证测试...")
    print("请确保后端服务已启动 (python backend/main.py)")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试执行失败: {e}")
