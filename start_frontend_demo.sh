#!/bin/bash

# 前端覆盖率模块演示启动脚本
# 快速启动前端服务并打开覆盖率页面

echo "🚀 启动前端覆盖率模块演示"
echo "================================"

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装Node.js"
    exit 1
fi

# 检查npm环境
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装npm"
    exit 1
fi

echo "✅ 环境检查通过"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FRONTEND_DIR="$SCRIPT_DIR/frontend"

# 检查前端目录
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "❌ 前端目录不存在: $FRONTEND_DIR"
    exit 1
fi

echo "✅ 项目结构检查通过"

# 进入前端目录
cd "$FRONTEND_DIR"

# 检查package.json
if [ ! -f "package.json" ]; then
    echo "❌ package.json 不存在"
    exit 1
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
    
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已存在"
fi

# 检查前端服务是否已经运行
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo "✅ 前端服务已在运行"
    FRONTEND_RUNNING=true
else
    echo "🚀 启动前端服务..."
    FRONTEND_RUNNING=false
    
    # 启动前端服务
    npm run dev &
    FRONTEND_PID=$!
    echo "前端服务PID: $FRONTEND_PID"
    
    # 等待前端服务启动
    echo "⏳ 等待前端服务启动..."
    for i in {1..30}; do
        if curl -s http://localhost:5173 > /dev/null 2>&1; then
            echo "✅ 前端服务启动成功"
            FRONTEND_RUNNING=true
            break
        fi
        sleep 2
        if [ $i -eq 30 ]; then
            echo "❌ 前端服务启动超时"
            if [ ! -z "$FRONTEND_PID" ]; then
                kill $FRONTEND_PID 2>/dev/null
            fi
            exit 1
        fi
    done
fi

if [ "$FRONTEND_RUNNING" = true ]; then
    echo ""
    echo "🎉 前端服务启动成功！"
    echo ""
    echo "📱 访问地址:"
    echo "   主页: http://localhost:5173"
    echo "   覆盖率管理: http://localhost:5173/coverage-management"
    echo ""
    echo "🔍 功能演示:"
    echo "   1. 统计卡片展示平均覆盖率等关键指标"
    echo "   2. 覆盖率趋势图表显示历史变化"
    echo "   3. 覆盖率分布图表显示等级分布"
    echo "   4. 文件覆盖率热力图展示文件级覆盖率"
    echo "   5. 用例覆盖率分析提供深度分析"
    echo "   6. 覆盖率记录列表支持筛选和排序"
    echo ""
    echo "💡 说明:"
    echo "   - 当前使用模拟数据进行演示"
    echo "   - 所有图表和交互功能都可正常使用"
    echo "   - 数据会在API不可用时自动切换到模拟数据"
    echo ""
    echo "🛑 停止服务:"
    echo "   按 Ctrl+C 停止前端服务"
    
    # 如果是脚本启动的服务，等待用户中断
    if [ ! -z "$FRONTEND_PID" ]; then
        echo ""
        echo "⏳ 前端服务正在运行，按 Ctrl+C 停止..."
        
        # 捕获中断信号
        trap 'echo ""; echo "🛑 正在停止前端服务..."; kill $FRONTEND_PID 2>/dev/null; echo "✅ 前端服务已停止"; exit 0' INT
        
        # 等待进程结束
        wait $FRONTEND_PID
    fi
else
    echo "❌ 前端服务启动失败"
    exit 1
fi
