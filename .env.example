# 应用配置
DEBUG=true
SECRET_KEY=your-secret-key-here
ENVIRONMENT=development
PORT=8000
WORKERS=1

# 数据库配置
DATABASE_URL=*********************************************************/quality_dashboard

# Redis配置
REDIS_URL=redis://:redis_f4tEn5@*************:38762/3
REDIS_PASSWORD=redis_f4tEn5
REDIS_DB=3

# 第三方集成配置（可选）
JIRA_URL=
JIRA_USERNAME=
JIRA_TOKEN=

SONARQUBE_URL=
SONARQUBE_TOKEN=

JENKINS_URL=
JENKINS_USERNAME=
JENKINS_TOKEN=

# 邮件配置（可选）
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=

# 前端配置
FRONTEND_URL=http://localhost:3000

# 日志配置
LOG_LEVEL=INFO

# 性能监控配置
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_ALERT_THRESHOLD_CPU=80
PERFORMANCE_ALERT_THRESHOLD_MEMORY=85

# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_MAX_MEMORY=512mb

# 数据同步配置
SYNC_ENABLED=true
SYNC_INTERVAL_JIRA=900
SYNC_INTERVAL_SONARQUBE=3600
SYNC_INTERVAL_JENKINS=1800
