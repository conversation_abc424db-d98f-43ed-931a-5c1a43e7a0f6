<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能监控看板 - 接口自动化驱动的质量大盘</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="common.css">
    <style>
        .metric-card {
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .perf-card {
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
        }
        
        .perf-card:hover {
            transform: translateX(5px);
        }
        
        .perf-card.success {
            border-left-color: #10b981;
        }
        
        .perf-card.warning {
            border-left-color: #f59e0b;
        }
        
        .perf-card.danger {
            border-left-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-indigo-600 text-2xl"></i>
                    <span class="ml-2 text-xl font-bold text-gray-800">质量大盘</span>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="quality_index.html" class="nav-btn">首页</a>
                    <a href="dashboard.html" class="nav-btn">质量大盘</a>
                    <a href="automation.html" class="nav-btn">自动化测试</a>
                    <a href="performance.html" class="nav-btn active">性能监控</a>
                    <a href="quality_gate.html" class="nav-btn">质量门禁</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="quality_index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">首页</a>
                <a href="dashboard.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">质量大盘</a>
                <a href="automation.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">自动化测试</a>
                <a href="performance.html" class="block px-3 py-2 rounded-md text-base font-medium text-indigo-600 bg-indigo-50">性能监控</a>
                <a href="quality_gate.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">质量门禁</a>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <div class="bg-orange-700 text-white py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center">
                <i class="fas fa-tachometer-alt text-3xl mr-4"></i>
                <div>
                    <h1 class="text-3xl font-bold">性能监控看板</h1>
                    <p class="mt-1 text-orange-200">通过接口自动化实现性能监控和质量可视化</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 核心指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 接口平均响应时间 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">平均响应时间</h3>
                    <span class="text-2xl font-bold text-green-600">125ms</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill success" style="width: 75%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>基线: 150ms</span>
                    <span class="text-green-600">-15ms 较上月</span>
                </div>
            </div>
            
            <!-- 接口P95响应时间 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">P95响应时间</h3>
                    <span class="text-2xl font-bold text-orange-600">320ms</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill warning" style="width: 85%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>基线: 300ms</span>
                    <span class="text-orange-600">+20ms 较上月</span>
                </div>
            </div>
            
            <!-- 接口性能基线覆盖率 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">性能基线覆盖率</h3>
                    <span class="text-2xl font-bold text-indigo-600">85%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill primary" style="width: 85%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>目标: 95%</span>
                    <span class="text-indigo-600">+10% 较上月</span>
                </div>
            </div>
            
            <!-- 性能异常数 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">性能异常数</h3>
                    <span class="text-2xl font-bold text-green-600">12</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill success" style="width: 60%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>本月总计</span>
                    <span class="text-green-600">-8 较上月</span>
                </div>
            </div>
        </div>

        <!-- 响应时间趋势 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-chart-line text-orange-500 mr-2"></i>响应时间趋势
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 平均响应时间趋势 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">平均响应时间趋势</h3>
                        <div class="chart-container">
                            <canvas id="avgResponseChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- P95响应时间趋势 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">P95响应时间趋势</h3>
                        <div class="chart-container">
                            <canvas id="p95ResponseChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 业务线性能基线 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-ruler-horizontal text-orange-500 mr-2"></i>业务线性能基线
                </h2>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- 用户中心 -->
                    <div class="perf-card success bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-user text-blue-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">用户中心</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-green-600 font-bold mr-2">95ms</span>
                                <span class="badge badge-success">优</span>
                            </div>
                        </div>
                        <div class="ml-8 grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">平均响应时间</span>
                                    <span class="text-sm font-medium text-green-600">95ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 65%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">P95响应时间</span>
                                    <span class="text-sm font-medium text-green-600">180ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 60%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">基线覆盖率</span>
                                    <span class="text-sm font-medium text-green-600">100%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订单系统 -->
                    <div class="perf-card warning bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-shopping-cart text-purple-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">订单系统</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-yellow-600 font-bold mr-2">145ms</span>
                                <span class="badge badge-warning">良</span>
                            </div>
                        </div>
                        <div class="ml-8 grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">平均响应时间</span>
                                    <span class="text-sm font-medium text-yellow-600">145ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill warning" style="width: 75%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">P95响应时间</span>
                                    <span class="text-sm font-medium text-yellow-600">320ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill warning" style="width: 85%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">基线覆盖率</span>
                                    <span class="text-sm font-medium text-green-600">95%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 95%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 支付系统 -->
                    <div class="perf-card success bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-credit-card text-green-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">支付系统</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-green-600 font-bold mr-2">110ms</span>
                                <span class="badge badge-success">优</span>
                            </div>
                        </div>
                        <div class="ml-8 grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">平均响应时间</span>
                                    <span class="text-sm font-medium text-green-600">110ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 70%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">P95响应时间</span>
                                    <span class="text-sm font-medium text-green-600">220ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 75%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">基线覆盖率</span>
                                    <span class="text-sm font-medium text-green-600">100%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill success" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商品管理 -->
                    <div class="perf-card danger bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-box text-red-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">商品管理</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-red-600 font-bold mr-2">210ms</span>
                                <span class="badge badge-danger">差</span>
                            </div>
                        </div>
                        <div class="ml-8 grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">平均响应时间</span>
                                    <span class="text-sm font-medium text-red-600">210ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill danger" style="width: 90%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">P95响应时间</span>
                                    <span class="text-sm font-medium text-red-600">450ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill danger" style="width: 95%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">基线覆盖率</span>
                                    <span class="text-sm font-medium text-yellow-600">80%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill warning" style="width: 80%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 营销系统 -->
                    <div class="perf-card warning bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-bullhorn text-yellow-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">营销系统</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-yellow-600 font-bold mr-2">165ms</span>
                                <span class="badge badge-warning">良</span>
                            </div>
                        </div>
                        <div class="ml-8 grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">平均响应时间</span>
                                    <span class="text-sm font-medium text-yellow-600">165ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill warning" style="width: 80%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">P95响应时间</span>
                                    <span class="text-sm font-medium text-yellow-600">350ms</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill warning" style="width: 85%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">基线覆盖率</span>
                                    <span class="text-sm font-medium text-yellow-600">75%</span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill warning" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能异常监控 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-exclamation-triangle text-orange-500 mr-2"></i>性能异常监控
                </h2>
            </div>
            <div class="p-6 overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>接口名称</th>
                            <th>所属业务线</th>
                            <th>异常类型</th>
                            <th>当前值</th>
                            <th>基线值</th>
                            <th>偏离率</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="font-medium">/api/products/search</td>
                            <td>商品管理</td>
                            <td>响应时间超标</td>
                            <td>450ms</td>
                            <td>300ms</td>
                            <td>+50%</td>
                            <td>
                                <span class="badge badge-danger">未解决</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">/api/products/detail</td>
                            <td>商品管理</td>
                            <td>响应时间超标</td>
                            <td>380ms</td>
                            <td>250ms</td>
                            <td>+52%</td>
                            <td>
                                <span class="badge badge-danger">未解决</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">/api/marketing/promotions</td>
                            <td>营销系统</td>
                            <td>响应时间超标</td>
                            <td>350ms</td>
                            <td>280ms</td>
                            <td>+25%</td>
                            <td>
                                <span class="badge badge-warning">处理中</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">/api/orders/list</td>
                            <td>订单系统</td>
                            <td>响应时间超标</td>
                            <td>320ms</td>
                            <td>250ms</td>
                            <td>+28%</td>
                            <td>
                                <span class="badge badge-warning">处理中</span>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">/api/orders/create</td>
                            <td>订单系统</td>
                            <td>响应时间波动</td>
                            <td>180ms</td>
                            <td>150ms</td>
                            <td>+20%</td>
                            <td>
                                <span class="badge badge-success">已解决</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 性能优化建议 -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>性能优化建议
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 商品管理系统优化建议 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">商品管理系统优化建议</h3>
                        <div class="space-y-4">
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-database text-red-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-red-700">数据库查询优化</h4>
                                        <p class="text-sm text-red-600 mt-1">商品搜索接口(/api/products/search)查询未使用索引，导致全表扫描，建议优化SQL并添加合适的索引。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-memory text-red-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-red-700">缓存策略优化</h4>
                                        <p class="text-sm text-red-600 mt-1">商品详情接口(/api/products/detail)未使用缓存，每次请求都查询数据库，建议添加Redis缓存热门商品信息。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-code text-red-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-red-700">代码逻辑优化</h4>
                                        <p class="text-sm text-red-600 mt-1">商品管理系统接口存在多次重复计算和不必要的循环，建议优化代码逻辑，减少计算复杂度。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 营销系统优化建议 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">营销系统优化建议</h3>
                        <div class="space-y-4">
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-network-wired text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-yellow-700">服务拆分</h4>
                                        <p class="text-sm text-yellow-600 mt-1">营销系统接口(/api/marketing/promotions)内部调用多个微服务，建议将热门促销信息预计算并缓存，减少实时计算。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-server text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-yellow-700">资源扩容</h4>
                                        <p class="text-sm text-yellow-600 mt-1">营销系统当前资源配置较低，建议增加服务实例数量，并优化负载均衡策略，提高并发处理能力。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-tasks text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-yellow-700">异步处理</h4>
                                        <p class="text-sm text-yellow-600 mt-1">营销数据统计可以改为异步处理，先返回基础数据，统计数据通过异步任务计算后更新，提高接口响应速度。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 全局优化建议 -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-700 mb-4">全局优化建议</h3>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-cogs text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-medium text-blue-700">性能监控全覆盖</h4>
                                <p class="text-sm text-blue-600 mt-1">当前性能基线覆盖率为85%，建议在四季度完成剩余接口的性能基线建立，特别是商品管理和营销系统的核心接口。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg mt-4">
                        <div class="flex items-start">
                            <i class="fas fa-eye text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-medium text-blue-700">生产环境监控加强</h4>
                                <p class="text-sm text-blue-600 mt-1">建议加强生产环境的性能监控，实现7x24小时实时监控，对性能异常及时预警，并建立快速响应机制。</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg mt-4">
                        <div class="flex items-start">
                            <i class="fas fa-graduation-cap text-blue-500 mt-1 mr-3"></i>
                            <div>
                                <h4 class="font-medium text-blue-700">性能测试能力提升</h4>
                                <p class="text-sm text-blue-600 mt-1">建议对测试团队进行性能测试专项培训，提升性能测试能力，并建立性能测试最佳实践指南，指导各团队进行性能优化。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">关于质量大盘</h3>
                    <p class="text-gray-400">通过接口自动化驱动的全年度质量提升方案，倒逼测试规范化，提升团队质量意识</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速导航</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="quality_index.html" class="hover:text-white">首页</a></li>
                        <li><a href="dashboard.html" class="hover:text-white">质量大盘</a></li>
                        <li><a href="automation.html" class="hover:text-white">自动化测试</a></li>
                        <li><a href="performance.html" class="hover:text-white">性能监控</a></li>
                        <li><a href="quality_gate.html" class="hover:text-white">质量门禁</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">相关资源</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">接口自动化规范</a></li>
                        <li><a href="#" class="hover:text-white">质量门禁标准</a></li>
                        <li><a href="#" class="hover:text-white">性能测试指南</a></li>
                        <li><a href="#" class="hover:text-white">自动化最佳实践</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2"></i>
                            <span>+86 123 4567 8901</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 测试部. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 平均响应时间趋势图表
            const avgResponseCtx = document.getElementById('avgResponseChart').getContext('2d');
            const avgResponseChart = new Chart(avgResponseCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月'],
                    datasets: [
                        {
                            label: '平均响应时间',
                            data: [180, 175, 165, 160, 155, 150, 140, 130, 125],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '基线值',
                            data: [180, 180, 175, 170, 165, 160, 155, 150, 150],
                            borderColor: '#6b7280',
                            borderDash: [5, 5],
                            backgroundColor: 'transparent',
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 100,
                            max: 200,
                            ticks: {
                                callback: function(value) {
                                    return value + 'ms';
                                }
                            }
                        }
                    }
                }
            });
            
            // P95响应时间趋势图表
            const p95ResponseCtx = document.getElementById('p95ResponseChart').getContext('2d');
            const p95ResponseChart = new Chart(p95ResponseCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月'],
                    datasets: [
                        {
                            label: 'P95响应时间',
                            data: [350, 345, 340, 335, 330, 325, 330, 325, 320],
                            borderColor: '#f97316',
                            backgroundColor: 'rgba(249, 115, 22, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '基线值',
                            data: [350, 340, 330, 320, 310, 300, 300, 300, 300],
                            borderColor: '#6b7280',
                            borderDash: [5, 5],
                            backgroundColor: 'transparent',
                            tension: 0.3,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 250,
                            max: 400,
                            ticks: {
                                callback: function(value) {
                                    return value + 'ms';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
