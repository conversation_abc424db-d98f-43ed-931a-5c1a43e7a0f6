/* 共享样式文件 */
:root {
  --primary-color: #4f46e5;
  --secondary-color: #f97316;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --dark-color: #1f2937;
  --light-color: #f9fafb;
  --gray-color: #6b7280;
}

/* 卡片悬停效果 */
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 导航按钮样式 */
.nav-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: rgba(79, 70, 229, 0.1);
}

.nav-btn.active {
  background-color: rgba(79, 70, 229, 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

/* 进度条样式 */
.progress-bar {
  height: 0.5rem;
  border-radius: 9999px;
  background-color: #e5e7eb;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 9999px;
  transition: width 1s ease-in-out;
}

.progress-bar-fill.primary {
  background-color: var(--primary-color);
}

.progress-bar-fill.success {
  background-color: var(--success-color);
}

.progress-bar-fill.warning {
  background-color: var(--warning-color);
}

.progress-bar-fill.danger {
  background-color: var(--danger-color);
}

/* 表格样式 */
.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background-color: #f3f4f6;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--dark-color);
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.data-table tr:hover {
  background-color: #f9fafb;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #4338ca;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #ea580c;
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
  
  .data-table th, .data-table td {
    padding: 0.5rem;
  }
}
