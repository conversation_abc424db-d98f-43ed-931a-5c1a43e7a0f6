<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;600;700;800&display=swap" rel="stylesheet">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkyWalking 服务监控分析报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #F9FAFB; /* 极浅灰色背景 */
            color: #1F2937; /* 深灰色主文字 */
            margin: 0;
            padding: 40px 20px; /* 页面边距 */
            line-height: 1.6;
        }

        .report-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-title {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 5px;
            background-image: linear-gradient(to right, #C084FC, #7E22CE);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        .sub-title {
            font-size: 14px;
            color: #6B7280; /* 中灰色 */
            margin-bottom: 30px;
        }

        .bento-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr); /* 6列网格，方便布局 */
            gap: 20px; /* 卡片间距 */
        }

        .bento-card {
            background-color: #FFFFFF; /* 白色背景 */
            border-radius: 20px; /* 圆角 */
            padding: 24px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            overflow: hidden; /* 防止内容溢出圆角 */
        }

        .bento-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0,0,0,0.07), 0 4px 6px -2px rgba(0,0,0,0.04);
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .card-icon {
            margin-right: 8px;
            font-size: 24px; /* 示例图标大小 */
            color: #7E22CE; /* 图标颜色 */
        }

        .text-gradient {
            background-image: linear-gradient(to right, #C084FC, #7E22CE);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            font-weight: 800;
        }

        .text-gray-small {
            font-size: 12px;
            color: #6B7280;
        }
        .text-gray-medium {
            font-size: 14px;
            color: #6B7280;
        }

        .pill-tag {
            display: inline-block;
            background-color: #EDE9FE; /* 浅紫色背景 */
            color: #5B21B6; /* 深紫色文字 */
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-right: 6px;
            margin-bottom: 6px;
        }

        .donut-chart-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#7E22CE 0% 64%, #E5E7EB 64% 100%); /* 示例甜甜圈 */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin-top: 10px;
        }
        .donut-chart-center-text {
            font-size: 28px;
            font-weight: 700;
        }

        .error-severe { color: #DC2626; font-weight: bold; }
        .error-medium { color: #F97316; font-weight: bold; }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        th, td {
            text-align: left;
            padding: 8px 4px;
            border-bottom: 1px solid #E5E7EB; /* 浅灰色分隔线 */
        }
        th {
            color: #374151;
            font-weight: 600;
        }
        td .pill-tag { margin-bottom: 0;}

        .list-item { margin-bottom: 8px; }
        .list-item strong { color: #374151; }

        .action-urgent {
            border-left: 4px solid #EF4444;
            padding-left: 12px;
            margin-bottom:16px;
        }
        .action-urgent .action-title, .action-improve .action-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
        }
        .footer-note {
            text-align: center;
            font-size: 12px;
            color: #9CA3AF;
            margin-top: 40px;
        }

        /* Grid spans */
        .span-col-4 { grid-column: span 4; }
        .span-col-3 { grid-column: span 3; }
        .span-col-2 { grid-column: span 2; }
        
        /* Responsive adjustments (basic example) */
        @media (max-width: 1024px) {
            .bento-grid {
                grid-template-columns: repeat(2, 1fr); /* 2列 for tablets */
            }
            .span-col-4, .span-col-3, .span-col-2 {
                grid-column: span 2; /* Full width for most cards on tablet */
            }
            .card-1-1 { grid-column: span 2; }
            .card-1-2 { grid-column: span 2; }
            .card-2-1 { grid-column: span 2; }
            .card-2-2 { grid-column: span 2; }
            .card-3-1 { grid-column: span 2; }
            .card-3-2 { grid-column: span 2; }
        }
        @media (max-width: 768px) {
            .bento-grid {
                grid-template-columns: 1fr; /* 1列 for mobile */
            }
            .span-col-4, .span-col-3, .span-col-2 {
                grid-column: span 1; /* Full width */
            }
            .main-title { font-size: 28px; }
            .card-title { font-size: 18px; }
        }

    </style>
</head>
<body>
    <div class="report-container">
        <h1 class="main-title">SkyWalking 服务监控分析报告</h1>
        <p class="sub-title">数据周期：2025年4月22日 - 4月28日</p>

        <div class="bento-grid">
            <!-- Row 1: 报告核心与监控概览 -->
            <div class="bento-card span-col-4 card-1-1">
                <h2 class="card-title"><span class="card-icon">[🎯]</span>报告概述与监控目标</h2>
                <p style="font-size: 15px; margin-bottom: 16px;">本报告旨在全面呈现服务的健康状况、性能瓶颈和潜在问题。通过系统化分析，识别高错误率及高频接口，提升整体服务质量与用户体验。</p>
                <p style="font-weight: 600; margin-bottom: 8px;">SkyWalking 价值:</p>
                <div>
                    <span class="pill-tag">全链路追踪</span>
                    <span class="pill-tag">性能指标监控</span>
                    <span class="pill-tag">错误检测与告警</span>
                    <span class="pill-tag">服务依赖分析</span>
                </div>
                <p class="text-gray-medium" style="margin-top: 12px; font-style: italic;">主动发现问题，而非被动等待用户反馈。</p>
            </div>

            <div class="bento-card span-col-2 card-1-2">
                <h2 class="card-title"><span class="card-icon">[🛡️]</span>整体监控覆盖情况</h2>
                <div style="display: flex; align-items: center; justify-content: space-around;">
                    <div>
                        <div style="font-size: 56px;" class="text-gradient">51</div>
                        <div class="text-gray-medium">未监控服务</div>
                        <div class="text-gray-small">总服务数: 142</div>
                    </div>
                    <div class="donut-chart-placeholder">
                        <span class="donut-chart-center-text text-gradient">36%</span>
                        <span class="text-gray-small">未覆盖</span>
                    </div>
                </div>
                <p class="text-gray-small" style="margin-top: 16px;">部分服务SkyWalking Agent配置或数据采集可能存在问题。</p>
            </div>

            <!-- Row 2: 关键问题 - 错误与未接入 -->
            <div class="bento-card span-col-3 card-2-1" style="max-height: 500px; overflow-y: auto;"> <!-- Added scroll for long content -->
                <h2 class="card-title"><span class="card-icon">[⚠️]</span>高错误率接口分析</h2>
                
                <h3 style="font-size: 16px; margin-top: 20px; margin-bottom: 10px; color: #7E22CE;">严重错误 (>90%)</h3>
                <table>
                    <thead>
                        <tr><th>接口路径 & 服务</th><th>错误率</th><th>总请求</th></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>/api/autoRecharge/callback</strong><br><span class="pill-tag">coupon-consumer</span><span class="pill-tag">salecard-channel-web</span></td>
                            <td><span class="error-severe">99.07%</span></td>
                            <td>47,024</td>
                        </tr>
                        <tr><td colspan="3" class="text-gray-small"><i>分析与影响: 该接口是xxx</i></td></tr>
                        <tr>
                            <td><strong>/service/commonGrabNumber/autoGrabNumberNew</strong><br><span class="pill-tag">number-basic-service</span></td>
                            <td><span class="error-severe">96.64%</span></td>
                            <td>3,477</td>
                        </tr>
                         <tr><td colspan="3" class="text-gray-small"><i>分析与影响: xxx</i></td></tr>
                        <tr>
                            <td><strong>/api/pageLabel/queryValid</strong><br><span class="pill-tag">number-basic-web</span></td>
                            <td><span class="error-severe">92.37%</span></td>
                            <td>3,134</td>
                        </tr>
                        <tr><td colspan="3" class="text-gray-small"><i>分析与影响: xxx</i></td></tr>
                    </tbody>
                </table>

                <h3 style="font-size: 16px; margin-top: 20px; margin-bottom: 10px; color: #7E22CE;">中度错误 (10-90%)</h3>
                 <table>
                    <thead>
                        <tr><th>接口路径 & 服务</th><th>错误率</th><th>总请求</th></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>/api/junboinner/jingdong/queryNumber</strong><br><span class="pill-tag">salecard-channel-web</span></td>
                            <td><span class="error-medium">70.95%</span></td>
                            <td>23,395</td>
                        </tr>
                        <tr><td colspan="3" class="text-gray-small"><i>分析: 4月25-28日明显上升，26日达95%。影响: xxx</i></td></tr>
                         <tr>
                            <td><strong>/service/flash/page/pageIdLocation</strong><br><span class="pill-tag">salecard-flash-service</span></td>
                            <td><span class="error-medium">14.08%</span></td>
                            <td>35,188</td>
                        </tr>
                         <tr><td colspan="3" class="text-gray-small"><i>分析与影响: xxx</i></td></tr>
                    </tbody>
                </table>
                
                <h3 style="font-size: 16px; margin-top: 20px; margin-bottom: 10px; color: #7E22CE;">值得关注</h3>
                 <table>
                    <thead>
                        <tr><th>接口路径 & 服务</th><th>错误率</th><th>日均请求</th><th>错误数</th></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>/basic/service/product/productRedisInfo</strong><br><span class="pill-tag">salecard-basic-service</span></td>
                            <td>0.25%</td>
                            <td><span style="font-weight:bold; color:#5B21B6;">8.8万</span></td>
                            <td>1,534</td>
                        </tr>
                        <tr><td colspan="4" class="text-gray-small"><i>分析: 4月25日异常增长。影响: xxx</i></td></tr>
                    </tbody>
                </table>
            </div>

            <div class="bento-card span-col-3 card-2-2">
                <h2 class="card-title"><span class="card-icon">[🔗]</span>未接入 SkyWalking 监控风险</h2>
                <p style="font-size: 15px; margin-bottom: 16px;">发现部分服务文件未能正常生成监控数据，可能未正确接入SkyWalking。</p>
                <p style="font-weight: 600; margin-bottom: 8px;">问题示例:</p>
                <ul style="font-size: 13px; color: #4B5563; padding-left: 20px; margin-bottom:16px; max-height: 100px; overflow-y: auto;">
                    <li><code>处理文件 trace_coupon-service__service_order_verify_info_add_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                    <li><code>处理文件 trace_coupon-service__service_supplier_zhonghengjia_order_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                    <li><code>处理文件 trace_coupon-service__service_giftOrder_info_updateOrderInfo_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                    <li><code>处理文件 trace_coupon-service__api_v2_spans_20250422-20250428.csv 时出错: No columns to parse from file</code></code></li>
                    <li><code></code>处理文件 trace_coupon-service__service_pid_product_config_findConfig_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                    <li><code></code>处理文件 trace_coupon-service__service_giftOrder_info_addOrderInfo_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                    <li><code></code>处理文件 trace_numberweb-vendor__vendor_manager_goodNumberOrder_appointment_tips_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                    <li><code></code>处理文件 trace_coupon-service__service_common_coupon_placeOrder_20250422-20250428.csv 时出错: No columns to parse from file</code></li>
                </ul>
                <p style="font-weight: 600; margin-bottom: 8px;">建议:</p>
                <ol style="font-size: 14px; padding-left: 20px;">
                    <li class="list-item">排查相关服务 SkyWalking Agent 配置。</li>
                    <li class="list-item">确保日志收集与处理链路完整。</li>
                    <li class="list-item"><strong>优先关注 <span class="text-gradient" style="font-weight: 600;">核心业务服务</span> 的接入。</strong></li>
                </ol>
            </div>

            <!-- Row 3: 高频接口与行动计划 -->
            <div class="bento-card span-col-4 card-3-1" style="max-height: 400px; overflow-y: auto;">
                <h2 class="card-title"><span class="card-icon">[📈]</span>高频接口概览与优化建议</h2>
                <p style="font-weight: 600; margin-bottom: 8px;">主要高频接口 (Top 5):</p>
                <table>
                    <thead>
                        <tr><th>接口路径 & 服务</th><th>日均请求</th><th>错误率</th><th>Max Resp (ms)</th></tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>/basic/service/product/productRedisInfo</strong><br><span class="pill-tag">salecard-basic-service</span></td>
                            <td><span style="font-weight:bold; color:#5B21B6;">88,722</span></td>
                            <td>0.25%</td>
                            <td>17,114</td>
                        </tr>
                        <tr>
                            <td><strong>/basic/service/tabChannel/selectByTabPid</strong><br><span class="pill-tag">salecard-basic-service</span></td>
                            <td><span style="font-weight:bold; color:#5B21B6;">72,726</span></td>
                            <td>0.0%</td>
                            <td>21,195</td>
                        </tr>
                        <tr>
                            <td><strong>/basic/service/channel/findById</strong><br><span class="pill-tag">salecard-basic-service</span></td>
                            <td><span style="font-weight:bold; color:#5B21B6;">67,788</span></td>
                            <td>0.0%</td>
                            <td>1,134</td>
                        </tr>
                         <!-- Add more high-frequency interfaces if needed -->
                    </tbody>
                </table>
                <p style="font-weight: 600; margin-top: 20px; margin-bottom: 8px;">优化策略建议:</p>
                <ul style="font-size: 14px; padding-left: 20px;">
                    <li class="list-item">针对 <code>/productRedisInfo</code>: 评估缓存策略，减少DB压力。</li>
                    <li class="list-item">针对 <code>/selectByTabPid</code>: 检查索引，优化查询效率。</li>
                    <li class="list-item">通用: 考虑对部分高频读接口增加更激进的缓存策略。</li>
                </ul>
            </div>

            <div class="bento-card span-col-2 card-3-2">
                <h2 class="card-title"><span class="card-icon">[✅]</span>行动计划与优先级</h2>
                <div class="action-urgent">
                    <p class="action-title">紧急修复 (1周内)</p>
                    <ul style="font-size: 14px; padding-left: 15px;">
                        <li>解决 <code>/api/autoRecharge/callback</code> 高错误率。</li>
                        <li>排查 <code>/api/junboinner/jingdong/queryNumber</code> 错误率上升原因。</li>
                    </ul>
                </div>
                <div class="action-improve">
                     <p class="action-title">持续改进 (1个月内)</p>
                    <ul style="font-size: 14px; padding-left: 15px;">
                        <li>确保关键服务正确接入SkyWalking。</li>
                        <li>完善监控覆盖与告警机制。</li>
                        <li>建立接口性能定期回顾机制。</li>
                    </ul>
                </div>
                <p class="text-gray-medium" style="margin-top: 20px; font-style: italic;">目标：显著提升系统稳定性与用户体验。</p>
            </div>
        </div>

        <p class="footer-note">
            * 本报告基于 SkyWalking 2025年4月22日至4月28日的监控数据分析。
        </p>
    </div>

<script>
    // Optional: Add hover effects via JS if more complex interactions are needed later,
    // but CSS :hover is generally preferred for simple visual changes.
    // document.querySelectorAll('.bento-card').forEach(card => {
    //   card.onmouseenter = () => { /* complex hover */ };
    //   card.onmouseleave = () => { /* complex hover */ };
    // });
</script>

</body>
</html>