<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量门禁看板 - 接口自动化驱动的质量大盘</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="common.css">
    <style>
        .metric-card {
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        
        .gate-card {
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
        }
        
        .gate-card:hover {
            transform: translateX(5px);
        }
        
        .gate-card.success {
            border-left-color: #10b981;
        }
        
        .gate-card.warning {
            border-left-color: #f59e0b;
        }
        
        .gate-card.danger {
            border-left-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-indigo-600 text-2xl"></i>
                    <span class="ml-2 text-xl font-bold text-gray-800">质量大盘</span>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="quality_index.html" class="nav-btn">首页</a>
                    <a href="dashboard.html" class="nav-btn">质量大盘</a>
                    <a href="automation.html" class="nav-btn">自动化测试</a>
                    <a href="performance.html" class="nav-btn">性能监控</a>
                    <a href="quality_gate.html" class="nav-btn active">质量门禁</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="quality_index.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">首页</a>
                <a href="dashboard.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">质量大盘</a>
                <a href="automation.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">自动化测试</a>
                <a href="performance.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">性能监控</a>
                <a href="quality_gate.html" class="block px-3 py-2 rounded-md text-base font-medium text-indigo-600 bg-indigo-50">质量门禁</a>
            </div>
        </div>
    </nav>

    <!-- 页面标题 -->
    <div class="bg-green-700 text-white py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center">
                <i class="fas fa-shield-alt text-3xl mr-4"></i>
                <div>
                    <h1 class="text-3xl font-bold">质量门禁看板</h1>
                    <p class="mt-1 text-green-200">通过接口自动化强化质量意识和准入标准</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 核心指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 质量门禁通过率 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">质量门禁通过率</h3>
                    <span class="text-2xl font-bold text-green-600">92%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill success" style="width: 92%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>目标: 95%</span>
                    <span class="text-green-600">+4% 较上月</span>
                </div>
            </div>
            
            <!-- 拦截问题数 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">拦截问题数</h3>
                    <span class="text-2xl font-bold text-orange-600">78</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill warning" style="width: 78%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>本月总计</span>
                    <span class="text-orange-600">-12 较上月</span>
                </div>
            </div>
            
            <!-- 自动化准入执行率 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">自动化准入执行率</h3>
                    <span class="text-2xl font-bold text-green-600">98%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill success" style="width: 98%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>目标: 95%</span>
                    <span class="text-green-600">+2% 较上月</span>
                </div>
            </div>
            
            <!-- 平均修复时间 -->
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-700">平均修复时间</h3>
                    <span class="text-2xl font-bold text-indigo-600">4.2小时</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-bar-fill primary" style="width: 70%"></div>
                </div>
                <div class="mt-4 flex justify-between text-sm text-gray-500">
                    <span>目标: 4小时</span>
                    <span class="text-indigo-600">-0.8小时 较上月</span>
                </div>
            </div>
        </div>

        <!-- 质量门禁详情 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-shield-alt text-green-500 mr-2"></i>质量门禁详情
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 门禁通过率图表 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">质量门禁通过率趋势</h3>
                        <div class="chart-container">
                            <canvas id="gatePassChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 拦截问题分布 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">拦截问题类型分布</h3>
                        <div class="chart-container">
                            <canvas id="issueTypeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自动化准入标准执行情况 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-clipboard-check text-green-500 mr-2"></i>自动化准入标准执行情况
                </h2>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- 新接口自动化覆盖 -->
                    <div class="gate-card success bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">新接口自动化覆盖</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-green-600 font-bold mr-2">98%</span>
                                <span class="badge badge-success">S</span>
                            </div>
                        </div>
                        <p class="text-gray-600 ml-8">新增接口必须有对应自动化测试用例，提测前自动化用例必须通过</p>
                        <div class="mt-3 ml-8">
                            <div class="progress-bar">
                                <div class="progress-bar-fill success" style="width: 98%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- CI/CD集成自动化测试 -->
                    <div class="gate-card success bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">CI/CD集成自动化测试</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-green-600 font-bold mr-2">100%</span>
                                <span class="badge badge-success">S</span>
                            </div>
                        </div>
                        <p class="text-gray-600 ml-8">在CI/CD流程中集成自动化测试，自动化测试失败自动阻断合并</p>
                        <div class="mt-3 ml-8">
                            <div class="progress-bar">
                                <div class="progress-bar-fill success" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 主链路接口覆盖率 -->
                    <div class="gate-card warning bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle text-yellow-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">主链路接口覆盖率</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-yellow-600 font-bold mr-2">92%</span>
                                <span class="badge badge-warning">A</span>
                            </div>
                        </div>
                        <p class="text-gray-600 ml-8">主链路接口自动化覆盖率目标为95%，未覆盖主链路接口列入高风险监控</p>
                        <div class="mt-3 ml-8">
                            <div class="progress-bar">
                                <div class="progress-bar-fill warning" style="width: 92%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 自动化测试通过率 -->
                    <div class="gate-card success bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">自动化测试通过率</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-green-600 font-bold mr-2">96%</span>
                                <span class="badge badge-success">S</span>
                            </div>
                        </div>
                        <p class="text-gray-600 ml-8">自动化测试通过率目标为95%，低于90%的接口需优先修复</p>
                        <div class="mt-3 ml-8">
                            <div class="progress-bar">
                                <div class="progress-bar-fill success" style="width: 96%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 接口文档标准化率 -->
                    <div class="gate-card danger bg-white rounded-lg p-4 shadow-sm">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-times-circle text-red-500 text-xl mr-3"></i>
                                <h3 class="text-lg font-medium text-gray-800">接口文档标准化率</h3>
                            </div>
                            <div class="flex items-center">
                                <span class="text-red-600 font-bold mr-2">85%</span>
                                <span class="badge badge-danger">C</span>
                            </div>
                        </div>
                        <p class="text-gray-600 ml-8">接口文档必须符合标准模板，只有符合标准的接口文档才能进入自动化</p>
                        <div class="mt-3 ml-8">
                            <div class="progress-bar">
                                <div class="progress-bar-fill danger" style="width: 85%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 质量门禁拦截数据 -->
        <div class="bg-white rounded-lg shadow mb-8">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-ban text-red-500 mr-2"></i>质量门禁拦截数据
                </h2>
            </div>
            <div class="p-6 overflow-x-auto">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>业务线</th>
                            <th>拦截次数</th>
                            <th>主要问题类型</th>
                            <th>平均修复时间</th>
                            <th>修复率</th>
                            <th>趋势</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="font-medium">用户中心</td>
                            <td>12</td>
                            <td>接口返回异常</td>
                            <td>2.5小时</td>
                            <td>
                                <div class="flex items-center">
                                    <span class="mr-2">100%</span>
                                    <span class="badge badge-success">S</span>
                                </div>
                            </td>
                            <td class="text-green-600">
                                <i class="fas fa-arrow-down"></i> 下降
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">订单系统</td>
                            <td>18</td>
                            <td>数据校验失败</td>
                            <td>3.8小时</td>
                            <td>
                                <div class="flex items-center">
                                    <span class="mr-2">95%</span>
                                    <span class="badge badge-success">A</span>
                                </div>
                            </td>
                            <td class="text-green-600">
                                <i class="fas fa-arrow-down"></i> 下降
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">支付系统</td>
                            <td>8</td>
                            <td>接口超时</td>
                            <td>4.2小时</td>
                            <td>
                                <div class="flex items-center">
                                    <span class="mr-2">100%</span>
                                    <span class="badge badge-success">S</span>
                                </div>
                            </td>
                            <td class="text-green-600">
                                <i class="fas fa-arrow-down"></i> 下降
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">商品管理</td>
                            <td>22</td>
                            <td>参数校验失败</td>
                            <td>5.1小时</td>
                            <td>
                                <div class="flex items-center">
                                    <span class="mr-2">90%</span>
                                    <span class="badge badge-warning">B</span>
                                </div>
                            </td>
                            <td class="text-red-600">
                                <i class="fas fa-arrow-up"></i> 上升
                            </td>
                        </tr>
                        <tr>
                            <td class="font-medium">营销系统</td>
                            <td>15</td>
                            <td>业务逻辑错误</td>
                            <td>4.8小时</td>
                            <td>
                                <div class="flex items-center">
                                    <span class="mr-2">93%</span>
                                    <span class="badge badge-warning">B</span>
                                </div>
                            </td>
                            <td class="text-yellow-600">
                                <i class="fas fa-equals"></i> 持平
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 问题分析与改进建议 -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>问题分析与改进建议
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- 主要问题分析 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">主要问题分析</h3>
                        <div class="space-y-4">
                            <div class="bg-red-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-exclamation-circle text-red-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-red-700">接口文档标准化率低</h4>
                                        <p class="text-sm text-red-600 mt-1">接口文档标准化率仅为85%，低于目标值95%，是当前最主要的问题。主要集中在商品管理和营销系统。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-yellow-700">商品管理系统拦截次数高</h4>
                                        <p class="text-sm text-yellow-600 mt-1">商品管理系统拦截次数为22次，远高于其他业务线，且呈上升趋势，主要问题为参数校验失败。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-yellow-700">主链路接口覆盖率未达标</h4>
                                        <p class="text-sm text-yellow-600 mt-1">主链路接口覆盖率为92%，未达到目标值95%，主要是营销系统和数据分析系统的主链路覆盖不足。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 改进建议 -->
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 mb-4">改进建议</h3>
                        <div class="space-y-4">
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-green-700">加强接口文档规范培训</h4>
                                        <p class="text-sm text-green-600 mt-1">针对商品管理和营销系统团队，开展接口文档规范专项培训，并提供文档模板和示例。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-green-700">商品管理系统参数校验优化</h4>
                                        <p class="text-sm text-green-600 mt-1">对商品管理系统进行参数校验逻辑优化，增加前置校验，减少后端校验失败的情况。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-green-700">主链路覆盖专项行动</h4>
                                        <p class="text-sm text-green-600 mt-1">针对营销系统和数据分析系统，启动主链路覆盖专项行动，两周内完成剩余主链路接口的自动化覆盖。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-green-700">建立问题快速响应机制</h4>
                                        <p class="text-sm text-green-600 mt-1">建立质量门禁拦截问题的快速响应机制，缩短平均修复时间，特别是商品管理和营销系统。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">关于质量大盘</h3>
                    <p class="text-gray-400">通过接口自动化驱动的全年度质量提升方案，倒逼测试规范化，提升团队质量意识</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速导航</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="quality_index.html" class="hover:text-white">首页</a></li>
                        <li><a href="dashboard.html" class="hover:text-white">质量大盘</a></li>
                        <li><a href="automation.html" class="hover:text-white">自动化测试</a></li>
                        <li><a href="performance.html" class="hover:text-white">性能监控</a></li>
                        <li><a href="quality_gate.html" class="hover:text-white">质量门禁</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">相关资源</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">接口自动化规范</a></li>
                        <li><a href="#" class="hover:text-white">质量门禁标准</a></li>
                        <li><a href="#" class="hover:text-white">性能测试指南</a></li>
                        <li><a href="#" class="hover:text-white">自动化最佳实践</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2"></i>
                            <span>+86 123 4567 8901</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 测试部. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 门禁通过率趋势图表
            const gatePassCtx = document.getElementById('gatePassChart').getContext('2d');
            const gatePassChart = new Chart(gatePassCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月'],
                    datasets: [
                        {
                            label: '质量门禁通过率',
                            data: [75, 78, 82, 85, 87, 88, 90, 91, 92],
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '自动化准入执行率',
                            data: [80, 85, 88, 90, 92, 94, 95, 97, 98],
                            borderColor: '#4f46e5',
                            backgroundColor: 'rgba(79, 70, 229, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
            
            // 拦截问题类型分布图表
            const issueTypeCtx = document.getElementById('issueTypeChart').getContext('2d');
            const issueTypeChart = new Chart(issueTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['参数校验失败', '接口返回异常', '业务逻辑错误', '接口超时', '数据一致性问题'],
                    datasets: [
                        {
                            data: [35, 25, 20, 12, 8],
                            backgroundColor: [
                                '#4f46e5',
                                '#10b981',
                                '#f97316',
                                '#ef4444',
                                '#8b5cf6'
                            ],
                            borderWidth: 0
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}% (${value}次)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        });
    </script>
</body>
</html>
