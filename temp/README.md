# 质量大盘 - 启动和测试指南

## 项目概述

这是一个基于 FastAPI (Python) 后端和 Vue.js 3 前端的质量大盘应用，用于展示接口自动化驱动的质量指标和数据分析。

**最新更新**: 后端已迁移到 `uv` 包管理器，提供更快的依赖管理和更好的开发体验。

## 项目结构

```
quality_dashboard/
├── backend/                 # FastAPI 后端 (使用 uv 管理)
│   ├── main.py             # 主应用文件
│   ├── pyproject.toml      # 项目配置和依赖 (替代 requirements.txt)
│   ├── .python-version     # Python 版本配置
│   ├── start.py            # uv 启动脚本
│   └── api/                # API 路由模块
│       ├── automation.py   # 自动化测试 API
│       ├── performance.py  # 性能监控 API
│       └── quality_gate.py # 质量门禁 API
├── frontend/               # Vue.js 前端
│   ├── package.json        # Node.js 依赖
│   ├── vite.config.js      # Vite 配置
│   ├── tailwind.config.js  # Tailwind CSS 配置
│   ├── index.html          # 入口 HTML
│   └── src/                # 源代码
│       ├── main.js         # 应用入口
│       ├── App.vue         # 根组件
│       ├── router/         # 路由配置
│       ├── stores/         # Pinia 状态管理
│       ├── services/       # API 服务
│       ├── components/     # Vue 组件
│       └── views/          # 页面组件
├── start-backend.bat       # 后端启动脚本 (Windows)
├── start-frontend.bat      # 前端启动脚本 (Windows)
├── start-all.bat          # 完整应用启动脚本 (Windows)
└── temp/                   # 原始 HTML 模板文件
    └── README.md           # 本文件
```

## 环境要求

### 后端要求
- Python 3.8+ (推荐 3.11+)
- uv 包管理器 (自动安装)

### 前端要求
- Node.js 16+ (推荐 18+)
- npm 8+ 或 yarn 1.22+

## 快速启动 (推荐方式)

### 方式一：使用启动脚本 (Windows)

**启动完整应用 (推荐)**
```bash
# 双击运行或在命令行执行
start-all.bat
```

**分别启动服务**
```bash
# 启动后端 (使用 uv)
start-backend.bat

# 启动前端 (在新窗口)
start-frontend.bat
```

### 方式二：手动启动

**1. 启动后端服务 (使用 uv)**

```bash
# 进入后端目录
cd backend

# 安装 uv (如果未安装)
# Windows PowerShell:
irm https://astral.sh/uv/install.ps1 | iex

# 同步项目依赖
uv sync

# 安装开发依赖
uv sync --extra dev

# 启动后端服务
uv run uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

后端服务将在 `http://localhost:8000` 启动

**2. 启动前端服务**

打开新的终端窗口：

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

## 访问应用

1. 确保后端和前端服务都已启动
2. 在浏览器中访问 `http://localhost:3000`
3. 您将看到质量大盘的首页

## API 文档

后端启动后，可以访问以下地址查看 API 文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 主要功能页面

1. **首页** (`/`) - 应用介绍和快速导航
2. **质量大盘** (`/dashboard`) - 核心质量指标展示
3. **自动化测试** (`/automation`) - 自动化测试数据
4. **性能监控** (`/performance`) - 系统性能指标
5. **质量门禁** (`/quality-gate`) - 质量门禁规则和结果

## 测试 API 接口

### 使用 curl 测试

```bash
# 测试根接口
curl http://localhost:8000/

# 测试质量大盘概览
curl http://localhost:8000/api/dashboard/overview

# 测试质量趋势数据
curl http://localhost:8000/api/dashboard/trends

# 测试团队对比数据
curl http://localhost:8000/api/dashboard/teams

# 测试自动化测试概览
curl http://localhost:8000/api/automation/overview

# 测试性能监控概览
curl http://localhost:8000/api/performance/overview

# 测试质量门禁概览
curl http://localhost:8000/api/quality-gate/overview
```

### 使用浏览器测试

直接在浏览器中访问以下 URL：

- `http://localhost:8000/api/dashboard/overview`
- `http://localhost:8000/api/dashboard/trends`
- `http://localhost:8000/api/automation/overview`
- `http://localhost:8000/api/performance/overview`
- `http://localhost:8000/api/quality-gate/overview`

## 开发模式

### 后端开发

后端使用 `uvicorn` 的热重载功能，修改 Python 代码后会自动重启服务。

### 前端开发

前端使用 Vite 的热模块替换 (HMR)，修改代码后会自动刷新浏览器。

## 构建生产版本

### 构建前端

```bash
cd temp/frontend
npm run build
```

构建产物将在 `dist/` 目录中。

### 部署后端

```bash
cd temp/backend
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 故障排除

### 常见问题

1. **端口冲突**
   - 后端默认端口：8000
   - 前端默认端口：3000
   - 如果端口被占用，请修改配置或停止占用端口的进程

2. **CORS 错误**
   - 确保后端 CORS 配置正确
   - 检查前端 API 请求地址是否正确

3. **依赖安装失败**
   - 检查 Python/Node.js 版本
   - 尝试清除缓存后重新安装

4. **API 请求失败**
   - 确保后端服务正在运行
   - 检查网络连接
   - 查看浏览器开发者工具的网络面板

### 日志查看

- 后端日志：在启动后端的终端窗口中查看
- 前端日志：在浏览器开发者工具的控制台中查看

## 技术栈

### 后端
- **FastAPI**: 现代、快速的 Python Web 框架
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI 服务器

### 前端
- **Vue.js 3**: 渐进式 JavaScript 框架
- **Vite**: 快速的前端构建工具
- **Vue Router**: 官方路由管理器
- **Pinia**: 状态管理库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Chart.js**: 图表库
- **Axios**: HTTP 客户端

## 下一步开发

1. 完善其他页面的功能实现
2. 添加用户认证和权限管理
3. 集成真实的数据源
4. 添加更多图表类型和数据可视化
5. 实现实时数据更新
6. 添加单元测试和集成测试
7. 优化性能和用户体验

## 联系方式

如有问题，请联系开发团队或查看项目文档。
