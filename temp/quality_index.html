<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接口自动化驱动的质量大盘</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome 图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="common.css">
    <style>
        .hero-section {
            background-image: linear-gradient(rgba(31, 41, 55, 0.8), rgba(31, 41, 55, 0.8)), url('https://source.unsplash.com/random/1920x1080/?technology');
            background-size: cover;
            background-position: center;
            height: 60vh;
        }
        
        .feature-card {
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .feature-icon {
            transition: all 0.3s ease;
        }
        
        .feature-card:hover .feature-icon {
            transform: scale(1.2);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-indigo-600 text-2xl"></i>
                    <span class="ml-2 text-xl font-bold text-gray-800">质量大盘</span>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="quality_index.html" class="nav-btn active">首页</a>
                    <a href="dashboard.html" class="nav-btn">质量大盘</a>
                    <a href="automation.html" class="nav-btn">自动化测试</a>
                    <a href="performance.html" class="nav-btn">性能监控</a>
                    <a href="quality_gate.html" class="nav-btn">质量门禁</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-md">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="quality_index.html" class="block px-3 py-2 rounded-md text-base font-medium text-indigo-600 bg-indigo-50">首页</a>
                <a href="dashboard.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">质量大盘</a>
                <a href="automation.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">自动化测试</a>
                <a href="performance.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">性能监控</a>
                <a href="quality_gate.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-indigo-50">质量门禁</a>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="hero-section flex items-center justify-center text-center text-white animate-fade-in">
        <div class="max-w-4xl px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">接口自动化驱动的全年度质量提升方案</h1>
            <p class="text-xl md:text-2xl mb-8">通过接口自动化倒逼测试规范化，提升团队质量意识，实现质量可视化</p>
            <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                <a href="dashboard.html" class="btn btn-primary">
                    <i class="fas fa-chart-pie mr-2"></i>查看质量大盘
                </a>
                <a href="automation.html" class="btn btn-secondary">
                    <i class="fas fa-robot mr-2"></i>自动化测试看板
                </a>
            </div>
        </div>
    </section>

    <!-- 季度计划概览 -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">季度计划概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- 一季度 -->
                <div class="feature-card bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 shadow-md">
                    <div class="flex justify-center mb-4">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-clipboard-list text-blue-600 text-2xl feature-icon"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-4">一季度：强制规范</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-blue-500 mt-1 mr-2"></i>
                            <span>用例管理工具统一</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-blue-500 mt-1 mr-2"></i>
                            <span>接口文档标准化</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-blue-500 mt-1 mr-2"></i>
                            <span>基础接口自动化覆盖(30%)</span>
                        </li>
                    </ul>
                </div>
                
                <!-- 二季度 -->
                <div class="feature-card bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-6 shadow-md">
                    <div class="flex justify-center mb-4">
                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-shield-alt text-purple-600 text-2xl feature-icon"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-4">二季度：质量门禁</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-purple-500 mt-1 mr-2"></i>
                            <span>自动化准入机制(50%)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-purple-500 mt-1 mr-2"></i>
                            <span>CI/CD质量门禁集成</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-purple-500 mt-1 mr-2"></i>
                            <span>覆盖率提升至70%</span>
                        </li>
                    </ul>
                </div>
                
                <!-- 三季度 -->
                <div class="feature-card bg-gradient-to-br from-green-50 to-teal-50 rounded-xl p-6 shadow-md">
                    <div class="flex justify-center mb-4">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-green-600 text-2xl feature-icon"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-4">三季度：全面覆盖</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>主链路全覆盖(90%)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>自动化效能评估体系</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>自助测试平台推广</span>
                        </li>
                    </ul>
                </div>
                
                <!-- 四季度 -->
                <div class="feature-card bg-gradient-to-br from-orange-50 to-red-50 rounded-xl p-6 shadow-md">
                    <div class="flex justify-center mb-4">
                        <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-tachometer-alt text-orange-600 text-2xl feature-icon"></i>
                        </div>
                    </div>
                    <h3 class="text-xl font-bold text-center mb-4">四季度：性能监控</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-orange-500 mt-1 mr-2"></i>
                            <span>性能自动化基线建立</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-orange-500 mt-1 mr-2"></i>
                            <span>生产环境监控全覆盖</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-orange-500 mt-1 mr-2"></i>
                            <span>质量大盘全面上线</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 看板导航 -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-center mb-12">质量大盘看板</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- 质量大盘 -->
                <a href="dashboard.html" class="card-hover block bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="h-48 bg-indigo-600 flex items-center justify-center">
                        <i class="fas fa-chart-pie text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">质量大盘</h3>
                        <p class="text-gray-600">全面展示质量指标，包括覆盖率、通过率、效能分析等多维度数据</p>
                    </div>
                </a>
                
                <!-- 自动化测试 -->
                <a href="automation.html" class="card-hover block bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="h-48 bg-purple-600 flex items-center justify-center">
                        <i class="fas fa-robot text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">自动化测试</h3>
                        <p class="text-gray-600">接口自动化覆盖率、稳定性、效能分析及团队对比数据</p>
                    </div>
                </a>
                
                <!-- 性能监控 -->
                <a href="performance.html" class="card-hover block bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="h-48 bg-orange-600 flex items-center justify-center">
                        <i class="fas fa-tachometer-alt text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">性能监控</h3>
                        <p class="text-gray-600">接口性能基线、响应时间趋势、异常监控及性能优化建议</p>
                    </div>
                </a>
                
                <!-- 质量门禁 -->
                <a href="quality_gate.html" class="card-hover block bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="h-48 bg-green-600 flex items-center justify-center">
                        <i class="fas fa-shield-alt text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">质量门禁</h3>
                        <p class="text-gray-600">自动化准入标准执行情况、质量门禁拦截数据及问题分析</p>
                    </div>
                </a>
                
                <!-- 团队对比 -->
                <a href="dashboard.html#team-comparison" class="card-hover block bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="h-48 bg-blue-600 flex items-center justify-center">
                        <i class="fas fa-users text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">团队对比</h3>
                        <p class="text-gray-600">各团队自动化覆盖率、质量评分及效能对比分析</p>
                    </div>
                </a>
                
                <!-- 趋势分析 -->
                <a href="dashboard.html#trend-analysis" class="card-hover block bg-white rounded-xl overflow-hidden shadow-md">
                    <div class="h-48 bg-red-600 flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-6xl"></i>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">趋势分析</h3>
                        <p class="text-gray-600">覆盖率、质量、效能等指标的历史趋势及预测分析</p>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">关于质量大盘</h3>
                    <p class="text-gray-400">通过接口自动化驱动的全年度质量提升方案，倒逼测试规范化，提升团队质量意识</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速导航</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="quality_index.html" class="hover:text-white">首页</a></li>
                        <li><a href="dashboard.html" class="hover:text-white">质量大盘</a></li>
                        <li><a href="automation.html" class="hover:text-white">自动化测试</a></li>
                        <li><a href="performance.html" class="hover:text-white">性能监控</a></li>
                        <li><a href="quality_gate.html" class="hover:text-white">质量门禁</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">相关资源</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">接口自动化规范</a></li>
                        <li><a href="#" class="hover:text-white">质量门禁标准</a></li>
                        <li><a href="#" class="hover:text-white">性能测试指南</a></li>
                        <li><a href="#" class="hover:text-white">自动化最佳实践</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">联系我们</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2"></i>
                            <span>+86 123 4567 8901</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 测试部. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                section.style.transitionDelay = `${quality_index * 0.1}s`;
                
                setTimeout(() => {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 100);
            });
        });
    </script>
</body>
</html>
