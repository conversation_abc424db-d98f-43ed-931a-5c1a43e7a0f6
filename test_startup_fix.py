#!/usr/bin/env python3
"""
启动修复验证脚本
"""

import requests
import time
import sys

def test_endpoint(url, description):
    """测试API端点"""
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {description}: 正常 (状态码: {response.status_code})")
            return True
        else:
            print(f"❌ {description}: 异常 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {description}: 连接失败 - {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 质量大盘启动修复验证")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 测试端点列表
    endpoints = [
        ("/health", "健康检查"),
        ("/health/live", "存活检查"),
        ("/health/ready", "就绪检查"),
        ("/api/integrations/status", "集成状态"),
        ("/api/sync/tasks", "同步任务"),
        ("/api/sync/history", "同步历史"),
        ("/api/performance/metrics", "性能监控"),
    ]
    
    # 执行测试
    results = []
    for endpoint, description in endpoints:
        url = f"{base_url}{endpoint}"
        result = test_endpoint(url, description)
        results.append(result)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！启动修复成功！")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查服务状态")
        return 1

if __name__ == "__main__":
    sys.exit(main())
