{"name": "quality-dashboard-frontend", "version": "1.0.0", "description": "质量大盘前端应用 - Vue.js 3", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "axios": "^1.6.2", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5", "vue3-grid-layout": "^1.0.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "postcss": "^8.4.32", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "vite": "^5.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}