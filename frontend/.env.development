# 开发环境配置
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=质量大盘系统
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEBUG=true
VITE_ENABLE_ANALYTICS=false

# 搜索配置
VITE_SEARCH_DEBOUNCE_TIME=300
VITE_SEARCH_MAX_SUGGESTIONS=8
VITE_SEARCH_MAX_RESULTS=100

# 图表配置
VITE_CHART_ANIMATION_DURATION=750
VITE_CHART_REFRESH_INTERVAL=30000

# 性能配置
VITE_REQUEST_TIMEOUT=10000
VITE_MAX_CONCURRENT_REQUESTS=6
