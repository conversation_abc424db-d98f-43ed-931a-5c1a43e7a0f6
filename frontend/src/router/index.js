import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
const Home = () => import('@/views/Home.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const Automation = () => import('@/views/Automation.vue')
const Performance = () => import('@/views/Performance.vue')
const QualityGate = () => import('@/views/QualityGate.vue')
const DefectManagement = () => import('@/views/DefectManagement.vue')
const CoverageManagement = () => import('@/views/CoverageManagement.vue')
const AlertManagement = () => import('@/views/AlertManagement.vue')
const ReportManagement = () => import('@/views/ReportManagement.vue')
const SearchResults = () => import('@/views/SearchResults.vue')
const APITestPage = () => import('@/views/APITestPage.vue')
const ChartTest = () => import('@/views/ChartTest.vue')

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      description: '质量大盘首页',
    },
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '质量大盘',
      description: '全面展示质量指标，包括覆盖率、通过率、效能分析等多维度数据',
    },
  },
  {
    path: '/automation',
    name: 'Automation',
    component: Automation,
    meta: {
      title: '自动化测试',
      description: '自动化测试覆盖率、执行结果、趋势分析等数据展示',
    },
  },
  {
    path: '/performance',
    name: 'Performance',
    component: Performance,
    meta: {
      title: '性能监控',
      description: '系统性能指标、响应时间、吞吐量等监控数据',
    },
  },
  {
    path: '/quality-gate',
    name: 'QualityGate',
    component: QualityGate,
    meta: {
      title: '质量门禁',
      description: '质量门禁规则、执行结果、通过率等数据展示',
    },
  },
  {
    path: '/defect-management',
    name: 'DefectManagement',
    component: DefectManagement,
    meta: {
      title: '缺陷管理',
      description: '缺陷跟踪、趋势分析和质量监控',
    },
  },
  {
    path: '/coverage-management',
    name: 'CoverageManagement',
    component: CoverageManagement,
    meta: {
      title: '测试覆盖率',
      description: '测试覆盖率统计、趋势分析和热力图展示',
    },
  },
  {
    path: '/alert-management',
    name: 'AlertManagement',
    component: AlertManagement,
    meta: {
      title: '质量预警',
      description: '质量预警系统，实时监控质量指标，智能预警异常情况',
    },
  },
  {
    path: '/report-management',
    name: 'ReportManagement',
    component: ReportManagement,
    meta: {
      title: '数据导出与报告',
      description: '生成质量报告，支持Excel/CSV导出，定时推送和自定义模板',
    },
  },
  {
    path: '/integration-management',
    name: 'IntegrationManagement',
    component: () => import('@/views/IntegrationManagement.vue'),
    meta: {
      title: '集成管理',
      description: '第三方工具集成管理，包括JIRA、SonarQube、Jenkins等系统的连接和数据同步',
    },
  },
  {
    path: '/performance-monitoring',
    name: 'PerformanceMonitoring',
    component: () => import('@/views/PerformanceMonitoring.vue'),
    meta: {
      title: '性能监控',
      description: '实时监控系统性能指标、API响应时间和资源使用情况',
    },
  },
  {
    path: '/search',
    name: 'SearchResults',
    component: SearchResults,
    meta: {
      title: '搜索结果',
      description: '全局搜索结果页面',
    },
  },
  {
    path: '/api-test',
    name: 'APITestPage',
    component: APITestPage,
    meta: {
      title: 'API测试',
      description: '前后端API集成测试页面',
    },
  },
  {
    path: '/chart-test',
    name: 'ChartTest',
    component: ChartTest,
    meta: {
      title: 'Chart.js 测试',
      description: 'Chart.js 图表组件测试页面',
    },
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到',
    },
  },
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 质量大盘`
  } else {
    document.title = '质量大盘 - 接口自动化驱动的质量大盘'
  }
  
  next()
})

export default router
