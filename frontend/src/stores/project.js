import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'

export const useProjectStore = defineStore('project', () => {
  // 状态
  const projects = ref([])
  const currentProject = ref(null)
  const loading = ref(false)
  const error = ref(null)
  
  // 项目统计
  const projectStats = ref({
    total: 0,
    active: 0,
    completed: 0,
    archived: 0
  })

  // 计算属性
  const activeProjects = computed(() => 
    projects.value.filter(project => project.status === 'active')
  )
  
  const completedProjects = computed(() => 
    projects.value.filter(project => project.status === 'completed')
  )
  
  const archivedProjects = computed(() => 
    projects.value.filter(project => project.status === 'archived')
  )

  const projectsById = computed(() => {
    const map = new Map()
    projects.value.forEach(project => {
      map.set(project.id, project)
    })
    return map
  })

  // 获取项目列表
  const fetchProjects = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await apiService.getProjects(params)
      
      if (response && Array.isArray(response.projects)) {
        projects.value = response.projects
        projectStats.value = response.stats || calculateStats()
      } else {
        // 如果API返回格式不对，使用模拟数据
        projects.value = getMockProjects()
        projectStats.value = calculateStats()
      }
      
      return projects.value
    } catch (err) {
      console.error('获取项目列表失败:', err)
      error.value = err.message || '获取项目列表失败'
      
      // 使用模拟数据作为后备
      projects.value = getMockProjects()
      projectStats.value = calculateStats()
      
      return projects.value
    } finally {
      loading.value = false
    }
  }

  // 获取单个项目详情
  const fetchProject = async (id) => {
    loading.value = true
    error.value = null
    
    try {
      const project = await apiService.getProject(id)
      currentProject.value = project
      
      // 更新项目列表中的对应项目
      const index = projects.value.findIndex(p => p.id === id)
      if (index > -1) {
        projects.value[index] = project
      }
      
      return project
    } catch (err) {
      console.error('获取项目详情失败:', err)
      error.value = err.message || '获取项目详情失败'
      
      // 尝试从现有列表中查找
      const project = projects.value.find(p => p.id === parseInt(id))
      if (project) {
        currentProject.value = project
        return project
      }
      
      throw err
    } finally {
      loading.value = false
    }
  }

  // 创建项目
  const createProject = async (projectData) => {
    loading.value = true
    error.value = null
    
    try {
      const newProject = await apiService.post('/api/projects', projectData)
      projects.value.unshift(newProject)
      projectStats.value = calculateStats()
      return newProject
    } catch (err) {
      console.error('创建项目失败:', err)
      error.value = err.message || '创建项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 更新项目
  const updateProject = async (id, projectData) => {
    loading.value = true
    error.value = null
    
    try {
      const updatedProject = await apiService.put(`/api/projects/${id}`, projectData)
      
      const index = projects.value.findIndex(p => p.id === id)
      if (index > -1) {
        projects.value[index] = updatedProject
      }
      
      if (currentProject.value && currentProject.value.id === id) {
        currentProject.value = updatedProject
      }
      
      projectStats.value = calculateStats()
      return updatedProject
    } catch (err) {
      console.error('更新项目失败:', err)
      error.value = err.message || '更新项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 删除项目
  const deleteProject = async (id) => {
    loading.value = true
    error.value = null
    
    try {
      await apiService.delete(`/api/projects/${id}`)
      
      projects.value = projects.value.filter(p => p.id !== id)
      
      if (currentProject.value && currentProject.value.id === id) {
        currentProject.value = null
      }
      
      projectStats.value = calculateStats()
    } catch (err) {
      console.error('删除项目失败:', err)
      error.value = err.message || '删除项目失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 根据ID获取项目
  const getProjectById = (id) => {
    return projectsById.value.get(parseInt(id))
  }

  // 搜索项目
  const searchProjects = (query) => {
    if (!query.trim()) return projects.value
    
    const searchTerm = query.toLowerCase()
    return projects.value.filter(project => 
      project.name.toLowerCase().includes(searchTerm) ||
      project.description?.toLowerCase().includes(searchTerm) ||
      project.key?.toLowerCase().includes(searchTerm)
    )
  }

  // 按状态筛选项目
  const filterProjectsByStatus = (status) => {
    if (!status) return projects.value
    return projects.value.filter(project => project.status === status)
  }

  // 重置状态
  const resetState = () => {
    projects.value = []
    currentProject.value = null
    loading.value = false
    error.value = null
    projectStats.value = {
      total: 0,
      active: 0,
      completed: 0,
      archived: 0
    }
  }

  // 计算统计信息
  const calculateStats = () => {
    const stats = {
      total: projects.value.length,
      active: 0,
      completed: 0,
      archived: 0
    }
    
    projects.value.forEach(project => {
      switch (project.status) {
        case 'active':
          stats.active++
          break
        case 'completed':
          stats.completed++
          break
        case 'archived':
          stats.archived++
          break
      }
    })
    
    return stats
  }

  // 获取模拟项目数据
  const getMockProjects = () => {
    return [
      {
        id: 1,
        name: '质量大盘系统',
        key: 'QDS',
        description: '软件质量监控和管理平台',
        status: 'active',
        owner: '张三',
        team: '质量团队',
        createdAt: '2024-01-01',
        updatedAt: '2024-12-19'
      },
      {
        id: 2,
        name: '自动化测试平台',
        key: 'ATP',
        description: '自动化测试执行和管理系统',
        status: 'active',
        owner: '李四',
        team: '测试团队',
        createdAt: '2024-02-01',
        updatedAt: '2024-12-18'
      },
      {
        id: 3,
        name: '缺陷管理系统',
        key: 'BMS',
        description: '缺陷跟踪和管理工具',
        status: 'completed',
        owner: '王五',
        team: '开发团队',
        createdAt: '2024-03-01',
        updatedAt: '2024-11-30'
      },
      {
        id: 4,
        name: '性能监控平台',
        key: 'PMP',
        description: '应用性能监控和分析系统',
        status: 'active',
        owner: '赵六',
        team: '运维团队',
        createdAt: '2024-04-01',
        updatedAt: '2024-12-15'
      }
    ]
  }

  // 初始化
  const init = async () => {
    await fetchProjects()
  }

  return {
    // 状态
    projects,
    currentProject,
    loading,
    error,
    projectStats,
    
    // 计算属性
    activeProjects,
    completedProjects,
    archivedProjects,
    projectsById,
    
    // 方法
    fetchProjects,
    fetchProject,
    createProject,
    updateProject,
    deleteProject,
    getProjectById,
    searchProjects,
    filterProjectsByStatus,
    resetState,
    init
  }
})
