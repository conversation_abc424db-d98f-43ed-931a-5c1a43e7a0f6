import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

export const useSearchStore = defineStore('search', () => {
  // 状态
  const suggestions = ref([])
  const searchResults = ref([])
  const recentSearches = ref([])
  const quickSearches = ref([])
  const searchHistory = ref([])
  const loading = ref(false)
  const totalResults = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(20)

  // 搜索统计
  const searchStats = ref({
    totalSearches: 0,
    popularQueries: [],
    searchTrends: []
  })

  // 计算属性
  const hasResults = computed(() => searchResults.value.length > 0)
  const hasMoreResults = computed(() => {
    return totalResults.value > currentPage.value * pageSize.value
  })

  // 获取搜索建议
  const fetchSuggestions = async (params) => {
    try {
      const response = await axios.get('/api/search/suggestions', {
        params: {
          query: params.query,
          type: params.type || 'all',
          limit: params.limit || 8
        }
      })
      
      suggestions.value = response.data.suggestions || []
      return suggestions.value
    } catch (error) {
      console.error('获取搜索建议失败:', error)
      // 返回模拟数据
      suggestions.value = generateMockSuggestions(params.query)
      return suggestions.value
    }
  }

  // 执行搜索
  const performSearch = async (params) => {
    loading.value = true
    
    try {
      const response = await axios.get('/api/search', {
        params: {
          query: params.query,
          type: params.type || 'all',
          scopes: params.scopes,
          timeRange: params.timeRange,
          startDate: params.startDate,
          endDate: params.endDate,
          projectId: params.projectId,
          statuses: params.statuses,
          page: currentPage.value,
          pageSize: pageSize.value
        }
      })
      
      const data = response.data
      searchResults.value = data.results || []
      totalResults.value = data.total || 0
      
      // 更新搜索统计
      updateSearchStats(params)
      
      return {
        results: searchResults.value,
        total: totalResults.value,
        page: currentPage.value,
        pageSize: pageSize.value
      }
    } catch (error) {
      console.error('搜索失败:', error)
      // 返回模拟数据
      const mockResults = generateMockSearchResults(params)
      searchResults.value = mockResults.results
      totalResults.value = mockResults.total
      
      return mockResults
    } finally {
      loading.value = false
    }
  }

  // 加载更多搜索结果
  const loadMoreResults = async (params) => {
    if (!hasMoreResults.value || loading.value) return
    
    currentPage.value++
    
    try {
      const response = await axios.get('/api/search', {
        params: {
          ...params,
          page: currentPage.value,
          pageSize: pageSize.value
        }
      })
      
      const newResults = response.data.results || []
      searchResults.value.push(...newResults)
      
      return newResults
    } catch (error) {
      console.error('加载更多结果失败:', error)
      currentPage.value-- // 回滚页码
      return []
    }
  }

  // 添加到最近搜索
  const addRecentSearch = (searchItem) => {
    // 移除重复项
    const existingIndex = recentSearches.value.findIndex(
      item => item.query === searchItem.query && item.type === searchItem.type
    )
    
    if (existingIndex > -1) {
      recentSearches.value.splice(existingIndex, 1)
    }
    
    // 添加到开头
    recentSearches.value.unshift(searchItem)
    
    // 限制数量
    if (recentSearches.value.length > 10) {
      recentSearches.value = recentSearches.value.slice(0, 10)
    }
    
    // 保存到本地存储
    saveRecentSearches()
  }

  // 清除最近搜索
  const clearRecentSearches = () => {
    recentSearches.value = []
    localStorage.removeItem('recentSearches')
  }

  // 删除单个最近搜索
  const removeRecentSearch = (index) => {
    recentSearches.value.splice(index, 1)
    saveRecentSearches()
  }

  // 加载快捷搜索
  const loadQuickSearches = async () => {
    try {
      const response = await axios.get('/api/search/quick-searches')
      quickSearches.value = response.data.quickSearches || []
    } catch (error) {
      console.error('加载快捷搜索失败:', error)
      // 使用默认快捷搜索
      quickSearches.value = getDefaultQuickSearches()
    }
  }

  // 获取搜索统计
  const fetchSearchStats = async () => {
    try {
      const response = await axios.get('/api/search/stats')
      searchStats.value = response.data
    } catch (error) {
      console.error('获取搜索统计失败:', error)
    }
  }

  // 保存搜索历史
  const saveSearchHistory = (searchParams, results) => {
    const historyItem = {
      id: Date.now(),
      query: searchParams.query,
      type: searchParams.type,
      filters: {
        scopes: searchParams.scopes,
        timeRange: searchParams.timeRange,
        projectId: searchParams.projectId,
        statuses: searchParams.statuses
      },
      resultCount: results.total,
      timestamp: new Date().toISOString()
    }
    
    searchHistory.value.unshift(historyItem)
    
    // 限制历史记录数量
    if (searchHistory.value.length > 50) {
      searchHistory.value = searchHistory.value.slice(0, 50)
    }
    
    // 保存到本地存储
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
  }

  // 重置搜索状态
  const resetSearch = () => {
    searchResults.value = []
    totalResults.value = 0
    currentPage.value = 1
    suggestions.value = []
    loading.value = false
  }

  // 私有方法
  const saveRecentSearches = () => {
    try {
      localStorage.setItem('recentSearches', JSON.stringify(recentSearches.value))
    } catch (error) {
      console.error('保存最近搜索失败:', error)
    }
  }

  const loadRecentSearches = () => {
    try {
      const saved = localStorage.getItem('recentSearches')
      if (saved) {
        recentSearches.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载最近搜索失败:', error)
    }
  }

  const loadSearchHistory = () => {
    try {
      const saved = localStorage.getItem('searchHistory')
      if (saved) {
        searchHistory.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载搜索历史失败:', error)
    }
  }

  const updateSearchStats = (params) => {
    searchStats.value.totalSearches++
    
    // 更新热门查询
    const existingQuery = searchStats.value.popularQueries.find(
      q => q.query === params.query
    )
    
    if (existingQuery) {
      existingQuery.count++
    } else {
      searchStats.value.popularQueries.push({
        query: params.query,
        count: 1,
        type: params.type
      })
    }
    
    // 按计数排序并限制数量
    searchStats.value.popularQueries.sort((a, b) => b.count - a.count)
    searchStats.value.popularQueries = searchStats.value.popularQueries.slice(0, 10)
  }

  // 模拟数据生成函数
  const generateMockSuggestions = (query) => {
    const mockSuggestions = [
      { text: `${query} 相关缺陷`, type: 'defect', category: '缺陷管理' },
      { text: `${query} 项目`, type: 'project', category: '项目管理' },
      { text: `${query} 用户`, type: 'user', category: '用户管理' },
      { text: `${query} 覆盖率报告`, type: 'coverage', category: '测试覆盖率' }
    ]
    
    return mockSuggestions.filter(s => 
      s.text.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 6)
  }

  const generateMockSearchResults = (params) => {
    const mockResults = [
      {
        id: 1,
        type: 'defect',
        title: `缺陷 #001: ${params.query} 相关问题`,
        description: '这是一个与搜索查询相关的缺陷描述...',
        updatedAt: Date.now() - 86400000
      },
      {
        id: 2,
        type: 'project',
        title: `项目: ${params.query} 管理系统`,
        description: '项目描述信息...',
        updatedAt: Date.now() - 172800000
      }
    ]
    
    return {
      results: mockResults,
      total: mockResults.length,
      page: 1,
      pageSize: 20
    }
  }

  const getDefaultQuickSearches = () => {
    return [
      {
        id: 1,
        label: '未解决的缺陷',
        query: 'status:open',
        type: 'defect',
        icon: 'fas fa-bug',
        count: 23
      },
      {
        id: 2,
        label: '高优先级问题',
        query: 'priority:high',
        type: 'defect',
        icon: 'fas fa-exclamation-triangle',
        count: 8
      },
      {
        id: 3,
        label: '活跃项目',
        query: 'status:active',
        type: 'project',
        icon: 'fas fa-project-diagram',
        count: 12
      },
      {
        id: 4,
        label: '低覆盖率文件',
        query: 'coverage:<60',
        type: 'coverage',
        icon: 'fas fa-chart-line',
        count: 15
      }
    ]
  }

  // 初始化
  const init = () => {
    loadRecentSearches()
    loadSearchHistory()
    loadQuickSearches()
  }

  // 在store创建时初始化
  init()

  return {
    // 状态
    suggestions,
    searchResults,
    recentSearches,
    quickSearches,
    searchHistory,
    loading,
    totalResults,
    currentPage,
    pageSize,
    searchStats,
    
    // 计算属性
    hasResults,
    hasMoreResults,
    
    // 方法
    fetchSuggestions,
    performSearch,
    loadMoreResults,
    addRecentSearch,
    clearRecentSearches,
    removeRecentSearch,
    loadQuickSearches,
    fetchSearchStats,
    saveSearchHistory,
    resetSearch,
    init
  }
})
