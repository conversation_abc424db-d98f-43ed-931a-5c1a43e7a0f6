import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const isLoading = ref(false)
  const notifications = ref([])
  const sidebarOpen = ref(false)
  
  // 计算属性
  const hasNotifications = computed(() => notifications.value.length > 0)
  
  // 方法
  const setLoading = (loading) => {
    isLoading.value = loading
  }
  
  const addNotification = (notification) => {
    const id = Date.now().toString()
    notifications.value.push({
      id,
      type: 'info',
      duration: 5000,
      ...notification,
    })
    
    // 自动移除通知
    if (notification.duration !== 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration || 5000)
    }
  }
  
  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }
  
  const clearNotifications = () => {
    notifications.value = []
  }
  
  const toggleSidebar = () => {
    sidebarOpen.value = !sidebarOpen.value
  }
  
  const closeSidebar = () => {
    sidebarOpen.value = false
  }
  
  // 显示成功通知
  const showSuccess = (message, title = '成功') => {
    addNotification({
      type: 'success',
      title,
      message,
    })
  }
  
  // 显示错误通知
  const showError = (message, title = '错误') => {
    addNotification({
      type: 'error',
      title,
      message,
      duration: 8000,
    })
  }
  
  // 显示警告通知
  const showWarning = (message, title = '警告') => {
    addNotification({
      type: 'warning',
      title,
      message,
    })
  }
  
  // 显示信息通知
  const showInfo = (message, title = '信息') => {
    addNotification({
      type: 'info',
      title,
      message,
    })
  }
  
  return {
    // 状态
    isLoading,
    notifications,
    sidebarOpen,
    
    // 计算属性
    hasNotifications,
    
    // 方法
    setLoading,
    addNotification,
    removeNotification,
    clearNotifications,
    toggleSidebar,
    closeSidebar,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  }
})
