/**
 * 测试覆盖率状态管理
 * 管理覆盖率数据的获取、缓存和状态
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'

export const useCoverageStore = defineStore('coverage', () => {
  // 状态数据
  const coverageMetrics = ref([])
  const currentMetric = ref(null)
  const fileCoverages = ref([])
  const coverageStats = ref({})
  const coverageTrends = ref([])
  const coverageDistribution = ref([])
  
  // 加载状态
  const loading = ref({
    metrics: false,
    stats: false,
    trends: false,
    distribution: false,
    files: false
  })
  
  // 查询参数
  const queryParams = ref({
    project_id: null,
    branch_name: 'main',
    source: null,
    page: 1,
    pageSize: 20,
    sortBy: 'measurement_date',
    sortOrder: 'desc'
  })
  
  // 趋势查询参数
  const trendParams = ref({
    project_id: null,
    branch_name: 'main',
    date_range: '30d',
    group_by: 'day',
    coverage_type: 'line'
  })
  
  // 计算属性
  const totalMetrics = computed(() => coverageMetrics.value.length)
  
  const averageCoverage = computed(() => {
    if (coverageMetrics.value.length === 0) return 0
    const total = coverageMetrics.value.reduce((sum, metric) => sum + metric.overall_coverage, 0)
    return Math.round(total / coverageMetrics.value.length * 10) / 10
  })
  
  const latestMetric = computed(() => {
    if (coverageMetrics.value.length === 0) return null
    return coverageMetrics.value[0] // 假设已按时间倒序排列
  })
  
  const coverageLevelCounts = computed(() => {
    const counts = {
      excellent: 0,
      good: 0,
      fair: 0,
      poor: 0,
      critical: 0
    }
    
    coverageMetrics.value.forEach(metric => {
      counts[metric.coverage_level] = (counts[metric.coverage_level] || 0) + 1
    })
    
    return counts
  })
  
  const coverageLevelColors = {
    excellent: '#10b981', // 绿色
    good: '#3b82f6',      // 蓝色
    fair: '#f59e0b',      // 黄色
    poor: '#f97316',      // 橙色
    critical: '#ef4444'   // 红色
  }


  
  // Actions
  async function fetchCoverageMetrics(params = {}) {
    loading.value.metrics = true
    try {
      const mergedParams = { ...queryParams.value, ...params }
      const response = await apiService.query('/api/coverage/', mergedParams)
      coverageMetrics.value = response || []

      // 更新查询参数
      Object.assign(queryParams.value, mergedParams)

      return coverageMetrics.value
    } catch (error) {
      console.error('获取覆盖率指标失败:', error)
      throw error
    } finally {
      loading.value.metrics = false
    }
  }
  
  async function fetchCoverageStats(params = {}) {
    loading.value.stats = true
    try {
      const response = await apiService.get('/api/coverage/stats', params)
      if (response.success) {
        coverageStats.value = response.data
      }
      return coverageStats.value
    } catch (error) {
      console.error('获取覆盖率统计失败:', error)
      throw error
    } finally {
      loading.value.stats = false
    }
  }
  
  async function fetchCoverageTrends(params = {}) {
    loading.value.trends = true
    try {
      const mergedParams = { ...trendParams.value, ...params }
      const response = await apiService.get('/api/coverage/trends', mergedParams)

      if (response.success) {
        coverageTrends.value = response.data.trends || []

        // 更新趋势参数
        Object.assign(trendParams.value, mergedParams)
      }

      return coverageTrends.value
    } catch (error) {
      console.error('获取覆盖率趋势失败:', error)
      throw error
    } finally {
      loading.value.trends = false
    }
  }
  
  async function fetchCoverageDistribution(params = {}) {
    loading.value.distribution = true
    try {
      const response = await apiService.get('/api/coverage/distribution', params)
      if (response.success) {
        coverageDistribution.value = response.data.distribution || []
      }
      return coverageDistribution.value
    } catch (error) {
      console.error('获取覆盖率分布失败:', error)
      throw error
    } finally {
      loading.value.distribution = false
    }
  }
  
  async function fetchFileCoverages(metricId, params = {}) {
    loading.value.files = true
    try {
      const response = await apiService.get(`/api/coverage/files/${metricId}`, params)
      if (response.success) {
        fileCoverages.value = response.data.files || []
        currentMetric.value = response.data.metric_info
      }
      return fileCoverages.value
    } catch (error) {
      console.error('获取文件覆盖率失败:', error)
      throw error
    } finally {
      loading.value.files = false
    }
  }
  
  // 数据格式化方法
  function formatCoverageValue(value) {
    if (typeof value !== 'number') return '0%'
    return `${Math.round(value * 10) / 10}%`
  }
  
  function getCoverageColor(value) {
    if (value >= 90) return coverageLevelColors.excellent
    if (value >= 80) return coverageLevelColors.good
    if (value >= 70) return coverageLevelColors.fair
    if (value >= 60) return coverageLevelColors.poor
    return coverageLevelColors.critical
  }
  
  function getCoverageLevel(value) {
    if (value >= 90) return 'excellent'
    if (value >= 80) return 'good'
    if (value >= 70) return 'fair'
    if (value >= 60) return 'poor'
    return 'critical'
  }
  
  function getCoverageLevelText(level) {
    const levelMap = {
      excellent: '优秀',
      good: '良好',
      fair: '一般',
      poor: '较差',
      critical: '危险'
    }
    return levelMap[level] || '未知'
  }
  
  // 图表数据转换
  function getTrendChartData() {
    if (coverageTrends.value.length === 0) {
      return {
        labels: [],
        datasets: []
      }
    }
    
    const labels = coverageTrends.value.map(item => item.date)
    
    const datasets = [
      {
        label: '行覆盖率',
        data: coverageTrends.value.map(item => item.line_coverage),
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.3,
        fill: true
      },
      {
        label: '分支覆盖率',
        data: coverageTrends.value.map(item => item.branch_coverage),
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.3,
        fill: true
      },
      {
        label: '函数覆盖率',
        data: coverageTrends.value.map(item => item.function_coverage),
        borderColor: '#f59e0b',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        tension: 0.3,
        fill: true
      }
    ]
    
    return { labels, datasets }
  }
  
  function getDistributionChartData() {
    if (coverageDistribution.value.length === 0) {
      return {
        labels: [],
        datasets: []
      }
    }
    
    const labels = coverageDistribution.value.map(item => getCoverageLevelText(item.level))
    const data = coverageDistribution.value.map(item => item.count)
    const backgroundColor = coverageDistribution.value.map(item => coverageLevelColors[item.level])
    
    return {
      labels,
      datasets: [{
        data,
        backgroundColor,
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    }
  }
  
  // 重置状态
  function resetState() {
    coverageMetrics.value = []
    currentMetric.value = null
    fileCoverages.value = []
    coverageStats.value = {}
    coverageTrends.value = []
    coverageDistribution.value = []
    
    // 重置加载状态
    Object.keys(loading.value).forEach(key => {
      loading.value[key] = false
    })
  }
  
  // 更新查询参数
  function updateQueryParams(params) {
    Object.assign(queryParams.value, params)
  }
  
  function updateTrendParams(params) {
    Object.assign(trendParams.value, params)
  }
  
  return {
    // 状态
    coverageMetrics,
    currentMetric,
    fileCoverages,
    coverageStats,
    coverageTrends,
    coverageDistribution,
    loading,
    queryParams,
    trendParams,
    
    // 计算属性
    totalMetrics,
    averageCoverage,
    latestMetric,
    coverageLevelCounts,
    coverageLevelColors,
    
    // 方法
    fetchCoverageMetrics,
    fetchCoverageStats,
    fetchCoverageTrends,
    fetchCoverageDistribution,
    fetchFileCoverages,
    formatCoverageValue,
    getCoverageColor,
    getCoverageLevel,
    getCoverageLevelText,
    getTrendChartData,
    getDistributionChartData,
    resetState,
    updateQueryParams,
    updateTrendParams
  }
})
