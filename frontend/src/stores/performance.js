import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'
import { useAppStore } from './app'

export const usePerformanceStore = defineStore('performance', () => {
  const appStore = useAppStore()

  // 状态
  const performanceMetrics = ref([])
  const serviceMetrics = ref([])
  const systemMetrics = ref([])
  const overviewData = ref(null)
  const trendsData = ref(null)
  const alerts = ref([])
  const realTimeMetrics = ref(null)
  
  // 加载状态
  const loading = ref({
    overview: false,
    trends: false,
    services: false,
    realTime: false,
    alerts: false
  })

  // 查询参数
  const queryParams = ref({
    page: 1,
    pageSize: 20,
    sortBy: 'record_time',
    sortOrder: 'desc',
    metricType: '',
    timeRange: '1h',
    hostName: ''
  })

  // 计算属性
  const averageResponseTime = computed(() => {
    if (serviceMetrics.value.length === 0) return 0
    const total = serviceMetrics.value.reduce((sum, metric) => sum + metric.avg_response_time, 0)
    return Math.round(total / serviceMetrics.value.length)
  })

  const systemHealthScore = computed(() => {
    if (systemMetrics.value.length === 0) return 100
    
    const latestMetrics = systemMetrics.value[0]
    if (!latestMetrics) return 100
    
    // 基于CPU、内存、磁盘使用率计算健康分数
    const cpuScore = Math.max(0, 100 - latestMetrics.cpu_usage)
    const memoryScore = Math.max(0, 100 - latestMetrics.memory_usage)
    const diskScore = latestMetrics.disk_usage ? Math.max(0, 100 - latestMetrics.disk_usage) : 100
    
    return Math.round((cpuScore + memoryScore + diskScore) / 3)
  })

  const criticalAlerts = computed(() => 
    alerts.value.filter(alert => alert.severity === 'critical')
  )

  const serviceAvailability = computed(() => {
    if (serviceMetrics.value.length === 0) return 100
    const total = serviceMetrics.value.reduce((sum, metric) => sum + metric.availability, 0)
    return Math.round(total / serviceMetrics.value.length * 100) / 100
  })

  // 方法
  const fetchOverview = async (params = {}) => {
    loading.value.overview = true
    try {
      const response = await apiService.getPerformanceOverview({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        overviewData.value = response.data
        performanceMetrics.value = response.data.metrics || []
      }
    } catch (error) {
      appStore.showError('获取性能概览失败')
      console.error('Failed to fetch performance overview:', error)
    } finally {
      loading.value.overview = false
    }
  }

  const fetchServicePerformance = async (params = {}) => {
    loading.value.services = true
    try {
      const response = await apiService.getServicePerformance({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        serviceMetrics.value = response.data.services || []
      }
    } catch (error) {
      appStore.showError('获取服务性能数据失败')
      console.error('Failed to fetch service performance:', error)
    } finally {
      loading.value.services = false
    }
  }

  const fetchTrends = async (params = {}) => {
    loading.value.trends = true
    try {
      const response = await apiService.getPerformanceTrends({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        trendsData.value = response.data
      }
    } catch (error) {
      appStore.showError('获取性能趋势失败')
      console.error('Failed to fetch performance trends:', error)
    } finally {
      loading.value.trends = false
    }
  }

  const fetchAlerts = async (params = {}) => {
    loading.value.alerts = true
    try {
      const response = await apiService.getPerformanceAlerts({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        alerts.value = response.data.alerts || []
      }
    } catch (error) {
      appStore.showError('获取性能告警失败')
      console.error('Failed to fetch performance alerts:', error)
    } finally {
      loading.value.alerts = false
    }
  }

  const fetchRealTimeMetrics = async (params = {}) => {
    loading.value.realTime = true
    try {
      const response = await apiService.getRealTimeMetrics({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        realTimeMetrics.value = response.data
      }
    } catch (error) {
      appStore.showError('获取实时指标失败')
      console.error('Failed to fetch real-time metrics:', error)
    } finally {
      loading.value.realTime = false
    }
  }

  const fetchSystemMetrics = async (params = {}) => {
    try {
      const response = await apiService.getSystemMetrics({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        systemMetrics.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取系统指标失败')
      console.error('Failed to fetch system metrics:', error)
    }
  }

  // 筛选方法
  const filterByMetricType = (type) => {
    updateQueryParams({ metricType: type, page: 1 })
    fetchOverview()
  }

  const filterByTimeRange = (range) => {
    updateQueryParams({ timeRange: range, page: 1 })
    refreshAll()
  }

  const filterByHost = (hostName) => {
    updateQueryParams({ hostName: hostName, page: 1 })
    fetchSystemMetrics()
  }

  // 更新查询参数
  const updateQueryParams = (newParams) => {
    queryParams.value = { ...queryParams.value, ...newParams }
  }

  // 重置状态
  const resetState = () => {
    performanceMetrics.value = []
    serviceMetrics.value = []
    systemMetrics.value = []
    overviewData.value = null
    trendsData.value = null
    alerts.value = []
    realTimeMetrics.value = null
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchOverview(),
      fetchServicePerformance(),
      fetchTrends(),
      fetchAlerts(),
      fetchSystemMetrics()
    ])
  }

  // 启动实时数据更新
  const startRealTimeUpdates = () => {
    const interval = setInterval(() => {
      fetchRealTimeMetrics()
    }, 30000) // 每30秒更新一次
    
    return interval
  }

  return {
    // 状态
    performanceMetrics,
    serviceMetrics,
    systemMetrics,
    overviewData,
    trendsData,
    alerts,
    realTimeMetrics,
    loading,
    queryParams,
    
    // 计算属性
    averageResponseTime,
    systemHealthScore,
    criticalAlerts,
    serviceAvailability,
    
    // 方法
    fetchOverview,
    fetchServicePerformance,
    fetchTrends,
    fetchAlerts,
    fetchRealTimeMetrics,
    fetchSystemMetrics,
    filterByMetricType,
    filterByTimeRange,
    filterByHost,
    updateQueryParams,
    resetState,
    refreshAll,
    startRealTimeUpdates
  }
})
