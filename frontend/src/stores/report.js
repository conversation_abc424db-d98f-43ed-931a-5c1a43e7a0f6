import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/utils/api'

export const useReportStore = defineStore('report', () => {
  // 状态
  const supportedFormats = ref([])
  const availableSections = ref([])
  const templates = ref([])
  const stats = ref({})
  const reports = ref([])
  const recentReports = ref([])
  const loading = ref(false)

  // 获取支持的报告格式
  const fetchSupportedFormats = async () => {
    try {
      const response = await api.get('/api/reports/formats')
      if (response.data.success) {
        supportedFormats.value = response.data.data.formats
      }
    } catch (error) {
      console.error('获取支持的报告格式失败:', error)
      // 设置默认格式
      supportedFormats.value = [
        { id: 'excel', name: 'Excel', extension: 'xlsx' },
        { id: 'csv', name: 'CSV', extension: 'csv' },
        { id: 'pdf', name: 'PDF', extension: 'pdf' }
      ]
    }
  }

  // 获取可用的报告部分
  const fetchAvailableSections = async () => {
    try {
      const response = await api.get('/reports/sections')
      if (response.data.success) {
        availableSections.value = response.data.data.sections
      }
    } catch (error) {
      console.error('获取可用的报告部分失败:', error)
      throw error
    }
  }

  // 获取报告模板
  const fetchTemplates = async () => {
    try {
      const response = await api.get('/reports/templates')
      if (response.data.success) {
        templates.value = response.data.data.templates
      }
    } catch (error) {
      console.error('获取报告模板失败:', error)
      throw error
    }
  }

  // 获取报告统计
  const fetchStats = async (params = {}) => {
    try {
      const response = await api.get('/reports/stats', { params })
      if (response.data.success) {
        stats.value = response.data.data
        recentReports.value = response.data.data.recent_reports || []
      }
    } catch (error) {
      console.error('获取报告统计失败:', error)
      throw error
    }
  }

  // 下载报告
  const downloadReport = async (params) => {
    try {
      loading.value = true
      
      const response = await api.get('/reports/quality/download', {
        params,
        responseType: 'blob'
      })

      // 创建下载链接
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // 从响应头获取文件名
      const contentDisposition = response.headers['content-disposition']
      let filename = 'quality_report'
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '')
        }
      } else {
        // 根据格式设置默认文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
        const extension = params.format === 'excel' ? 'xlsx' : params.format === 'csv' ? 'csv' : 'pdf'
        filename = `quality_report_${timestamp}.${extension}`
      }

      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      // 刷新统计数据
      await fetchStats()

    } catch (error) {
      console.error('下载报告失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 异步生成报告
  const generateReportAsync = async (data) => {
    try {
      loading.value = true
      
      const response = await api.post('/reports/quality/generate', data)
      
      if (response.data.success) {
        return response.data
      } else {
        throw new Error(response.data.message || '生成报告失败')
      }
    } catch (error) {
      console.error('异步生成报告失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取报告模板配置
  const getTemplateConfig = (templateId) => {
    return templates.value.find(t => t.id === templateId)
  }

  // 验证报告参数
  const validateReportParams = (params) => {
    const errors = []

    if (!params.project_ids || params.project_ids.length === 0) {
      errors.push('请选择至少一个项目')
    }

    if (!params.format) {
      errors.push('请选择报告格式')
    }

    if (!params.include_sections || params.include_sections.length === 0) {
      errors.push('请选择至少一个报告部分')
    }

    const supportedFormatIds = supportedFormats.value.map(f => f.id)
    if (params.format && !supportedFormatIds.includes(params.format)) {
      errors.push('不支持的报告格式')
    }

    const availableSectionIds = availableSections.value.map(s => s.id)
    if (params.include_sections) {
      const invalidSections = params.include_sections.filter(s => !availableSectionIds.includes(s))
      if (invalidSections.length > 0) {
        errors.push(`不支持的报告部分: ${invalidSections.join(', ')}`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取格式信息
  const getFormatInfo = (formatId) => {
    return supportedFormats.value.find(f => f.id === formatId)
  }

  // 获取部分信息
  const getSectionInfo = (sectionId) => {
    return availableSections.value.find(s => s.id === sectionId)
  }

  // 获取报告列表
  const fetchReports = async (params = {}) => {
    try {
      const response = await api.get('/api/reports/', { params })
      if (response.data.success) {
        reports.value = response.data.data.reports
        return response.data.data
      }
    } catch (error) {
      console.error('获取报告列表失败:', error)
      throw error
    }
  }

  // 下载报告文件
  const downloadReportFile = async (reportId) => {
    try {
      const response = await api.get(`/api/reports/${reportId}/download`, {
        responseType: 'blob'
      })

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `report_${reportId}.xlsx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      return true
    } catch (error) {
      console.error('下载报告文件失败:', error)
      throw error
    }
  }

  // 删除报告
  const deleteReport = async (reportId) => {
    try {
      const response = await api.delete(`/api/reports/${reportId}`)
      return response.data
    } catch (error) {
      console.error('删除报告失败:', error)
      throw error
    }
  }

  // 重置状态
  const reset = () => {
    supportedFormats.value = []
    availableSections.value = []
    templates.value = []
    stats.value = {}
    reports.value = []
    recentReports.value = []
    loading.value = false
  }

  return {
    // 状态
    supportedFormats,
    availableSections,
    templates,
    stats,
    reports,
    recentReports,
    loading,

    // 方法
    fetchSupportedFormats,
    fetchAvailableSections,
    fetchTemplates,
    fetchStats,
    fetchReports,
    downloadReport,
    downloadReportFile,
    deleteReport,
    generateReportAsync,
    getTemplateConfig,
    validateReportParams,
    formatFileSize,
    getFormatInfo,
    getSectionInfo,
    reset
  }
})
