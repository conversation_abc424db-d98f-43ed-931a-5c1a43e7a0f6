import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useNotificationStore = defineStore('notification', () => {
  // 状态
  const notifications = ref([])
  const maxNotifications = ref(5) // 最大通知数量

  // 通知类型
  const NotificationType = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
  }

  // 添加通知
  const addNotification = (notification) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const newNotification = {
      id,
      type: NotificationType.INFO,
      title: '',
      message: '',
      duration: 5000, // 默认5秒
      persistent: false, // 是否持久显示
      actions: [], // 操作按钮
      ...notification,
      createdAt: new Date()
    }

    notifications.value.unshift(newNotification)

    // 限制通知数量
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }

    // 自动移除非持久通知
    if (!newNotification.persistent && newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, newNotification.duration)
    }

    return id
  }

  // 移除通知
  const removeNotification = (id) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  // 清空所有通知
  const clearAll = () => {
    notifications.value = []
  }

  // 便捷方法 - 成功通知
  const showSuccess = (message, title = '成功', options = {}) => {
    return addNotification({
      type: NotificationType.SUCCESS,
      title,
      message,
      ...options
    })
  }

  // 便捷方法 - 错误通知
  const showError = (message, title = '错误', options = {}) => {
    return addNotification({
      type: NotificationType.ERROR,
      title,
      message,
      duration: 8000, // 错误通知显示更久
      ...options
    })
  }

  // 便捷方法 - 警告通知
  const showWarning = (message, title = '警告', options = {}) => {
    return addNotification({
      type: NotificationType.WARNING,
      title,
      message,
      duration: 6000,
      ...options
    })
  }

  // 便捷方法 - 信息通知
  const showInfo = (message, title = '信息', options = {}) => {
    return addNotification({
      type: NotificationType.INFO,
      title,
      message,
      ...options
    })
  }

  // 显示确认通知（带操作按钮）
  const showConfirm = (message, title = '确认', options = {}) => {
    const defaultActions = [
      {
        label: '确认',
        action: 'confirm',
        style: 'primary'
      },
      {
        label: '取消',
        action: 'cancel',
        style: 'secondary'
      }
    ]

    return addNotification({
      type: NotificationType.WARNING,
      title,
      message,
      persistent: true,
      actions: defaultActions,
      ...options
    })
  }

  // 显示加载通知
  const showLoading = (message, title = '加载中...', options = {}) => {
    return addNotification({
      type: NotificationType.INFO,
      title,
      message,
      persistent: true,
      showSpinner: true,
      ...options
    })
  }

  // 更新通知
  const updateNotification = (id, updates) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      Object.assign(notification, updates)
    }
  }

  // 标记通知为已读
  const markAsRead = (id) => {
    updateNotification(id, { read: true })
  }

  // 标记所有通知为已读
  const markAllAsRead = () => {
    notifications.value.forEach(notification => {
      notification.read = true
    })
  }

  // 获取未读通知数量
  const getUnreadCount = () => {
    return notifications.value.filter(n => !n.read).length
  }

  // 获取特定类型的通知
  const getNotificationsByType = (type) => {
    return notifications.value.filter(n => n.type === type)
  }

  // 处理通知操作
  const handleNotificationAction = (notificationId, action) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification && notification.onAction) {
      notification.onAction(action, notification)
    }
    
    // 如果是取消或确认操作，自动移除通知
    if (action === 'cancel' || action === 'confirm') {
      removeNotification(notificationId)
    }
  }

  // 批量操作
  const batchRemove = (ids) => {
    ids.forEach(id => removeNotification(id))
  }

  // 根据条件移除通知
  const removeByCondition = (condition) => {
    notifications.value = notifications.value.filter(n => !condition(n))
  }

  // 获取通知统计
  const getStats = () => {
    const total = notifications.value.length
    const unread = getUnreadCount()
    const byType = {
      success: getNotificationsByType(NotificationType.SUCCESS).length,
      error: getNotificationsByType(NotificationType.ERROR).length,
      warning: getNotificationsByType(NotificationType.WARNING).length,
      info: getNotificationsByType(NotificationType.INFO).length
    }

    return {
      total,
      unread,
      read: total - unread,
      byType
    }
  }

  // 重置状态
  const reset = () => {
    notifications.value = []
  }

  return {
    // 状态
    notifications,
    maxNotifications,
    
    // 常量
    NotificationType,
    
    // 基础方法
    addNotification,
    removeNotification,
    clearAll,
    updateNotification,
    
    // 便捷方法
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
    showLoading,
    
    // 管理方法
    markAsRead,
    markAllAsRead,
    getUnreadCount,
    getNotificationsByType,
    handleNotificationAction,
    batchRemove,
    removeByCondition,
    getStats,
    reset
  }
})
