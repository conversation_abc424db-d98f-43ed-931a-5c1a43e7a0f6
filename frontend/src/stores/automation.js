import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'
import { useAppStore } from './app'

export const useAutomationStore = defineStore('automation', () => {
  const appStore = useAppStore()

  // 状态
  const testCases = ref([])
  const testExecutions = ref([])
  const automationMetrics = ref([])
  const overviewData = ref(null)
  const executionHistory = ref([])
  const trendsData = ref(null)
  const teamStats = ref([])
  
  // 加载状态
  const loading = ref({
    overview: false,
    history: false,
    trends: false,
    testCases: false,
    executions: false
  })

  // 查询参数
  const queryParams = ref({
    page: 1,
    pageSize: 20,
    sortBy: 'created_at',
    sortOrder: 'desc',
    testType: '',
    status: '',
    dateRange: '7d'
  })

  // 计算属性
  const totalTestCases = computed(() => testCases.value.length)
  const automatedTestCases = computed(() => 
    testCases.value.filter(tc => tc.is_automated).length
  )
  const automationRate = computed(() => {
    if (totalTestCases.value === 0) return 0
    return Math.round((automatedTestCases.value / totalTestCases.value) * 100)
  })
  
  const recentExecutions = computed(() => 
    testExecutions.value.slice(0, 10)
  )
  
  const passRate = computed(() => {
    if (testExecutions.value.length === 0) return 0
    const passed = testExecutions.value.filter(ex => ex.status === 'passed').length
    return Math.round((passed / testExecutions.value.length) * 100)
  })

  // 方法
  const fetchOverview = async (params = {}) => {
    loading.value.overview = true
    try {
      const response = await apiService.getAutomationOverview({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        overviewData.value = response.data
        automationMetrics.value = response.data.metrics || []
      }
    } catch (error) {
      appStore.showError('获取自动化概览失败')
      console.error('Failed to fetch automation overview:', error)
    } finally {
      loading.value.overview = false
    }
  }

  const fetchExecutionHistory = async (params = {}) => {
    loading.value.history = true
    try {
      const response = await apiService.getAutomationExecutionHistory({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        executionHistory.value = response.data.execution_results || []
      }
    } catch (error) {
      appStore.showError('获取执行历史失败')
      console.error('Failed to fetch execution history:', error)
    } finally {
      loading.value.history = false
    }
  }

  const fetchTrends = async (params = {}) => {
    loading.value.trends = true
    try {
      const response = await apiService.getAutomationTrends({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        trendsData.value = response.data
      }
    } catch (error) {
      appStore.showError('获取自动化趋势失败')
      console.error('Failed to fetch automation trends:', error)
    } finally {
      loading.value.trends = false
    }
  }

  const fetchTestCases = async (params = {}) => {
    loading.value.testCases = true
    try {
      const response = await apiService.getTestCases({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        testCases.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取测试用例失败')
      console.error('Failed to fetch test cases:', error)
    } finally {
      loading.value.testCases = false
    }
  }

  const fetchTestExecutions = async (params = {}) => {
    loading.value.executions = true
    try {
      const response = await apiService.getTestExecutions({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        testExecutions.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取测试执行记录失败')
      console.error('Failed to fetch test executions:', error)
    } finally {
      loading.value.executions = false
    }
  }

  const fetchTeamStats = async (params = {}) => {
    try {
      const response = await apiService.getAutomationTeamStats({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        teamStats.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取团队统计失败')
      console.error('Failed to fetch team stats:', error)
    }
  }

  // 筛选方法
  const filterByTestType = (type) => {
    updateQueryParams({ testType: type, page: 1 })
    fetchTestCases()
  }

  const filterByStatus = (status) => {
    updateQueryParams({ status: status, page: 1 })
    fetchTestExecutions()
  }

  const filterByDateRange = (range) => {
    updateQueryParams({ dateRange: range, page: 1 })
    refreshAll()
  }

  // 更新查询参数
  const updateQueryParams = (newParams) => {
    queryParams.value = { ...queryParams.value, ...newParams }
  }

  // 重置状态
  const resetState = () => {
    testCases.value = []
    testExecutions.value = []
    automationMetrics.value = []
    overviewData.value = null
    executionHistory.value = []
    trendsData.value = null
    teamStats.value = []
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchOverview(),
      fetchExecutionHistory(),
      fetchTrends(),
      fetchTestCases(),
      fetchTestExecutions()
    ])
  }

  return {
    // 状态
    testCases,
    testExecutions,
    automationMetrics,
    overviewData,
    executionHistory,
    trendsData,
    teamStats,
    loading,
    queryParams,
    
    // 计算属性
    totalTestCases,
    automatedTestCases,
    automationRate,
    recentExecutions,
    passRate,
    
    // 方法
    fetchOverview,
    fetchExecutionHistory,
    fetchTrends,
    fetchTestCases,
    fetchTestExecutions,
    fetchTeamStats,
    filterByTestType,
    filterByStatus,
    filterByDateRange,
    updateQueryParams,
    resetState,
    refreshAll
  }
})
