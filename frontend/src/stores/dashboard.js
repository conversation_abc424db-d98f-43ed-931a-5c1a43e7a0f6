import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'
import { useAppStore } from './app'

export const useDashboardStore = defineStore('dashboard', () => {
  const appStore = useAppStore()

  // 状态
  const projects = ref([])
  const teams = ref([])
  const metricCards = ref([])
  const qualityTrends = ref([])
  const overviewData = ref(null)
  const trendsData = ref(null)
  const teamComparison = ref([])
  
  // 加载状态
  const loading = ref({
    overview: false,
    trends: false,
    teams: false,
    projects: false
  })

  // 查询参数
  const queryParams = ref({
    page: 1,
    pageSize: 20,
    sortBy: 'created_at',
    sortOrder: 'desc',
    dateRange: '7d'
  })

  // 个性化仪表板状态
  const userLayout = ref(null)
  const widgets = ref([])
  const dashboardConfigs = ref([])
  const currentUserId = ref(1) // 默认用户ID
  const focusedMetricIds = ref([]) // 用户关注的指标ID列表
  const defaultLayout = ref([
    { i: 'metric-overview', x: 0, y: 0, w: 12, h: 4 },
    { i: 'defect-trend', x: 0, y: 4, w: 6, h: 6 },
    { i: 'coverage-trend', x: 6, y: 4, w: 6, h: 6 },
    { i: 'team-comparison', x: 0, y: 10, w: 12, h: 6 }
  ])

  // 计算属性
  const totalProjects = computed(() => projects.value.length)
  const activeProjects = computed(() => 
    projects.value.filter(p => p.status === 'active').length
  )
  const averageQualityScore = computed(() => {
    if (teams.value.length === 0) return 0
    const scores = teams.value.map(t => {
      switch(t.quality_score) {
        case 'A': return 90
        case 'B': return 80
        case 'C': return 70
        case 'D': return 60
        default: return 0
      }
    })
    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length)
  })

  // 方法
  const fetchOverview = async (params = {}) => {
    loading.value.overview = true
    try {
      const response = await apiService.getDashboardOverview({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        overviewData.value = response.data
        metricCards.value = response.data.metric_cards || []
      }
    } catch (error) {
      appStore.showError('获取概览数据失败')
      console.error('Failed to fetch overview:', error)
    } finally {
      loading.value.overview = false
    }
  }

  const fetchTrends = async (params = {}) => {
    loading.value.trends = true
    try {
      const response = await apiService.getDashboardTrends({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        trendsData.value = response.data
      }
    } catch (error) {
      appStore.showError('获取趋势数据失败')
      console.error('Failed to fetch trends:', error)
    } finally {
      loading.value.trends = false
    }
  }

  const fetchTeamComparison = async (params = {}) => {
    loading.value.teams = true
    try {
      const response = await apiService.getTeamComparison({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        teamComparison.value = response.data.teams || []
      }
    } catch (error) {
      appStore.showError('获取团队对比数据失败')
      console.error('Failed to fetch team comparison:', error)
    } finally {
      loading.value.teams = false
    }
  }

  const fetchProjects = async (params = {}) => {
    loading.value.projects = true
    try {
      const response = await apiService.getProjects({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        projects.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取项目列表失败')
      console.error('Failed to fetch projects:', error)
    } finally {
      loading.value.projects = false
    }
  }

  const fetchTeams = async (params = {}) => {
    loading.value.teams = true
    try {
      const response = await apiService.getTeams({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        teams.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取团队列表失败')
      console.error('Failed to fetch teams:', error)
    } finally {
      loading.value.teams = false
    }
  }

  const fetchQualityTrends = async (params = {}) => {
    try {
      const response = await apiService.getQualityTrends({
        ...queryParams.value,
        ...params
      })
      
      if (response.success) {
        qualityTrends.value = response.data || []
      }
    } catch (error) {
      appStore.showError('获取质量趋势失败')
      console.error('Failed to fetch quality trends:', error)
    }
  }

  // 更新查询参数
  const updateQueryParams = (newParams) => {
    queryParams.value = { ...queryParams.value, ...newParams }
  }

  // 重置状态
  const resetState = () => {
    projects.value = []
    teams.value = []
    metricCards.value = []
    qualityTrends.value = []
    overviewData.value = null
    trendsData.value = null
    teamComparison.value = []
  }

  // 刷新所有数据
  const refreshAll = async () => {
    await Promise.all([
      fetchOverview(),
      fetchTrends(),
      fetchTeamComparison(),
      fetchProjects(),
      fetchTeams()
    ])
  }

  // 个性化仪表板方法
  const loadUserLayout = async (userId = null) => {
    const id = userId || currentUserId.value
    try {
      const response = await apiService.get(`/dashboard-config/users/${id}/default-config`)
      if (response.success && response.data) {
        userLayout.value = response.data.layout_config || defaultLayout.value
        widgets.value = initializeWidgets(response.data.widget_configs || {})
        focusedMetricIds.value = response.data.focused_metric_ids || []
      } else {
        userLayout.value = defaultLayout.value
        widgets.value = initializeDefaultWidgets()
        focusedMetricIds.value = []
      }
    } catch (error) {
      console.error('加载用户布局失败:', error)
      userLayout.value = defaultLayout.value
      widgets.value = initializeDefaultWidgets()
      focusedMetricIds.value = []
    }
  }

  const saveLayout = async (layout) => {
    try {
      const configData = {
        config_name: '当前配置',
        is_default: true,
        layout_config: layout,
        widget_configs: getWidgetConfigs(),
        focused_metric_ids: focusedMetricIds.value
      }
      // 应该调用 PUT 来更新现有（默认）配置，或者确保 POST 创建时能正确处理
      // 查找当前用户的默认配置
      const existingConfigs = await getUserConfigs()
      const defaultConfig = existingConfigs.find(c => c.is_default)

      if (defaultConfig) {
        // 更新现有的默认配置
        await apiService.put(`/dashboard-config/configs/${defaultConfig.id}`, configData)
      } else {
        // 如果没有默认配置，则创建一个新的（不太可能，因为 loadUserLayout 会尝试创建）
        await apiService.post(`/dashboard-config/users/${currentUserId.value}/configs`, configData)
      }
      userLayout.value = layout
    } catch (error) {
      console.error('保存布局失败:', error)
      throw error
    }
  }

  const addWidget = (widget) => {
    widgets.value.push(widget)
  }

  const removeWidget = (widgetId) => {
    widgets.value = widgets.value.filter(w => w.id !== widgetId)
  }

  const updateWidgetConfig = (widgetId, config) => {
    const widget = widgets.value.find(w => w.id === widgetId)
    if (widget) {
      widget.config = { ...widget.config, ...config }
    }
  }

  const resetLayout = async () => {
    userLayout.value = [...defaultLayout.value]
    widgets.value = initializeDefaultWidgets()
    focusedMetricIds.value = []
  }

  const getUserConfigs = async () => {
    try {
      const response = await apiService.get(`/dashboard-config/users/${currentUserId.value}/configs`)
      if (response.success) {
        dashboardConfigs.value = response.data
        return response.data
      }
      return []
    } catch (error) {
      console.error('获取用户配置失败:', error)
      return []
    }
  }

  const saveConfig = async (configName, layout) => {
    try {
      const configData = {
        config_name: configName,
        is_default: false,
        layout_config: layout,
        widget_configs: getWidgetConfigs(),
        focused_metric_ids: focusedMetricIds.value // 保存关注的指标
      }

      await apiService.post(`/dashboard-config/users/${currentUserId.value}/configs`, configData)
    } catch (error) {
      console.error('保存配置失败:', error)
      throw error
    }
  }

  const loadConfig = async (configId) => {
    try {
      const response = await apiService.get(`/dashboard-config/configs/${configId}`)
      if (response.success && response.data) {
        userLayout.value = response.data.layout_config
        widgets.value = initializeWidgets(response.data.widget_configs || {})
        focusedMetricIds.value = response.data.focused_metric_ids || []
      }
    } catch (error) {
      console.error('加载配置失败:', error)
      throw error
    }
  }

  const deleteConfig = async (configId) => {
    try {
      await apiService.delete(`/dashboard-config/configs/${configId}`)
    } catch (error) {
      console.error('删除配置失败:', error)
      throw error
    }
  }

  const setDefaultConfig = async (configId) => {
    try {
      await apiService.put(`/dashboard-config/configs/${configId}`, { is_default: true })
    } catch (error) {
      console.error('设置默认配置失败:', error)
      throw error
    }
  }

  const importConfig = (config) => {
    if (config.layout) {
      userLayout.value = config.layout
    }
    if (config.widgets) {
      widgets.value = config.widgets
    }
    if (config.focusedMetricIds) {
      focusedMetricIds.value = config.focusedMetricIds
    }
  }

  // 辅助方法
  const initializeDefaultWidgets = () => {
    return [
      {
        id: 'metric-overview',
        type: 'metric-cards',
        title: '质量指标概览',
        config: { showTrend: true, showTarget: true },
        data: null
      },
      {
        id: 'defect-trend',
        type: 'defect-trend-chart',
        title: '缺陷趋势',
        config: { dateRange: '30d', groupBy: 'day' },
        data: null
      },
      {
        id: 'coverage-trend',
        type: 'coverage-trend-chart',
        title: '覆盖率趋势',
        config: { dateRange: '30d', metricType: 'line' },
        data: null
      },
      {
        id: 'team-comparison',
        type: 'team-comparison-chart',
        title: '团队质量对比',
        config: { showDetails: true },
        data: null
      }
    ]
  }

  const initializeWidgets = (widgetConfigs) => {
    const defaultWidgets = initializeDefaultWidgets()
    return defaultWidgets.map(widget => ({
      ...widget,
      config: { ...widget.config, ...(widgetConfigs[widget.id] || {}) }
    }))
  }

  const getWidgetConfigs = () => {
    const configs = {}
    widgets.value.forEach(widget => {
      configs[widget.id] = widget.config
    })
    return configs
  }

  const setFocusedMetricIds = async (metricIds) => {
    focusedMetricIds.value = metricIds
    // Optionally, immediately save this change to the backend
    // This depends on whether focused metrics are saved independently or as part of the whole layout
    // For now, we assume it's saved with the layout via saveLayout or saveConfig
    // If immediate save is needed:
    // const currentConfig = dashboardConfigs.value.find(c => c.is_default); // Or however the current config is identified
    // if (currentConfig) {
    //   await apiService.put(`/dashboard-config/configs/${currentConfig.id}`, { focused_metric_ids: metricIds });
    // }
  }

  return {
    // 状态
    projects,
    teams,
    metricCards,
    qualityTrends,
    overviewData,
    trendsData,
    teamComparison,
    loading,
    queryParams,

    // 个性化仪表板状态
    userLayout,
    widgets,
    dashboardConfigs,
    currentUserId,
    defaultLayout,
    focusedMetricIds, // 导出 focusedMetricIds

    // 计算属性
    totalProjects,
    activeProjects,
    averageQualityScore,

    // 方法
    fetchOverview,
    fetchTrends,
    fetchTeamComparison,
    fetchProjects,
    fetchTeams,
    fetchQualityTrends,
    updateQueryParams,
    resetState,
    refreshAll,

    // 个性化仪表板方法
    loadUserLayout,
    saveLayout,
    addWidget,
    removeWidget,
    updateWidgetConfig,
    resetLayout,
    getUserConfigs,
    saveConfig,
    loadConfig,
    deleteConfig,
    setDefaultConfig,
    importConfig,
    setFocusedMetricIds // 导出 setFocusedMetricIds
  }
})
