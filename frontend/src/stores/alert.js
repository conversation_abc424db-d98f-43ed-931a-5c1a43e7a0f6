import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/utils/api'

export const useAlertStore = defineStore('alert', () => {
  // 状态
  const alerts = ref([])
  const rules = ref([])
  const stats = ref({})
  const loading = ref(false)

  // 获取预警列表
  const fetchAlerts = async (params = {}) => {
    loading.value = true
    try {
      const response = await api.get('/api/alerts/', { params })
      alerts.value = response.data.data.alerts
      return response.data.data
    } catch (error) {
      console.error('获取预警列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取预警统计
  const fetchStats = async (days = 30) => {
    try {
      const response = await api.get('/api/alerts/stats', {
        params: { days }
      })
      stats.value = response.data.data
      return response.data.data
    } catch (error) {
      console.error('获取预警统计失败:', error)
      throw error
    }
  }

  // 获取预警规则列表
  const fetchRules = async (params = {}) => {
    try {
      const response = await api.get('/api/alerts/rules', { params })
      rules.value = response.data.data.rules
      return response.data.data
    } catch (error) {
      console.error('获取预警规则失败:', error)
      throw error
    }
  }

  // 创建预警规则
  const createRule = async (ruleData) => {
    try {
      const response = await api.post('/api/alerts/rules', ruleData)
      return response.data.data
    } catch (error) {
      console.error('创建预警规则失败:', error)
      throw error
    }
  }

  // 更新预警规则
  const updateRule = async (ruleId, ruleData) => {
    try {
      const response = await api.put(`/api/alerts/rules/${ruleId}`, ruleData)
      return response.data.data
    } catch (error) {
      console.error('更新预警规则失败:', error)
      throw error
    }
  }

  // 删除预警规则
  const deleteRule = async (ruleId) => {
    try {
      const response = await api.delete(`/api/alerts/rules/${ruleId}`)
      return response.data
    } catch (error) {
      console.error('删除预警规则失败:', error)
      throw error
    }
  }

  // 确认预警
  const acknowledgeAlert = async (alertId, userId) => {
    try {
      const response = await api.post(`/api/alerts/${alertId}/acknowledge`, {
        acknowledged_by: userId
      })
      return response.data.data
    } catch (error) {
      console.error('确认预警失败:', error)
      throw error
    }
  }

  // 解决预警
  const resolveAlert = async (alertId, userId) => {
    try {
      const response = await api.post(`/api/alerts/${alertId}/resolve`, {
        resolved_by: userId
      })
      return response.data.data
    } catch (error) {
      console.error('解决预警失败:', error)
      throw error
    }
  }

  // 忽略预警
  const dismissAlert = async (alertId, userId) => {
    try {
      const response = await api.post(`/api/alerts/${alertId}/dismiss`, {
        dismissed_by: userId
      })
      return response.data.data
    } catch (error) {
      console.error('忽略预警失败:', error)
      throw error
    }
  }

  // 触发预警检查
  const triggerCheck = async () => {
    try {
      const response = await api.post('/api/alerts/check')
      return response.data
    } catch (error) {
      console.error('触发预警检查失败:', error)
      throw error
    }
  }

  // 获取预警详情
  const getAlertDetail = async (alertId) => {
    try {
      const response = await api.get(`/api/alerts/${alertId}`)
      return response.data.data
    } catch (error) {
      console.error('获取预警详情失败:', error)
      throw error
    }
  }

  // 获取预警历史
  const getAlertHistory = async (alertId) => {
    try {
      const response = await api.get(`/api/alerts/${alertId}/history`)
      return response.data.data
    } catch (error) {
      console.error('获取预警历史失败:', error)
      throw error
    }
  }

  // 测试通知配置
  const testNotification = async (notificationType, config) => {
    try {
      const response = await api.post('/api/alerts/test-notification', {
        notification_type: notificationType,
        config
      })
      return response.data
    } catch (error) {
      console.error('测试通知失败:', error)
      throw error
    }
  }

  // 获取预警趋势数据
  const getAlertTrends = async (params = {}) => {
    try {
      const response = await api.get('/api/alerts/trends', { params })
      return response.data.data
    } catch (error) {
      console.error('获取预警趋势失败:', error)
      throw error
    }
  }

  // 批量操作预警
  const batchUpdateAlerts = async (alertIds, action, data = {}) => {
    try {
      const response = await api.post('/api/alerts/batch', {
        alert_ids: alertIds,
        action,
        ...data
      })
      return response.data
    } catch (error) {
      console.error('批量操作预警失败:', error)
      throw error
    }
  }

  // 导出预警数据
  const exportAlerts = async (params = {}) => {
    try {
      const response = await api.get('/api/alerts/export', {
        params,
        responseType: 'blob'
      })
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `alerts_${new Date().toISOString().split('T')[0]}.xlsx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      return true
    } catch (error) {
      console.error('导出预警数据失败:', error)
      throw error
    }
  }

  // 重置状态
  const resetState = () => {
    alerts.value = []
    rules.value = []
    stats.value = {}
    loading.value = false
  }

  return {
    // 状态
    alerts,
    rules,
    stats,
    loading,
    
    // 方法
    fetchAlerts,
    fetchStats,
    fetchRules,
    createRule,
    updateRule,
    deleteRule,
    acknowledgeAlert,
    resolveAlert,
    dismissAlert,
    triggerCheck,
    getAlertDetail,
    getAlertHistory,
    testNotification,
    getAlertTrends,
    batchUpdateAlerts,
    exportAlerts,
    resetState
  }
})
