import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '@/services/api'

export const useDefectStore = defineStore('defect', () => {
  // 状态
  const defects = ref([])
  const trendData = ref(null)
  const distributionData = ref(null)
  const statistics = ref(null)
  const loading = ref({
    list: false,
    trends: false,
    distribution: false,
    statistics: false
  })
  const error = ref(null)
  
  // 查询参数
  const queryParams = ref({
    page: 1,
    pageSize: 20,
    sortBy: 'created_at',
    sortOrder: 'desc',
    severity: '',
    status: '',
    priority: '',
    project_id: null,
    dateRange: '30d'
  })

  // 计算属性
  const totalDefects = computed(() => statistics.value?.total_defects || 0)
  const openDefects = computed(() => statistics.value?.open_defects || 0)
  const resolvedDefects = computed(() => statistics.value?.resolved_defects || 0)
  const criticalDefects = computed(() => statistics.value?.critical_defects || 0)

  // 严重程度颜色映射
  const severityColors = {
    critical: '#ef4444',
    high: '#f97316', 
    medium: '#eab308',
    low: '#22c55e'
  }

  // 状态颜色映射
  const statusColors = {
    open: '#ef4444',
    in_progress: '#f59e0b',
    resolved: '#10b981',
    closed: '#6b7280',
    reopened: '#8b5cf6'
  }

  // 获取缺陷列表
  const fetchDefects = async (params = {}) => {
    loading.value.list = true
    error.value = null
    
    try {
      const mergedParams = { ...queryParams.value, ...params }
      const response = await apiService.get('/api/defects', mergedParams)
      
      if (response.success) {
        defects.value = response.data.items || []
        // 更新查询参数
        Object.assign(queryParams.value, mergedParams)
        return response.data
      } else {
        throw new Error(response.message || '获取缺陷列表失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取缺陷列表失败:', err)
      throw err
    } finally {
      loading.value.list = false
    }
  }

  // 获取缺陷趋势数据
  const fetchTrendData = async (params = {}) => {
    loading.value.trends = true
    error.value = null

    try {
      const mergedParams = {
        date_range: queryParams.value.dateRange,
        group_by: 'day',
        ...params
      }

      // 只有当project_id不为null时才添加到参数中
      if (queryParams.value.project_id !== null && queryParams.value.project_id !== undefined) {
        mergedParams.project_id = queryParams.value.project_id
      }
      if (params.project_id !== null && params.project_id !== undefined) {
        mergedParams.project_id = params.project_id
      }

      const response = await apiService.get('/api/defects/trends', mergedParams)
      
      if (response.success) {
        trendData.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取缺陷趋势失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取缺陷趋势失败:', err)
      throw err
    } finally {
      loading.value.trends = false
    }
  }

  // 获取缺陷分布数据
  const fetchDistributionData = async (params = {}) => {
    loading.value.distribution = true
    error.value = null

    try {
      const mergedParams = {
        dimension: 'severity',
        ...params
      }

      // 只有当project_id不为null时才添加到参数中
      if (queryParams.value.project_id !== null && queryParams.value.project_id !== undefined) {
        mergedParams.project_id = queryParams.value.project_id
      }
      if (params.project_id !== null && params.project_id !== undefined) {
        mergedParams.project_id = params.project_id
      }

      const response = await apiService.get('/api/defects/distribution', mergedParams)
      
      if (response.success) {
        distributionData.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取缺陷分布失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取缺陷分布失败:', err)
      throw err
    } finally {
      loading.value.distribution = false
    }
  }

  // 获取缺陷统计数据
  const fetchStatistics = async (params = {}) => {
    loading.value.statistics = true
    error.value = null

    try {
      const mergedParams = {
        date_range: queryParams.value.dateRange,
        ...params
      }

      // 只有当project_id不为null时才添加到参数中
      if (queryParams.value.project_id !== null && queryParams.value.project_id !== undefined) {
        mergedParams.project_id = queryParams.value.project_id
      }
      if (params.project_id !== null && params.project_id !== undefined) {
        mergedParams.project_id = params.project_id
      }

      const response = await apiService.get('/api/defects/stats', mergedParams)
      
      if (response.success) {
        statistics.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取缺陷统计失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取缺陷统计失败:', err)
      throw err
    } finally {
      loading.value.statistics = false
    }
  }

  // 获取单个缺陷详情
  const fetchDefectById = async (id) => {
    try {
      const response = await apiService.get(`/api/defects/${id}`)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '获取缺陷详情失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取缺陷详情失败:', err)
      throw err
    }
  }

  // 创建缺陷
  const createDefect = async (defectData) => {
    try {
      const response = await apiService.post('/api/defects', defectData)
      
      if (response.success) {
        // 刷新列表
        await fetchDefects()
        await fetchStatistics()
        return response.data
      } else {
        throw new Error(response.message || '创建缺陷失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('创建缺陷失败:', err)
      throw err
    }
  }

  // 更新缺陷
  const updateDefect = async (id, defectData) => {
    try {
      const response = await apiService.put(`/api/defects/${id}`, defectData)
      
      if (response.success) {
        // 刷新列表
        await fetchDefects()
        await fetchStatistics()
        return response.data
      } else {
        throw new Error(response.message || '更新缺陷失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('更新缺陷失败:', err)
      throw err
    }
  }

  // 删除缺陷
  const deleteDefect = async (id) => {
    try {
      const response = await apiService.delete(`/api/defects/${id}`)
      
      if (response.success) {
        // 刷新列表
        await fetchDefects()
        await fetchStatistics()
        return true
      } else {
        throw new Error(response.message || '删除缺陷失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('删除缺陷失败:', err)
      throw err
    }
  }

  // 重置状态
  const resetState = () => {
    defects.value = []
    trendData.value = null
    distributionData.value = null
    statistics.value = null
    error.value = null
    Object.assign(loading.value, {
      list: false,
      trends: false,
      distribution: false,
      statistics: false
    })
  }

  // 更新查询参数
  const updateQueryParams = (params) => {
    Object.assign(queryParams.value, params)
  }

  // 获取严重程度颜色
  const getSeverityColor = (severity) => {
    return severityColors[severity] || '#6b7280'
  }

  // 获取状态颜色
  const getStatusColor = (status) => {
    return statusColors[status] || '#6b7280'
  }

  // 格式化缺陷数据用于图表
  const formatTrendChartData = (trends) => {
    if (!trends || !trends.trends) return null

    const trendList = trends.trends
    const dates = trendList.map(item => item.date)
    const severities = ['critical', 'high', 'medium', 'low']

    return {
      labels: dates,
      datasets: severities.map(severity => ({
        label: severity.toUpperCase(),
        data: trendList.map(item => item[`${severity}_count`] || 0),
        borderColor: severityColors[severity],
        backgroundColor: severityColors[severity] + '20',
        tension: 0.4,
        fill: false
      }))
    }
  }

  // 格式化分布数据用于图表
  const formatDistributionChartData = (distribution) => {
    if (!distribution || !distribution.distribution) return null

    const data = distribution.distribution
    
    return {
      labels: data.map(item => {
        // 根据不同维度获取标签
        return item.severity || item.status || item.project || item.label || 'unknown'
      }),
      datasets: [{
        data: data.map(item => item.count),
        backgroundColor: data.map(item => {
          // 安全地获取标签并转换为小写
          const label = item.severity || item.status || item.project || item.label || 'unknown'
          const severity = typeof label === 'string' ? label.toLowerCase() : String(label).toLowerCase()
          return severityColors[severity] || '#6b7280'
        }),
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    }
  }

  return {
    // 状态
    defects,
    trendData,
    distributionData,
    statistics,
    loading,
    error,
    queryParams,
    
    // 计算属性
    totalDefects,
    openDefects,
    resolvedDefects,
    criticalDefects,
    
    // 方法
    fetchDefects,
    fetchTrendData,
    fetchDistributionData,
    fetchStatistics,
    fetchDefectById,
    createDefect,
    updateDefect,
    deleteDefect,
    resetState,
    updateQueryParams,
    getSeverityColor,
    getStatusColor,
    formatTrendChartData,
    formatDistributionChartData
  }
})
