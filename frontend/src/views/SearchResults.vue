<template>
  <div class="search-results-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="fas fa-search mr-3"></i>
          搜索结果
        </h1>
        <p class="page-subtitle">
          为 "{{ searchQuery }}" 找到 {{ totalResults }} 条结果
        </p>
      </div>
      
      <!-- 搜索栏 -->
      <div class="search-bar-container">
        <GlobalSearch
          :auto-focus="false"
          :show-preview="false"
          @search="handleNewSearch"
        />
      </div>
    </div>

    <!-- 搜索筛选器 -->
    <div class="search-filters">
      <div class="filter-section">
        <div class="filter-group">
          <label class="filter-label">类型:</label>
          <div class="filter-buttons">
            <button
              v-for="type in searchTypes"
              :key="type.value"
              @click="selectType(type.value)"
              class="filter-btn"
              :class="{ 'active': selectedType === type.value }"
            >
              <i :class="type.icon" class="mr-2"></i>
              {{ type.label }}
              <span v-if="type.count" class="count-badge">{{ type.count }}</span>
            </button>
          </div>
        </div>

        <div class="filter-group">
          <label class="filter-label">排序:</label>
          <select v-model="sortBy" @change="handleSortChange" class="sort-select">
            <option value="relevance">相关性</option>
            <option value="date">时间</option>
            <option value="title">标题</option>
            <option value="type">类型</option>
          </select>
          
          <button @click="toggleSortOrder" class="sort-order-btn">
            <i :class="sortOrder === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down'"></i>
          </button>
        </div>

        <div class="filter-actions">
          <button @click="showAdvancedFilters = !showAdvancedFilters" class="advanced-filter-btn">
            <i class="fas fa-sliders-h mr-2"></i>
            高级筛选
          </button>
          <button @click="clearFilters" class="clear-filters-btn">
            <i class="fas fa-times mr-2"></i>
            清除筛选
          </button>
        </div>
      </div>

      <!-- 高级筛选面板 -->
      <transition name="slide-down">
        <div v-if="showAdvancedFilters" class="advanced-filters-panel">
          <div class="advanced-filters-content">
            <div class="filter-row">
              <div class="filter-item">
                <label class="filter-item-label">项目:</label>
                <select v-model="filters.projectId" class="filter-select">
                  <option value="">全部项目</option>
                  <option v-for="project in projects" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </option>
                </select>
              </div>

              <div class="filter-item">
                <label class="filter-item-label">状态:</label>
                <select v-model="filters.status" class="filter-select">
                  <option value="">全部状态</option>
                  <option value="open">开放</option>
                  <option value="in_progress">进行中</option>
                  <option value="resolved">已解决</option>
                  <option value="closed">已关闭</option>
                </select>
              </div>

              <div class="filter-item">
                <label class="filter-item-label">时间范围:</label>
                <select v-model="filters.timeRange" class="filter-select">
                  <option value="">全部时间</option>
                  <option value="1d">最近1天</option>
                  <option value="7d">最近7天</option>
                  <option value="30d">最近30天</option>
                  <option value="90d">最近90天</option>
                </select>
              </div>
            </div>

            <div class="filter-actions">
              <button @click="applyAdvancedFilters" class="apply-filters-btn">
                <i class="fas fa-check mr-2"></i>
                应用筛选
              </button>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 搜索结果内容 -->
    <div class="search-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <LoadingSpinner message="搜索中..." />
      </div>

      <!-- 搜索结果 -->
      <div v-else-if="searchResults.length > 0" class="results-container">
        <!-- 结果统计 -->
        <div class="results-stats">
          <span class="results-count">
            显示第 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalResults) }} 条，
            共 {{ totalResults }} 条结果
          </span>
          <span class="search-time">
            搜索耗时 {{ searchTime }}ms
          </span>
        </div>

        <!-- 结果列表 -->
        <div class="results-list">
          <div
            v-for="result in searchResults"
            :key="result.id"
            @click="selectResult(result)"
            class="result-item"
            :class="`result-${result.type}`"
          >
            <div class="result-header">
              <div class="result-icon">
                <i :class="getResultIcon(result.type)"></i>
              </div>
              <div class="result-meta">
                <span class="result-type">{{ getResultTypeLabel(result.type) }}</span>
                <span class="result-id" v-if="result.id">#{{ result.id }}</span>
                <span class="result-date">{{ formatDate(result.updatedAt) }}</span>
              </div>
            </div>

            <div class="result-content">
              <h3 class="result-title" v-html="highlightMatch(result.title)"></h3>
              <p class="result-description" v-html="highlightMatch(result.description)"></p>
              
              <div class="result-details">
                <div v-if="result.project" class="result-project">
                  <i class="fas fa-project-diagram mr-1"></i>
                  {{ result.project.name }}
                </div>
                <div v-if="result.assignee" class="result-assignee">
                  <i class="fas fa-user mr-1"></i>
                  {{ result.assignee.name }}
                </div>
                <div v-if="result.priority" class="result-priority" :class="`priority-${result.priority}`">
                  <i class="fas fa-flag mr-1"></i>
                  {{ getPriorityLabel(result.priority) }}
                </div>
                <div v-if="result.status" class="result-status" :class="`status-${result.status}`">
                  <i class="fas fa-circle mr-1"></i>
                  {{ getStatusLabel(result.status) }}
                </div>
              </div>

              <div class="result-tags" v-if="result.tags && result.tags.length > 0">
                <span
                  v-for="tag in result.tags.slice(0, 3)"
                  :key="tag"
                  class="result-tag"
                >
                  {{ tag }}
                </span>
                <span v-if="result.tags.length > 3" class="more-tags">
                  +{{ result.tags.length - 3 }}
                </span>
              </div>
            </div>

            <div class="result-actions">
              <button @click.stop="viewResult(result)" class="action-btn view-btn">
                <i class="fas fa-eye mr-1"></i>
                查看
              </button>
              <button @click.stop="editResult(result)" class="action-btn edit-btn" v-if="canEdit(result)">
                <i class="fas fa-edit mr-1"></i>
                编辑
              </button>
              <button @click.stop="shareResult(result)" class="action-btn share-btn">
                <i class="fas fa-share mr-1"></i>
                分享
              </button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <div class="pagination">
            <button
              @click="goToPage(currentPage - 1)"
              :disabled="currentPage <= 1"
              class="pagination-btn prev-btn"
            >
              <i class="fas fa-chevron-left"></i>
              上一页
            </button>

            <div class="pagination-pages">
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="goToPage(page)"
                class="pagination-page"
                :class="{ 'active': page === currentPage }"
              >
                {{ page }}
              </button>
            </div>

            <button
              @click="goToPage(currentPage + 1)"
              :disabled="!hasMoreResults"
              class="pagination-btn next-btn"
            >
              下一页
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>

          <div class="pagination-info">
            <span>每页 {{ pageSize }} 条</span>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 无结果状态 -->
      <div v-else class="no-results">
        <div class="no-results-icon">
          <i class="fas fa-search text-gray-400 text-6xl"></i>
        </div>
        <h3 class="no-results-title">未找到相关结果</h3>
        <p class="no-results-description">
          尝试使用不同的关键词或调整筛选条件
        </p>
        <div class="no-results-suggestions">
          <h4 class="suggestions-title">搜索建议:</h4>
          <ul class="suggestions-list">
            <li>检查拼写是否正确</li>
            <li>尝试使用更通用的关键词</li>
            <li>减少筛选条件</li>
            <li>使用同义词或相关词汇</li>
          </ul>
        </div>
        <button @click="clearAllFilters" class="clear-all-btn">
          <i class="fas fa-refresh mr-2"></i>
          清除所有筛选条件
        </button>
      </div>
    </div>

    <!-- 搜索历史侧边栏 -->
    <div class="search-sidebar" :class="{ 'open': showSidebar }">
      <div class="sidebar-header">
        <h3 class="sidebar-title">搜索历史</h3>
        <button @click="showSidebar = false" class="close-sidebar-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="sidebar-content">
        <div v-if="searchHistory.length === 0" class="empty-history">
          <i class="fas fa-history text-gray-400 text-3xl mb-2"></i>
          <p class="text-gray-500">暂无搜索历史</p>
        </div>
        
        <div v-else class="history-list">
          <div
            v-for="history in searchHistory.slice(0, 20)"
            :key="history.id"
            @click="repeatSearch(history)"
            class="history-item"
          >
            <div class="history-content">
              <div class="history-query">{{ history.query }}</div>
              <div class="history-meta">
                <span class="history-type">{{ getResultTypeLabel(history.type) }}</span>
                <span class="history-count">{{ history.resultCount }} 条结果</span>
                <span class="history-time">{{ formatDate(history.timestamp) }}</span>
              </div>
            </div>
            <button @click.stop="removeFromHistory(history.id)" class="remove-history-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 侧边栏切换按钮 -->
    <button @click="showSidebar = !showSidebar" class="sidebar-toggle-btn">
      <i class="fas fa-history"></i>
    </button>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useSearchStore } from '@/stores/search'
import { useProjectStore } from '@/stores/project'
import GlobalSearch from '@/components/search/GlobalSearch.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import { useSearchHighlight } from '@/composables/useSearchHighlight'
import { useSearchPerformance } from '@/composables/useSearchPerformance'

const route = useRoute()
const router = useRouter()
const searchStore = useSearchStore()
const projectStore = useProjectStore()
const { highlightText, getTextSnippets, calculateMatchScore } = useSearchHighlight()
const { optimizedSearch, getPerformanceStats } = useSearchPerformance()

// 响应式数据
const showAdvancedFilters = ref(false)
const showSidebar = ref(false)
const selectedType = ref('all')
const sortBy = ref('relevance')
const sortOrder = ref('desc')
const searchTime = ref(0)

// 筛选条件
const filters = ref({
  projectId: '',
  status: '',
  timeRange: '',
  priority: '',
  assignee: ''
})

// 搜索类型配置
const searchTypes = ref([
  { value: 'all', label: '全部', icon: 'fas fa-globe', count: 0 },
  { value: 'defects', label: '缺陷', icon: 'fas fa-bug', count: 0 },
  { value: 'projects', label: '项目', icon: 'fas fa-project-diagram', count: 0 },
  { value: 'users', label: '用户', icon: 'fas fa-users', count: 0 },
  { value: 'coverage', label: '覆盖率', icon: 'fas fa-chart-line', count: 0 }
])

// 计算属性
const searchQuery = computed(() => route.query.q || '')
const searchResults = computed(() => searchStore.searchResults)
const totalResults = computed(() => searchStore.totalResults)
const currentPage = computed(() => searchStore.currentPage)
const pageSize = computed({
  get: () => searchStore.pageSize,
  set: (value) => searchStore.pageSize = value
})
const loading = computed(() => searchStore.loading)
const hasMoreResults = computed(() => searchStore.hasMoreResults)
const projects = computed(() => projectStore.projects)
const searchHistory = computed(() => searchStore.searchHistory)

// 分页相关计算属性
const totalPages = computed(() => Math.ceil(totalResults.value / pageSize.value))
const visiblePages = computed(() => {
  const current = currentPage.value
  const total = totalPages.value
  const delta = 2

  let start = Math.max(1, current - delta)
  let end = Math.min(total, current + delta)

  if (end - start < 4) {
    if (start === 1) {
      end = Math.min(total, start + 4)
    } else {
      start = Math.max(1, end - 4)
    }
  }

  const pages = []
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// 监听器
watch(() => route.query, (newQuery) => {
  if (newQuery.q) {
    performSearch()
  }
}, { immediate: true })

watch([selectedType, sortBy, sortOrder], () => {
  if (searchQuery.value) {
    performSearch()
  }
})

// 方法
const performSearch = async () => {
  if (!searchQuery.value.trim()) return

  const startTime = Date.now()

  const searchParams = {
    query: searchQuery.value,
    type: selectedType.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value,
    ...filters.value
  }

  try {
    await searchStore.performSearch(searchParams)
    searchTime.value = Date.now() - startTime

    // 更新类型计数
    updateTypeCounts()

    // 保存搜索历史
    searchStore.saveSearchHistory(searchParams, {
      total: totalResults.value,
      time: searchTime.value
    })
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

const handleNewSearch = (searchParams) => {
  router.push({
    name: 'SearchResults',
    query: {
      q: searchParams.query,
      type: searchParams.type || 'all'
    }
  })
}

const selectType = (type) => {
  selectedType.value = type
  updateURL()
}

const handleSortChange = () => {
  performSearch()
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
}

const clearFilters = () => {
  filters.value = {
    projectId: '',
    status: '',
    timeRange: '',
    priority: '',
    assignee: ''
  }
  selectedType.value = 'all'
  sortBy.value = 'relevance'
  sortOrder.value = 'desc'
  performSearch()
}

const clearAllFilters = () => {
  clearFilters()
  router.push({
    name: 'SearchResults',
    query: { q: searchQuery.value }
  })
}

const applyAdvancedFilters = () => {
  showAdvancedFilters.value = false
  performSearch()
}

const goToPage = (page) => {
  if (page < 1 || page > totalPages.value) return

  searchStore.currentPage = page
  performSearch()

  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' })
}

const handlePageSizeChange = () => {
  searchStore.currentPage = 1
  performSearch()
}

const selectResult = (result) => {
  // 根据结果类型跳转到相应页面
  switch (result.type) {
    case 'defect':
      router.push({ name: 'DefectDetail', params: { id: result.id } })
      break
    case 'project':
      router.push({ name: 'ProjectDetail', params: { id: result.id } })
      break
    case 'user':
      router.push({ name: 'UserProfile', params: { id: result.id } })
      break
    case 'coverage':
      router.push({ name: 'CoverageDetail', params: { id: result.id } })
      break
    default:
      console.log('选择结果:', result)
  }
}

const viewResult = (result) => {
  selectResult(result)
}

const editResult = (result) => {
  switch (result.type) {
    case 'defect':
      router.push({ name: 'DefectEdit', params: { id: result.id } })
      break
    case 'project':
      router.push({ name: 'ProjectEdit', params: { id: result.id } })
      break
    default:
      console.log('编辑结果:', result)
  }
}

const shareResult = (result) => {
  const url = `${window.location.origin}/${result.type}/${result.id}`

  if (navigator.share) {
    navigator.share({
      title: result.title,
      text: result.description,
      url: url
    })
  } else {
    // 复制到剪贴板
    navigator.clipboard.writeText(url).then(() => {
      alert('链接已复制到剪贴板')
    })
  }
}

const canEdit = (result) => {
  // 根据用户权限和结果类型判断是否可编辑
  return ['defect', 'project'].includes(result.type)
}

const repeatSearch = (history) => {
  router.push({
    name: 'SearchResults',
    query: {
      q: history.query,
      type: history.type || 'all'
    }
  })
  showSidebar.value = false
}

const removeFromHistory = (historyId) => {
  const index = searchHistory.value.findIndex(h => h.id === historyId)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
    localStorage.setItem('searchHistory', JSON.stringify(searchHistory.value))
  }
}

const updateURL = () => {
  const query = { ...route.query }
  if (selectedType.value !== 'all') {
    query.type = selectedType.value
  } else {
    delete query.type
  }

  router.replace({ query })
}

const updateTypeCounts = () => {
  // 这里应该从API获取各类型的结果数量
  // 暂时使用模拟数据
  const mockCounts = {
    all: totalResults.value,
    defects: Math.floor(totalResults.value * 0.4),
    projects: Math.floor(totalResults.value * 0.3),
    users: Math.floor(totalResults.value * 0.2),
    coverage: Math.floor(totalResults.value * 0.1)
  }

  searchTypes.value.forEach(type => {
    type.count = mockCounts[type.value] || 0
  })
}

// 工具方法
const getResultIcon = (type) => {
  const iconMap = {
    defect: 'fas fa-bug',
    project: 'fas fa-project-diagram',
    user: 'fas fa-user',
    coverage: 'fas fa-chart-line',
    report: 'fas fa-file-alt'
  }
  return iconMap[type] || 'fas fa-file'
}

const getResultTypeLabel = (type) => {
  const labelMap = {
    defect: '缺陷',
    project: '项目',
    user: '用户',
    coverage: '覆盖率',
    report: '报告',
    all: '全部'
  }
  return labelMap[type] || type
}

const getPriorityLabel = (priority) => {
  const labelMap = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '紧急'
  }
  return labelMap[priority] || priority
}

const getStatusLabel = (status) => {
  const labelMap = {
    open: '开放',
    in_progress: '进行中',
    resolved: '已解决',
    closed: '已关闭',
    active: '活跃',
    inactive: '非活跃'
  }
  return labelMap[status] || status
}

const highlightMatch = (text) => {
  if (!text) return text
  return highlightText(text, searchQuery.value, {
    highlightClass: 'search-highlight-result'
  })
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  // 从URL参数初始化状态
  selectedType.value = route.query.type || 'all'

  // 加载项目列表
  projectStore.fetchProjects()

  // 如果有搜索查询，执行搜索
  if (searchQuery.value) {
    performSearch()
  }
})
</script>

<style scoped>
.search-results-page {
  min-height: 100vh;
  background: var(--background, #f9fafb);
  position: relative;
}

/* 页面头部 */
.page-header {
  background: var(--surface, white);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  padding: 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  margin-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--on-surface, #1f2937);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
}

.page-subtitle {
  font-size: 16px;
  color: var(--on-surface-variant, #6b7280);
  margin: 0;
}

.search-bar-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 搜索筛选器 */
.search-filters {
  background: var(--surface, white);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  padding: 16px 24px;
}

.filter-section {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--on-surface, #1f2937);
  white-space: nowrap;
}

.filter-buttons {
  display: flex;
  gap: 8px;
}

.filter-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
  white-space: nowrap;
}

.filter-btn:hover {
  border-color: var(--primary-300, #a5b4fc);
  color: var(--primary-600, #4f46e5);
}

.filter-btn.active {
  background: var(--primary-600, #4f46e5);
  border-color: var(--primary-600, #4f46e5);
  color: white;
}

.count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 2px 6px;
  border-radius: var(--radius-sm, 4px);
  font-size: 12px;
  margin-left: 6px;
}

.filter-btn:not(.active) .count-badge {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-600, #4b5563);
}

.sort-select {
  padding: 6px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  font-size: 14px;
  background: var(--surface, white);
  color: var(--on-surface, #1f2937);
}

.sort-order-btn {
  background: none;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  padding: 6px 8px;
  cursor: pointer;
  color: var(--gray-500, #6b7280);
  transition: all var(--transition-fast, 0.15s ease);
}

.sort-order-btn:hover {
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #374151);
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.advanced-filter-btn,
.clear-filters-btn {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
}

.advanced-filter-btn:hover,
.clear-filters-btn:hover {
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #374151);
}

/* 高级筛选面板 */
.advanced-filters-panel {
  background: var(--surface-variant, #f9fafb);
  border-top: 1px solid var(--border-color, #e5e7eb);
  padding: 16px 24px;
}

.advanced-filters-content {
  max-width: 1200px;
  margin: 0 auto;
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.filter-item-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--on-surface, #1f2937);
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  font-size: 14px;
  background: var(--surface, white);
  color: var(--on-surface, #1f2937);
}

.apply-filters-btn {
  display: flex;
  align-items: center;
  background: var(--primary-600, #4f46e5);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: var(--radius, 6px);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast, 0.15s ease);
}

.apply-filters-btn:hover {
  background: var(--primary-700, #4338ca);
}

/* 搜索内容 */
.search-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 结果统计 */
.results-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.results-count {
  font-size: 14px;
  color: var(--on-surface, #1f2937);
}

.search-time {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

/* 结果列表 */
.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  background: var(--surface, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius-lg, 12px);
  padding: 20px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
  position: relative;
}

.result-item:hover {
  box-shadow: var(--shadow-md, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  border-color: var(--primary-200, #c7d2fe);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius, 6px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-right: 12px;
}

.result-defect .result-icon {
  background: var(--red-100, #fee2e2);
  color: var(--red-600, #dc2626);
}

.result-project .result-icon {
  background: var(--blue-100, #dbeafe);
  color: var(--blue-600, #2563eb);
}

.result-user .result-icon {
  background: var(--green-100, #dcfce7);
  color: var(--green-600, #16a34a);
}

.result-coverage .result-icon {
  background: var(--purple-100, #f3e8ff);
  color: var(--purple-600, #9333ea);
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
}

.result-type {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  padding: 2px 6px;
  border-radius: var(--radius-sm, 4px);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.result-content {
  margin-bottom: 16px;
}

.result-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.result-title :deep(mark) {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  padding: 0 2px;
  border-radius: 2px;
}

.result-description {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
  line-height: 1.5;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-description :deep(mark) {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  padding: 0 2px;
  border-radius: 2px;
}

.result-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.result-project,
.result-assignee,
.result-priority,
.result-status {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--on-surface-variant, #6b7280);
}

.result-priority.priority-high {
  color: var(--red-600, #dc2626);
}

.result-priority.priority-medium {
  color: var(--yellow-600, #d97706);
}

.result-priority.priority-low {
  color: var(--green-600, #16a34a);
}

.result-status.status-open {
  color: var(--blue-600, #2563eb);
}

.result-status.status-in_progress {
  color: var(--yellow-600, #d97706);
}

.result-status.status-resolved {
  color: var(--green-600, #16a34a);
}

.result-status.status-closed {
  color: var(--gray-600, #4b5563);
}

.result-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.result-tag {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  padding: 2px 8px;
  border-radius: var(--radius-sm, 4px);
  font-size: 12px;
}

.more-tags {
  background: var(--gray-200, #e5e7eb);
  color: var(--gray-600, #4b5563);
  padding: 2px 8px;
  border-radius: var(--radius-sm, 4px);
  font-size: 12px;
}

.result-actions {
  display: flex;
  gap: 8px;
  position: absolute;
  top: 20px;
  right: 20px;
  opacity: 0;
  transition: opacity var(--transition-fast, 0.15s ease);
}

.result-item:hover .result-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  font-size: 12px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
}

.action-btn:hover {
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #374151);
}

.view-btn:hover {
  background: var(--blue-50, #eff6ff);
  color: var(--blue-600, #2563eb);
  border-color: var(--blue-200, #bfdbfe);
}

.edit-btn:hover {
  background: var(--green-50, #f0fdf4);
  color: var(--green-600, #16a34a);
  border-color: var(--green-200, #bbf7d0);
}

.share-btn:hover {
  background: var(--purple-50, #faf5ff);
  color: var(--purple-600, #9333ea);
  border-color: var(--purple-200, #e9d5ff);
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #374151);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.pagination-page {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover {
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #374151);
}

.pagination-page.active {
  background: var(--primary-600, #4f46e5);
  border-color: var(--primary-600, #4f46e5);
  color: white;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

.page-size-select {
  padding: 4px 8px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  font-size: 14px;
  background: var(--surface, white);
  color: var(--on-surface, #1f2937);
}

/* 无结果状态 */
.no-results {
  text-align: center;
  padding: 80px 20px;
}

.no-results-icon {
  margin-bottom: 24px;
}

.no-results-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0 0 12px 0;
}

.no-results-description {
  font-size: 16px;
  color: var(--on-surface-variant, #6b7280);
  margin: 0 0 32px 0;
}

.no-results-suggestions {
  max-width: 400px;
  margin: 0 auto 32px;
  text-align: left;
}

.suggestions-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0 0 12px 0;
}

.suggestions-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions-list li {
  padding: 4px 0;
  color: var(--on-surface-variant, #6b7280);
  position: relative;
  padding-left: 16px;
}

.suggestions-list li::before {
  content: '•';
  color: var(--primary-600, #4f46e5);
  position: absolute;
  left: 0;
}

.clear-all-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--primary-600, #4f46e5);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: var(--radius, 6px);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast, 0.15s ease);
  margin: 0 auto;
}

.clear-all-btn:hover {
  background: var(--primary-700, #4338ca);
}

/* 搜索历史侧边栏 */
.search-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--surface, white);
  border-left: 1px solid var(--border-color, #e5e7eb);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  z-index: 1000;
  transition: right var(--transition-normal, 0.25s ease);
  overflow-y: auto;
}

.search-sidebar.open {
  right: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-variant, #f9fafb);
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0;
}

.close-sidebar-btn {
  background: none;
  border: none;
  color: var(--gray-500, #6b7280);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--radius, 6px);
  transition: all var(--transition-fast, 0.15s ease);
}

.close-sidebar-btn:hover {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
}

.sidebar-content {
  padding: 20px;
}

.empty-history {
  text-align: center;
  padding: 40px 20px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
}

.history-item:hover {
  background: var(--surface-variant, #f9fafb);
  border-color: var(--primary-200, #c7d2fe);
}

.history-content {
  flex: 1;
  min-width: 0;
}

.history-query {
  font-size: 14px;
  font-weight: 500;
  color: var(--on-surface, #1f2937);
  margin-bottom: 4px;
  word-break: break-word;
}

.history-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
}

.history-type {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  padding: 1px 4px;
  border-radius: var(--radius-sm, 4px);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
  display: inline-block;
  width: fit-content;
}

.remove-history-btn {
  background: none;
  border: none;
  color: var(--gray-400, #9ca3af);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm, 4px);
  transition: all var(--transition-fast, 0.15s ease);
  flex-shrink: 0;
}

.remove-history-btn:hover {
  background: var(--red-50, #fef2f2);
  color: var(--red-500, #ef4444);
}

/* 侧边栏切换按钮 */
.sidebar-toggle-btn {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 48px;
  height: 48px;
  background: var(--primary-600, #4f46e5);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  transition: all var(--transition-fast, 0.15s ease);
  z-index: 999;
}

.sidebar-toggle-btn:hover {
  background: var(--primary-700, #4338ca);
  transform: translateY(-50%) scale(1.05);
}

/* 动画效果 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all var(--transition-normal, 0.25s ease);
  transform-origin: top;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: scaleY(0.95);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .search-sidebar {
    width: 350px;
    right: -350px;
  }

  .filter-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-group {
    flex-wrap: wrap;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }

  .search-content {
    padding: 16px;
  }

  .search-sidebar {
    width: 100vw;
    right: -100vw;
  }

  .filter-buttons {
    flex-wrap: wrap;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .pagination {
    justify-content: center;
  }

  .pagination-info {
    justify-content: center;
  }

  .result-actions {
    position: static;
    opacity: 1;
    margin-top: 12px;
    justify-content: flex-start;
  }

  .sidebar-toggle-btn {
    bottom: 20px;
    top: auto;
    transform: none;
  }

  .sidebar-toggle-btn:hover {
    transform: scale(1.05);
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .search-results-page:not([data-theme]) {
    --background: #111827;
    --surface: #1f2937;
    --surface-variant: #374151;
    --on-surface: #f9fafb;
    --on-surface-variant: #d1d5db;
    --border-color: #4b5563;
  }
}

/* 打印样式 */
@media print {
  .search-sidebar,
  .sidebar-toggle-btn,
  .filter-actions,
  .result-actions,
  .pagination-container {
    display: none !important;
  }

  .search-results-page {
    background: white !important;
  }

  .result-item {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
</style>
