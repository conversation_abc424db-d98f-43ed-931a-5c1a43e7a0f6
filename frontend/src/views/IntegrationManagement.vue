<template>
  <div class="integration-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="text-2xl font-bold text-gray-900">第三方集成管理</h1>
      <p class="text-gray-600 mt-2">管理与JIRA、SonarQube、Jenkins等第三方工具的集成</p>
    </div>

    <!-- 集成状态卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <!-- JIRA集成状态 -->
      <div class="integration-card">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="integration-icon jira">
              <i class="fab fa-jira"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-lg font-semibold">JIRA</h3>
              <p class="text-sm text-gray-600">缺陷管理系统</p>
            </div>
          </div>
          <div class="status-indicator" :class="getStatusClass(integrationStatus.jira?.connected)">
            <i class="fas fa-circle"></i>
          </div>
        </div>
        
        <div class="integration-details">
          <div class="detail-item">
            <span class="label">配置状态:</span>
            <span class="value" :class="integrationStatus.jira?.configured ? 'text-green-600' : 'text-red-600'">
              {{ integrationStatus.jira?.configured ? '已配置' : '未配置' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">连接状态:</span>
            <span class="value" :class="integrationStatus.jira?.connected ? 'text-green-600' : 'text-red-600'">
              {{ integrationStatus.jira?.connected ? '已连接' : '连接失败' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">服务地址:</span>
            <span class="value text-xs">{{ integrationStatus.jira?.url || '未配置' }}</span>
          </div>
        </div>
      </div>

      <!-- SonarQube集成状态 -->
      <div class="integration-card">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="integration-icon sonarqube">
              <i class="fas fa-code"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-lg font-semibold">SonarQube</h3>
              <p class="text-sm text-gray-600">代码质量分析</p>
            </div>
          </div>
          <div class="status-indicator" :class="getStatusClass(integrationStatus.sonarqube?.connected)">
            <i class="fas fa-circle"></i>
          </div>
        </div>
        
        <div class="integration-details">
          <div class="detail-item">
            <span class="label">配置状态:</span>
            <span class="value" :class="integrationStatus.sonarqube?.configured ? 'text-green-600' : 'text-red-600'">
              {{ integrationStatus.sonarqube?.configured ? '已配置' : '未配置' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">连接状态:</span>
            <span class="value" :class="integrationStatus.sonarqube?.connected ? 'text-green-600' : 'text-red-600'">
              {{ integrationStatus.sonarqube?.connected ? '已连接' : '连接失败' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">服务地址:</span>
            <span class="value text-xs">{{ integrationStatus.sonarqube?.url || '未配置' }}</span>
          </div>
        </div>
      </div>

      <!-- Jenkins集成状态 -->
      <div class="integration-card">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="integration-icon jenkins">
              <i class="fab fa-jenkins"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-lg font-semibold">Jenkins</h3>
              <p class="text-sm text-gray-600">持续集成/部署</p>
            </div>
          </div>
          <div class="status-indicator" :class="getStatusClass(integrationStatus.jenkins?.connected)">
            <i class="fas fa-circle"></i>
          </div>
        </div>
        
        <div class="integration-details">
          <div class="detail-item">
            <span class="label">配置状态:</span>
            <span class="value" :class="integrationStatus.jenkins?.configured ? 'text-green-600' : 'text-red-600'">
              {{ integrationStatus.jenkins?.configured ? '已配置' : '未配置' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">连接状态:</span>
            <span class="value" :class="integrationStatus.jenkins?.connected ? 'text-green-600' : 'text-red-600'">
              {{ integrationStatus.jenkins?.connected ? '已连接' : '连接失败' }}
            </span>
          </div>
          <div class="detail-item">
            <span class="label">服务地址:</span>
            <span class="value text-xs">{{ integrationStatus.jenkins?.url || '未配置' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 同步任务管理 -->
    <div class="sync-tasks-section">
      <div class="section-header">
        <h2 class="text-xl font-semibold text-gray-900">数据同步任务</h2>
        <button 
          @click="refreshTasks"
          class="btn btn-secondary"
          :disabled="loading"
        >
          <i class="fas fa-refresh mr-2"></i>
          刷新
        </button>
      </div>

      <!-- 任务统计 -->
      <div class="task-stats mb-6">
        <div class="stat-item">
          <span class="stat-label">总任务数</span>
          <span class="stat-value">{{ syncTasks.total || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">运行中</span>
          <span class="stat-value text-blue-600">{{ syncTasks.running || 0 }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">已启用</span>
          <span class="stat-value text-green-600">{{ syncTasks.enabled || 0 }}</span>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="task-list">
        <div 
          v-for="task in syncTasks.tasks" 
          :key="task.id"
          class="task-item"
        >
          <div class="task-header">
            <div class="task-info">
              <h3 class="task-name">{{ task.name }}</h3>
              <p class="task-description">{{ task.source }} → {{ task.target }}</p>
            </div>
            <div class="task-actions">
              <button 
                @click="executeTask(task.id)"
                class="btn btn-primary btn-sm"
                :disabled="task.is_running || loading"
              >
                <i class="fas fa-play mr-1"></i>
                {{ task.is_running ? '运行中' : '执行' }}
              </button>
            </div>
          </div>
          
          <div class="task-details">
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">状态:</span>
                <span class="value">
                  <span class="status-badge" :class="getTaskStatusClass(task.status)">
                    {{ getTaskStatusText(task.status) }}
                  </span>
                </span>
              </div>
              <div class="detail-item">
                <span class="label">上次同步:</span>
                <span class="value">{{ formatDateTime(task.last_sync) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">下次同步:</span>
                <span class="value">{{ formatDateTime(task.next_sync) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">同步间隔:</span>
                <span class="value">{{ formatInterval(task.schedule_interval) }}</span>
              </div>
            </div>
            
            <div v-if="task.error_message" class="error-message">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              {{ task.error_message }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 同步历史 -->
    <div class="sync-history-section">
      <div class="section-header">
        <h2 class="text-xl font-semibold text-gray-900">同步历史</h2>
        <button 
          @click="refreshHistory"
          class="btn btn-secondary"
          :disabled="loading"
        >
          <i class="fas fa-history mr-2"></i>
          刷新历史
        </button>
      </div>

      <div class="history-list">
        <div 
          v-for="record in syncHistory" 
          :key="`${record.task_id}-${record.start_time}`"
          class="history-item"
        >
          <div class="history-header">
            <div class="history-info">
              <span class="task-id">{{ record.task_id }}</span>
              <span class="timestamp">{{ formatDateTime(record.start_time) }}</span>
            </div>
            <span class="status-badge" :class="getTaskStatusClass(record.status)">
              {{ getTaskStatusText(record.status) }}
            </span>
          </div>
          
          <div class="history-details">
            <div class="detail-grid">
              <div class="detail-item">
                <span class="label">执行时长:</span>
                <span class="value">{{ formatDuration(record.duration) }}</span>
              </div>
              <div class="detail-item">
                <span class="label">处理记录:</span>
                <span class="value">{{ record.records_processed || 0 }}</span>
              </div>
              <div class="detail-item">
                <span class="label">成功记录:</span>
                <span class="value text-green-600">{{ record.records_success || 0 }}</span>
              </div>
              <div class="detail-item">
                <span class="label">失败记录:</span>
                <span class="value text-red-600">{{ record.records_failed || 0 }}</span>
              </div>
            </div>
            
            <div v-if="record.error_message" class="error-message">
              <i class="fas fa-exclamation-triangle mr-2"></i>
              {{ record.error_message }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useNotification } from '@/composables/useNotification'

const { showNotification } = useNotification()

// 响应式数据
const loading = ref(false)
const integrationStatus = ref({})
const syncTasks = ref({
  tasks: [],
  total: 0,
  running: 0,
  enabled: 0
})
const syncHistory = ref([])

// 获取集成状态
const fetchIntegrationStatus = async () => {
  try {
    const response = await fetch('/api/integrations/status')
    const result = await response.json()
    
    if (result.success) {
      integrationStatus.value = result.data
    }
  } catch (error) {
    console.error('获取集成状态失败:', error)
    showNotification('获取集成状态失败', 'error')
  }
}

// 获取同步任务
const fetchSyncTasks = async () => {
  try {
    const response = await fetch('/api/sync/tasks')
    const result = await response.json()
    
    if (result.success) {
      syncTasks.value = result.data
    }
  } catch (error) {
    console.error('获取同步任务失败:', error)
    showNotification('获取同步任务失败', 'error')
  }
}

// 获取同步历史
const fetchSyncHistory = async () => {
  try {
    const response = await fetch('/api/sync/history?limit=20')
    const result = await response.json()
    
    if (result.success) {
      syncHistory.value = result.data.history
    }
  } catch (error) {
    console.error('获取同步历史失败:', error)
    showNotification('获取同步历史失败', 'error')
  }
}

// 执行同步任务
const executeTask = async (taskId) => {
  try {
    loading.value = true
    
    const response = await fetch(`/api/sync/tasks/${taskId}/execute`, {
      method: 'POST'
    })
    const result = await response.json()
    
    if (result.success) {
      showNotification('同步任务执行成功', 'success')
      await refreshTasks()
      await refreshHistory()
    } else {
      showNotification('同步任务执行失败', 'error')
    }
  } catch (error) {
    console.error('执行同步任务失败:', error)
    showNotification('执行同步任务失败', 'error')
  } finally {
    loading.value = false
  }
}

// 刷新任务
const refreshTasks = async () => {
  loading.value = true
  await fetchSyncTasks()
  loading.value = false
}

// 刷新历史
const refreshHistory = async () => {
  loading.value = true
  await fetchSyncHistory()
  loading.value = false
}

// 工具函数
const getStatusClass = (connected) => {
  return connected ? 'text-green-500' : 'text-red-500'
}

const getTaskStatusClass = (status) => {
  const statusClasses = {
    'pending': 'bg-gray-100 text-gray-800',
    'running': 'bg-blue-100 text-blue-800',
    'success': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'cancelled': 'bg-yellow-100 text-yellow-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

const getTaskStatusText = (status) => {
  const statusTexts = {
    'pending': '等待中',
    'running': '运行中',
    'success': '成功',
    'failed': '失败',
    'cancelled': '已取消'
  }
  return statusTexts[status] || '未知'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  } catch {
    return '无效日期'
  }
}

const formatInterval = (seconds) => {
  if (!seconds) return '未设置'
  
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else {
    return `${minutes}分钟`
  }
}

const formatDuration = (seconds) => {
  if (!seconds) return '0秒'
  
  if (seconds < 60) {
    return `${seconds.toFixed(1)}秒`
  } else {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}分${remainingSeconds}秒`
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  loading.value = true
  
  await Promise.all([
    fetchIntegrationStatus(),
    fetchSyncTasks(),
    fetchSyncHistory()
  ])
  
  loading.value = false
})
</script>

<style scoped>
.integration-management {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.integration-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.integration-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.integration-icon.jira {
  background: #0052cc;
}

.integration-icon.sonarqube {
  background: #4e9bcd;
}

.integration-icon.jenkins {
  background: #d33833;
}

.status-indicator {
  font-size: 12px;
}

.integration-details {
  margin-top: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-item .label {
  font-weight: 500;
  color: #6b7280;
}

.detail-item .value {
  font-weight: 600;
}

.sync-tasks-section,
.sync-history-section {
  margin-top: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.task-stats {
  display: flex;
  gap: 24px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
}

.task-item,
.history-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.task-header,
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.task-description {
  font-size: 14px;
  color: #6b7280;
}

.task-details,
.history-details {
  margin-top: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  padding: 12px;
  color: #dc2626;
  font-size: 14px;
}

.task-id {
  font-weight: 600;
  color: #1f2937;
  margin-right: 16px;
}

.timestamp {
  color: #6b7280;
  font-size: 14px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #4338ca;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}
</style>
