<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
        <h1 class="text-4xl md:text-5xl font-bold mb-6 fade-in">
          接口自动化驱动的全年度质量提升方案
        </h1>
        <p class="text-xl md:text-2xl mb-8 fade-in">
          通过接口自动化倒逼测试规范化，提升团队质量意识，实现质量可视化
        </p>
        <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 fade-in">
          <router-link to="/dashboard" class="btn btn-primary">
            <i class="fas fa-chart-pie mr-2"></i>
            查看质量大盘
          </router-link>
          <router-link to="/automation" class="btn btn-secondary">
            <i class="fas fa-robot mr-2"></i>
            自动化测试看板
          </router-link>
        </div>
      </div>
    </section>

    <!-- 特性介绍 -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">核心功能</h2>
          <p class="text-xl text-gray-600">全方位质量监控，助力团队持续改进</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="feature in features"
            :key="feature.title"
            class="text-center p-6 rounded-lg hover:shadow-lg transition-shadow"
          >
            <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full"
                 :class="feature.bgColor">
              <i :class="[feature.icon, feature.iconColor, 'text-2xl']"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ feature.title }}</h3>
            <p class="text-gray-600">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">质量成果</h2>
          <p class="text-xl text-gray-600">数据驱动的质量提升成效</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="stat in stats"
            :key="stat.label"
            class="text-center p-6 bg-white rounded-lg shadow"
          >
            <div class="text-3xl font-bold mb-2" :class="stat.color">{{ stat.value }}</div>
            <div class="text-gray-600">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速导航 -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">快速导航</h2>
          <p class="text-xl text-gray-600">选择您需要查看的质量数据</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <router-link
            v-for="nav in navigationCards"
            :key="nav.name"
            :to="nav.path"
            class="block p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group"
          >
            <div class="flex items-center mb-4">
              <i :class="[nav.icon, nav.color, 'text-2xl', 'mr-3']"></i>
              <h3 class="text-lg font-semibold text-gray-900 group-hover:text-primary-600">
                {{ nav.title }}
              </h3>
            </div>
            <p class="text-gray-600">{{ nav.description }}</p>
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref } from 'vue'

export default {
  name: 'Home',
  setup() {
    const features = ref([
      {
        title: '质量大盘',
        description: '全面展示质量指标，多维度数据分析',
        icon: 'fas fa-chart-pie',
        iconColor: 'text-primary-600',
        bgColor: 'bg-primary-100',
      },
      {
        title: '自动化测试',
        description: '接口自动化覆盖率，执行结果监控',
        icon: 'fas fa-robot',
        iconColor: 'text-success-600',
        bgColor: 'bg-success-100',
      },
      {
        title: '性能监控',
        description: '系统性能指标，响应时间分析',
        icon: 'fas fa-tachometer-alt',
        iconColor: 'text-warning-600',
        bgColor: 'bg-warning-100',
      },
      {
        title: '质量门禁',
        description: '质量门禁规则，通过率统计',
        icon: 'fas fa-shield-alt',
        iconColor: 'text-danger-600',
        bgColor: 'bg-danger-100',
      },
    ])
    
    const stats = ref([
      {
        value: '78%',
        label: '接口自动化覆盖率',
        color: 'text-primary-600',
      },
      {
        value: '92%',
        label: '质量门禁通过率',
        color: 'text-success-600',
      },
      {
        value: '125ms',
        label: '平均响应时间',
        color: 'text-warning-600',
      },
      {
        value: '42%',
        label: '自动化效能提升',
        color: 'text-danger-600',
      },
    ])
    
    const navigationCards = ref([
      {
        name: 'Dashboard',
        title: '质量大盘',
        description: '查看整体质量指标和趋势分析',
        path: '/dashboard',
        icon: 'fas fa-chart-pie',
        color: 'text-primary-600',
      },
      {
        name: 'Automation',
        title: '自动化测试',
        description: '监控自动化测试覆盖率和执行情况',
        path: '/automation',
        icon: 'fas fa-robot',
        color: 'text-success-600',
      },
      {
        name: 'Performance',
        title: '性能监控',
        description: '实时监控系统性能和响应时间',
        path: '/performance',
        icon: 'fas fa-tachometer-alt',
        color: 'text-warning-600',
      },
      {
        name: 'QualityGate',
        title: '质量门禁',
        description: '管理质量门禁规则和查看执行结果',
        path: '/quality-gate',
        icon: 'fas fa-shield-alt',
        color: 'text-danger-600',
      },
    ])
    
    return {
      features,
      stats,
      navigationCards,
    }
  },
}
</script>

<style scoped>
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 6rem 0;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.fade-in {
  animation: fadeIn 1s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
