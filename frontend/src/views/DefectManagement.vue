<template>
  <div class="defect-management p-6 space-y-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">缺陷管理</h1>
        <p class="mt-1 text-sm text-gray-500">
          缺陷跟踪、趋势分析和质量监控
        </p>
      </div>
      <div class="flex space-x-3">
        <button
          @click="refreshData"
          :disabled="isRefreshing"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <svg v-if="!isRefreshing" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <svg v-else class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isRefreshing ? '刷新中...' : '刷新数据' }}
        </button>
        <button
          @click="exportData"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          导出数据
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <MetricCard
        title="总缺陷数"
        :value="totalDefects.toString()"
        :progress="Math.min((totalDefects / 100) * 100, 100)"
        target="目标: < 100"
        change="+5%"
        change-type="neutral"
        status="primary"
      />
      <MetricCard
        title="未解决缺陷"
        :value="openDefects.toString()"
        :progress="Math.min((openDefects / 50) * 100, 100)"
        target="目标: < 50"
        change="-2%"
        change-type="positive"
        status="danger"
      />
      <MetricCard
        title="已解决缺陷"
        :value="resolvedDefects.toString()"
        :progress="totalDefects > 0 ? (resolvedDefects / totalDefects) * 100 : 0"
        target="目标: > 80%"
        change="+8%"
        change-type="positive"
        status="success"
      />
      <MetricCard
        title="严重缺陷"
        :value="criticalDefects.toString()"
        :progress="Math.min((criticalDefects / 10) * 100, 100)"
        target="目标: < 10"
        change="-1"
        change-type="positive"
        status="warning"
      />
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 缺陷趋势图表 -->
      <DefectTrendChart
        ref="trendChartRef"
        :project-id="selectedProjectId"
        @data-updated="handleTrendDataUpdated"
      />
      
      <!-- 缺陷分布图表 -->
      <DefectDistributionChart
        ref="distributionChartRef"
        :project-id="selectedProjectId"
        @data-updated="handleDistributionDataUpdated"
      />
    </div>

    <!-- 筛选面板 -->
    <FilterPanel
      :filters="filterConfig"
      :loading="loading.list"
      @filter-change="handleFilterChange"
      @reset="handleFilterReset"
    />

    <!-- 缺陷列表 -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">缺陷列表</h3>
      </div>
      
      <DataTable
        :data="defects"
        :columns="defectColumns"
        :loading="loading.list"
        :pagination="pagination"
        @sort="handleSort"
        @page-change="handlePageChange"
        @row-click="handleRowClick"
      />
    </div>

    <!-- 缺陷详情模态框 -->
    <DefectDetailModal
      v-if="showDetailModal"
      :defect-id="selectedDefectId"
      @close="closeDetailModal"
      @updated="handleDefectUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useDefectStore } from '@/stores/defect'
import MetricCard from '@/components/common/MetricCard.vue'
import FilterPanel from '@/components/common/FilterPanel.vue'
import DataTable from '@/components/common/DataTable.vue'
import DefectTrendChart from '@/components/charts/DefectTrendChart.vue'
import DefectDistributionChart from '@/components/charts/DefectDistributionChart.vue'
// import DefectDetailModal from '@/components/defect/DefectDetailModal.vue'

// 使用缺陷store
const defectStore = useDefectStore()

// 响应式数据
const isRefreshing = ref(false)
const selectedProjectId = ref(null)
const showDetailModal = ref(false)
const selectedDefectId = ref(null)
const trendChartRef = ref(null)
const distributionChartRef = ref(null)

// 计算属性
const defects = computed(() => defectStore.defects)
const loading = computed(() => defectStore.loading)
const totalDefects = computed(() => defectStore.totalDefects)
const openDefects = computed(() => defectStore.openDefects)
const resolvedDefects = computed(() => defectStore.resolvedDefects)
const criticalDefects = computed(() => defectStore.criticalDefects)

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 筛选配置
const filterConfig = [
  {
    key: 'severity',
    label: '严重程度',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '严重', value: 'critical' },
      { label: '高', value: 'high' },
      { label: '中', value: 'medium' },
      { label: '低', value: 'low' }
    ]
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '打开', value: 'open' },
      { label: '处理中', value: 'in_progress' },
      { label: '已解决', value: 'resolved' },
      { label: '已关闭', value: 'closed' },
      { label: '重新打开', value: 'reopened' }
    ]
  },
  {
    key: 'priority',
    label: '优先级',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '紧急', value: 'urgent' },
      { label: '高', value: 'high' },
      { label: '中', value: 'medium' },
      { label: '低', value: 'low' }
    ]
  },
  {
    key: 'dateRange',
    label: '日期范围',
    type: 'select',
    options: [
      { label: '最近7天', value: '7d' },
      { label: '最近30天', value: '30d' },
      { label: '最近90天', value: '90d' },
      { label: '最近1年', value: '1y' }
    ]
  }
]

// 表格列配置
const defectColumns = [
  {
    key: 'id',
    title: 'ID',
    width: 80,
    sortable: true
  },
  {
    key: 'title',
    title: '标题',
    width: 300,
    sortable: true
  },
  {
    key: 'severity',
    title: '严重程度',
    width: 120,
    sortable: true,
    render: (value) => {
      const severityMap = {
        critical: { text: '严重', class: 'bg-red-100 text-red-800' },
        high: { text: '高', class: 'bg-orange-100 text-orange-800' },
        medium: { text: '中', class: 'bg-yellow-100 text-yellow-800' },
        low: { text: '低', class: 'bg-green-100 text-green-800' }
      }
      const config = severityMap[value] || { text: value, class: 'bg-gray-100 text-gray-800' }
      return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}">${config.text}</span>`
    }
  },
  {
    key: 'status',
    title: '状态',
    width: 120,
    sortable: true,
    render: (value) => {
      const statusMap = {
        open: { text: '打开', class: 'bg-red-100 text-red-800' },
        in_progress: { text: '处理中', class: 'bg-yellow-100 text-yellow-800' },
        resolved: { text: '已解决', class: 'bg-green-100 text-green-800' },
        closed: { text: '已关闭', class: 'bg-gray-100 text-gray-800' },
        reopened: { text: '重新打开', class: 'bg-purple-100 text-purple-800' }
      }
      const config = statusMap[value] || { text: value, class: 'bg-gray-100 text-gray-800' }
      return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}">${config.text}</span>`
    }
  },
  {
    key: 'priority',
    title: '优先级',
    width: 100,
    sortable: true
  },
  {
    key: 'assignee_name',
    title: '负责人',
    width: 120
  },
  {
    key: 'created_at',
    title: '创建时间',
    width: 150,
    sortable: true,
    render: (value) => {
      return new Date(value).toLocaleDateString('zh-CN')
    }
  },
  {
    key: 'actions',
    title: '操作',
    width: 100,
    render: () => {
      return '<button class="text-blue-600 hover:text-blue-900">查看</button>'
    }
  }
]

// 方法
const fetchData = async () => {
  try {
    await Promise.all([
      defectStore.fetchDefects(),
      defectStore.fetchStatistics()
    ])
    
    // 更新分页信息
    pagination.total = defectStore.totalDefects
  } catch (error) {
    console.error('获取缺陷数据失败:', error)
  }
}

const refreshData = async () => {
  isRefreshing.value = true
  try {
    await fetchData()
    
    // 刷新图表
    if (trendChartRef.value) {
      await trendChartRef.value.refresh()
    }
    if (distributionChartRef.value) {
      await distributionChartRef.value.refresh()
    }
  } finally {
    isRefreshing.value = false
  }
}

const exportData = () => {
  // TODO: 实现数据导出功能
  console.log('导出缺陷数据')
}

const handleFilterChange = (filters) => {
  defectStore.updateQueryParams(filters)
  fetchData()
}

const handleFilterReset = () => {
  defectStore.updateQueryParams({
    severity: '',
    status: '',
    priority: '',
    dateRange: '30d'
  })
  fetchData()
}

const handleSort = ({ column, direction }) => {
  defectStore.updateQueryParams({
    sortBy: column,
    sortOrder: direction
  })
  fetchData()
}

const handlePageChange = ({ page, pageSize }) => {
  defectStore.updateQueryParams({
    page,
    pageSize
  })
  pagination.current = page
  pagination.pageSize = pageSize
  fetchData()
}

const handleRowClick = (row) => {
  selectedDefectId.value = row.id
  showDetailModal.value = true
}

const closeDetailModal = () => {
  showDetailModal.value = false
  selectedDefectId.value = null
}

const handleDefectUpdated = () => {
  fetchData()
  closeDetailModal()
}

const handleTrendDataUpdated = (data) => {
  console.log('趋势数据更新:', data)
}

const handleDistributionDataUpdated = (data) => {
  console.log('分布数据更新:', data)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.defect-management {
  min-height: calc(100vh - 64px);
}
</style>
