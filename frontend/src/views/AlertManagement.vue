<template>
  <div class="alert-management">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">质量预警系统</h1>
      <p class="mt-2 text-gray-600">
        实时监控质量指标，智能预警异常情况，提升质量管控效率
      </p>
    </div>

    <!-- 预警仪表板 -->
    <AlertDashboard />

    <!-- 预警规则管理 -->
    <div class="mt-8">
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">预警规则管理</h3>
            <button
              @click="showCreateRuleModal = true"
              class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <i class="fas fa-plus mr-2"></i>
              新建规则
            </button>
          </div>
        </div>

        <div class="p-6">
          <!-- 规则筛选 -->
          <div class="flex space-x-4 mb-6">
            <select
              v-model="ruleFilters.project_id"
              @change="fetchRules"
              class="form-select"
            >
              <option value="">所有项目</option>
              <option
                v-for="project in projects"
                :key="project.id"
                :value="project.id"
              >
                {{ project.name }}
              </option>
            </select>

            <select
              v-model="ruleFilters.is_enabled"
              @change="fetchRules"
              class="form-select"
            >
              <option value="">所有状态</option>
              <option :value="true">已启用</option>
              <option :value="false">已禁用</option>
            </select>
          </div>

          <!-- 规则列表 -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    规则名称
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    项目
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    阈值
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="rule in rules" :key="rule.id">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">
                        {{ rule.name }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ rule.description }}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="getAlertTypeClass(rule.alert_type)">
                      {{ getAlertTypeText(rule.alert_type) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ rule.project?.name || '全局' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {{ rule.threshold_operator }} {{ rule.threshold_value }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                          :class="rule.is_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                      {{ rule.is_enabled ? '已启用' : '已禁用' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <button
                        @click="editRule(rule)"
                        class="text-blue-600 hover:text-blue-900"
                      >
                        编辑
                      </button>
                      <button
                        @click="toggleRule(rule)"
                        :class="rule.is_enabled ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                      >
                        {{ rule.is_enabled ? '禁用' : '启用' }}
                      </button>
                      <button
                        @click="deleteRule(rule.id)"
                        class="text-red-600 hover:text-red-900"
                      >
                        删除
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <div class="mt-6 flex justify-between items-center">
            <div class="text-sm text-gray-700">
              显示 {{ (rulePagination.page - 1) * rulePagination.page_size + 1 }} 到 
              {{ Math.min(rulePagination.page * rulePagination.page_size, rulePagination.total) }} 
              条，共 {{ rulePagination.total }} 条
            </div>
            <div class="flex space-x-2">
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="changeRulePage(page)"
                class="px-3 py-1 rounded"
                :class="page === rulePagination.page 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
              >
                {{ page }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建规则模态框 -->
    <AlertRuleModal
      v-if="showCreateRuleModal"
      :rule="editingRule"
      @close="closeRuleModal"
      @saved="handleRuleSaved"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAlertStore } from '@/stores/alert'
import { useProjectStore } from '@/stores/project'
import { useNotificationStore } from '@/stores/notification'
import AlertDashboard from '@/components/alerts/AlertDashboard.vue'
import AlertRuleModal from '@/components/alerts/AlertRuleModal.vue'

const alertStore = useAlertStore()
const projectStore = useProjectStore()
const notificationStore = useNotificationStore()

// 响应式数据
const showCreateRuleModal = ref(false)
const editingRule = ref(null)

const ruleFilters = ref({
  project_id: '',
  is_enabled: ''
})

const rulePagination = ref({
  page: 1,
  page_size: 20,
  total: 0,
  total_pages: 0
})

// 计算属性
const projects = computed(() => projectStore.projects)
const rules = computed(() => alertStore.rules)

const visiblePages = computed(() => {
  const total = rulePagination.value.total_pages
  const current = rulePagination.value.page
  const pages = []
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const fetchRules = async () => {
  try {
    const params = {
      page: rulePagination.value.page,
      page_size: rulePagination.value.page_size,
      ...ruleFilters.value
    }
    
    const result = await alertStore.fetchRules(params)
    rulePagination.value = {
      page: result.page,
      page_size: result.page_size,
      total: result.total,
      total_pages: result.total_pages
    }
  } catch (error) {
    notificationStore.showError('获取预警规则失败')
  }
}

const editRule = (rule) => {
  editingRule.value = rule
  showCreateRuleModal.value = true
}

const closeRuleModal = () => {
  showCreateRuleModal.value = false
  editingRule.value = null
}

const handleRuleSaved = () => {
  closeRuleModal()
  fetchRules()
  notificationStore.showSuccess('预警规则保存成功')
}

const toggleRule = async (rule) => {
  try {
    await alertStore.updateRule(rule.id, { is_enabled: !rule.is_enabled })
    notificationStore.showSuccess(`规则已${rule.is_enabled ? '禁用' : '启用'}`)
    fetchRules()
  } catch (error) {
    notificationStore.showError('更新规则状态失败')
  }
}

const deleteRule = async (ruleId) => {
  if (!confirm('确定要删除这个预警规则吗？')) return
  
  try {
    await alertStore.deleteRule(ruleId)
    notificationStore.showSuccess('预警规则删除成功')
    fetchRules()
  } catch (error) {
    notificationStore.showError('删除预警规则失败')
  }
}

const changeRulePage = (page) => {
  rulePagination.value.page = page
  fetchRules()
}

const getAlertTypeText = (type) => {
  const types = {
    'defect_spike': '缺陷激增',
    'coverage_drop': '覆盖率下降',
    'quality_decline': '质量下降'
  }
  return types[type] || type
}

const getAlertTypeClass = (type) => {
  const classes = {
    'defect_spike': 'bg-red-100 text-red-800',
    'coverage_drop': 'bg-yellow-100 text-yellow-800',
    'quality_decline': 'bg-orange-100 text-orange-800'
  }
  return classes[type] || 'bg-gray-100 text-gray-800'
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    projectStore.fetchProjects(),
    fetchRules()
  ])
})
</script>

<style scoped>
.alert-management {
  padding: 24px;
}

.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}
</style>
