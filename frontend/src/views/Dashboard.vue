<template>
  <div class="min-h-screen bg-gray-50 flex"> <!-- 修改: 添加 flex 类 -->
    <!-- 主要内容区域 -->
    <div class="flex-grow overflow-y-auto"> <!-- 新增: 包裹主要内容并允许滚动 -->
      <!-- 页面标题 -->
      <div class="bg-primary-700 text-white py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex items-center">
            <i class="fas fa-chart-pie text-3xl mr-4"></i>
            <div>
              <h1 class="text-3xl font-bold">质量大盘</h1>
              <p class="mt-1 text-primary-200">
                全面展示质量指标，包括覆盖率、通过率、效能分析等多维度数据
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 指标选择与操作按钮 -->
        <div class="mb-6 flex justify-between items-center">
          <h2 class="text-xl font-semibold text-gray-700">关注的指标</h2>
          <button
            @click="showMetricSelectionModal = true"
            class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition duration-150 ease-in-out"
          >
            <i class="fas fa-cog mr-2"></i>选择关注指标
          </button>
        </div>

        <!-- 核心指标卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <MetricCard
            v-for="metric in displayedMetricCards"
            :key="metric.title"
            :title="metric.title"
            :value="metric.value"
            :progress="metric.progress"
            :target="metric.target"
            :change="metric.change"
            :change-type="metric.change_type"
            :status="getMetricStatus(metric)"
          />
          <div v-if="displayedMetricCards.length === 0 && !loading.overview" class="col-span-full text-center py-8 text-gray-500">
            <p>没有选择关注的指标，或没有可显示的指标。</p>
            <p>请点击 "选择关注指标" 按钮进行配置。</p>
          </div>
        </div>

        <!-- 质量趋势分析 -->
        <div class="bg-white rounded-lg shadow mb-8">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">
              <i class="fas fa-chart-line text-primary-500 mr-2"></i>
              质量趋势分析
            </h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- 自动化覆盖率趋势 -->
              <div>
                <h3 class="text-lg font-medium text-gray-700 mb-4">自动化覆盖率趋势</h3>
                <div class="chart-container">
                  <LineChart
                    v-if="coverageTrendData"
                    :data="coverageTrendData"
                    :options="chartOptions"
                  />
                </div>
              </div>

              <!-- 质量门禁通过率趋势 -->
              <div>
                <h3 class="text-lg font-medium text-gray-700 mb-4">质量门禁通过率趋势</h3>
                <div class="chart-container">
                  <LineChart
                    v-if="gateTrendData"
                    :data="gateTrendData"
                    :options="chartOptions"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 筛选面板 -->
        <div v-if="showFilters" class="mb-8">
          <FilterPanel
            :show-date-range="true"
            :show-project="true"
            :show-team="true"
            :projects="dashboardStore.projects"
            :teams="dashboardStore.teams"
            @filter-change="handleFilterChange"
            @filter-apply="handleFilterApply"
          />
        </div>

        <!-- 团队质量对比 -->
        <DataTable
          title="团队质量对比"
          :data="teamData"
          :columns="teamColumns"
          :loading="loading.teams"
          :searchable="true"
          :paginated="true"
          :exportable="true"
          @sort="handleTeamSort"
          @export="handleTeamExport"
        >
          <!-- 自定义单元格渲染 -->
          <template #cell-interface_coverage="{ value, item }">
            <div class="flex items-center">
              <span class="mr-2">{{ value }}%</span>
              <span class="badge" :class="getBadgeClass(value)">
                {{ getGrade(value) }}
              </span>
            </div>
          </template>

          <template #cell-main_path_coverage="{ value, item }">
            <div class="flex items-center">
              <span class="mr-2">{{ value }}%</span>
              <span class="badge" :class="getBadgeClass(value)">
                {{ getGrade(value) }}
              </span>
            </div>
          </template>

          <template #cell-quality_gate_pass_rate="{ value, item }">
            <div class="flex items-center">
              <span class="mr-2">{{ value }}%</span>
              <span class="badge" :class="getBadgeClass(value)">
                {{ getGrade(value) }}
              </span>
            </div>
          </template>

          <template #cell-quality_score="{ value, item }">
            <span class="font-bold" :class="getScoreColor(value)">
              {{ value }}
            </span>
          </template>
        </DataTable>
      </div>
    </div>

    <!-- 快速访问面板 -->
    <!-- <div class="w-80 flex-shrink-0 bg-white shadow-lg border-l border-gray-200 h-screen overflow-y-auto">
      <QuickAccessPanel />
    </div> -->

    <!-- 添加模态框的模板 -->
    <teleport to="body">
      <div v-if="showMetricSelectionModal" class="modal-overlay" @click.self="showMetricSelectionModal = false">
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title">选择关注的指标</h3>
            <button @click="showMetricSelectionModal = false" class="modal-close-btn">&times;</button>
          </div>
          <div class="metric-options">
            <div v-for="card in allMetricCards" :key="card.title" class="metric-option">
              <input
                type="checkbox"
                :id="'metric-' + card.title.replace(/\s+/g, '-')"
                :value="card.title"
                v-model="selectedMetrics"
              />
              <label :for="'metric-' + card.title.replace(/\s+/g, '-')">{{ card.title }}</label>
            </div>
            <div v-if="allMetricCards.length === 0" class="text-gray-500">
              暂无可用指标。
            </div>
          </div>
          <div class="modal-actions">
            <button
              @click="showMetricSelectionModal = false"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
            >
              取消
            </button>
            <button
              @click="handleSaveFocusedMetrics"
              class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              保存选择
            </button>
          </div>
        </div>
      </div>
    </teleport>

  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import { useAppStore } from '@/stores/app'
import MetricCard from '@/components/common/MetricCard.vue'
import LineChart from '@/components/charts/LineChart.vue'
import DataTable from '@/components/common/DataTable.vue'
import FilterPanel from '@/components/common/FilterPanel.vue'
import QuickAccessPanel from '@/components/dashboard/QuickAccessPanel.vue' // 新增: 导入快速访问面板
// 考虑创建一个简单的模态框组件或使用现有UI库的模态框
// import ConfigModal from '@/components/dashboard/ConfigModal.vue' // 假设有这样一个组件

// 使用stores
const dashboardStore = useDashboardStore()
const appStore = useAppStore()

// 响应式数据
const showFilters = ref(false)
const refreshInterval = ref(null)
const showMetricSelectionModal = ref(false)
const selectedMetrics = ref([]) // 用于模态框中的选择

// 计算属性
const allMetricCards = computed(() => dashboardStore.metricCards) // 所有从后端获取的指标卡片
const focusedMetricIds = computed(() => dashboardStore.focusedMetricIds)

const displayedMetricCards = computed(() => {
  if (focusedMetricIds.value && focusedMetricIds.value.length > 0) {
    return allMetricCards.value.filter(card => focusedMetricIds.value.includes(card.title)); // 假设 card.title 是唯一标识符
  }
  return allMetricCards.value; // 如果没有设置关注指标，则显示全部
})

const coverageTrendData = computed(() => dashboardStore.trendsData?.coverage_trend)
const gateTrendData = computed(() => dashboardStore.trendsData?.gate_trend)
const teamData = computed(() => dashboardStore.teamComparison)
const loading = computed(() => dashboardStore.loading)

// 团队对比表格配置
const teamColumns = [
  { key: 'team_name', title: '团队', sortable: true },
  {
    key: 'interface_coverage',
    title: '接口覆盖率',
    sortable: true,
    formatter: (value) => `${value}%`
  },
  {
    key: 'main_path_coverage',
    title: '主链路覆盖率',
    sortable: true,
    formatter: (value) => `${value}%`
  },
  {
    key: 'quality_gate_pass_rate',
    title: '质量门禁通过率',
    sortable: true,
    formatter: (value) => `${value}%`
  },
  { key: 'avg_response_time', title: '平均响应时间', sortable: true },
  { key: 'quality_score', title: '质量评分', sortable: true }
]

// 图表配置
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
    },
    tooltip: {
      mode: 'index',
      intersect: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      ticks: {
        callback: function(value) {
          return value + '%'
        },
      },
    },
  },
}
// 方法
const loadDashboardData = async () => {
  try {
    appStore.setLoading(true)
    // 加载用户布局配置，这会获取 focusedMetricIds
    // await dashboardStore.loadUserLayout(dashboardStore.currentUserId)
    await dashboardStore.refreshAll() // 加载其他仪表盘数据，包括 allMetricCards
    // 初始化 selectedMetrics，用于模态框
    selectedMetrics.value = [...(focusedMetricIds.value || [])];
    appStore.showSuccess('数据加载成功')
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    appStore.showError('数据加载失败，请稍后重试')
  } finally {
    appStore.setLoading(false)
  }
}

const handleSaveFocusedMetrics = async () => {
  await dashboardStore.setFocusedMetricIds(selectedMetrics.value)
  // 触发保存整个布局/配置，因为 focused_metric_ids 是 DashboardConfig 的一部分
  try {
    await dashboardStore.saveLayout(dashboardStore.userLayout) // userLayout 可能也需要更新或传递
    appStore.showSuccess('关注指标已保存')
  } catch (error) {
    appStore.showError('保存关注指标失败')
  }
  showMetricSelectionModal.value = false
}

const getMetricStatus = (metric) => {
  if (metric.progress >= 90) return 'success'
  if (metric.progress >= 70) return 'primary'
  if (metric.progress >= 50) return 'warning'
  return 'danger'
}

const getBadgeClass = (value) => {
  if (value >= 90) return 'badge-success'
  if (value >= 80) return 'badge-primary'
  if (value >= 70) return 'badge-warning'
  return 'badge-danger'
}

const getGrade = (value) => {
  if (value >= 90) return 'S'
  if (value >= 80) return 'A'
  if (value >= 70) return 'B'
  return 'C'
}

const getScoreColor = (score) => {
  if (score === 'A') return 'text-success-600'
  if (score === 'B') return 'text-primary-600'
  if (score === 'C') return 'text-warning-600'
  return 'text-danger-600'
}

// 筛选处理
const handleFilterChange = (filters) => {
  dashboardStore.updateQueryParams(filters)
}

const handleFilterApply = (filters) => {
  dashboardStore.updateQueryParams(filters)
  loadDashboardData()
}

// 表格事件处理
const handleTeamSort = ({ sortBy, sortOrder }) => {
  dashboardStore.updateQueryParams({ sortBy, sortOrder })
  dashboardStore.fetchTeamComparison()
}

const handleTeamExport = (data) => {
  // 导出团队对比数据
  const csv = convertToCSV(data)
  downloadCSV(csv, 'team-comparison.csv')
}

// 工具方法
const convertToCSV = (data) => {
  const headers = teamColumns.map(col => col.title).join(',')
  const rows = data.map(item =>
    teamColumns.map(col => item[col.key]).join(',')
  ).join('\n')
  return `${headers}\n${rows}`
}

const downloadCSV = (csv, filename) => {
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 自动刷新
const startAutoRefresh = () => {
  refreshInterval.value = setInterval(() => {
    loadDashboardData()
  }, 300000) // 5分钟刷新一次
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// 生命周期
onMounted(() => {
  loadDashboardData()
  startAutoRefresh()

  // 监听 allMetricCards 的变化，以便在模态框中正确显示选项
  watch(allMetricCards, (newCards) => {
    if (newCards && newCards.length > 0 && showMetricSelectionModal.value) {
      // 如果需要，可以在这里更新模态框内的选项
    }
  })
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* 简易模态框样式 (实际项目中建议使用成熟的UI库组件) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  width: 90%;
  max-width: 500px;
}
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}
.modal-title {
  font-size: 1.25rem;
  font-weight: bold;
}
.modal-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}
.metric-options {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}
.metric-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}
.metric-option:last-child {
  border-bottom: none;
}
.metric-option input[type="checkbox"] {
  margin-right: 0.75rem;
}
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}
</style>

