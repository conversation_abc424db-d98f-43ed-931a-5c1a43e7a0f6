<template>
  <div class="performance-monitoring">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="text-2xl font-bold text-gray-900">性能监控</h1>
      <p class="text-gray-600 mt-2">实时监控系统性能指标和API响应时间</p>
    </div>

    <!-- 性能概览卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- 系统健康状态 -->
      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon health">
            <i class="fas fa-heartbeat"></i>
          </div>
          <div class="metric-info">
            <h3 class="metric-title">系统健康</h3>
            <p class="metric-value" :class="getHealthStatusClass(performanceData.summary?.system_health)">
              {{ getHealthStatusText(performanceData.summary?.system_health) }}
            </p>
          </div>
        </div>
      </div>

      <!-- 总告警数 -->
      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon alerts">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="metric-info">
            <h3 class="metric-title">告警数量</h3>
            <p class="metric-value">{{ performanceData.summary?.total_alerts || 0 }}</p>
            <p class="metric-subtitle">
              关键: {{ performanceData.summary?.critical_alerts || 0 }}
            </p>
          </div>
        </div>
      </div>

      <!-- API性能 -->
      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon api">
            <i class="fas fa-tachometer-alt"></i>
          </div>
          <div class="metric-info">
            <h3 class="metric-title">API响应时间</h3>
            <p class="metric-value">
              {{ formatResponseTime(performanceData.api_performance?.avg_response_time) }}
            </p>
            <p class="metric-subtitle">
              成功率: {{ formatPercentage(performanceData.api_performance?.success_rate) }}
            </p>
          </div>
        </div>
      </div>

      <!-- 请求吞吐量 -->
      <div class="metric-card">
        <div class="metric-header">
          <div class="metric-icon throughput">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="metric-info">
            <h3 class="metric-title">请求吞吐量</h3>
            <p class="metric-value">
              {{ formatThroughput(performanceData.api_performance?.requests_per_hour) }}
            </p>
            <p class="metric-subtitle">每小时请求数</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统资源监控 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- CPU和内存使用率 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">系统资源使用率</h3>
          <button @click="refreshData" class="btn btn-secondary btn-sm">
            <i class="fas fa-refresh mr-1"></i>
            刷新
          </button>
        </div>
        <div class="chart-content">
          <div class="resource-metrics">
            <!-- CPU使用率 -->
            <div class="resource-item">
              <div class="resource-header">
                <span class="resource-label">CPU使用率</span>
                <span class="resource-value">{{ formatPercentage(performanceData.system_metrics?.cpu_usage) }}</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill cpu"
                  :style="{ width: (performanceData.system_metrics?.cpu_usage || 0) + '%' }"
                ></div>
              </div>
            </div>

            <!-- 内存使用率 -->
            <div class="resource-item">
              <div class="resource-header">
                <span class="resource-label">内存使用率</span>
                <span class="resource-value">{{ formatPercentage(performanceData.system_metrics?.memory_usage) }}</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill memory"
                  :style="{ width: (performanceData.system_metrics?.memory_usage || 0) + '%' }"
                ></div>
              </div>
            </div>

            <!-- 磁盘使用率 -->
            <div class="resource-item">
              <div class="resource-header">
                <span class="resource-label">磁盘使用率</span>
                <span class="resource-value">{{ formatPercentage(performanceData.system_metrics?.disk_usage) }}</span>
              </div>
              <div class="progress-bar">
                <div 
                  class="progress-fill disk"
                  :style="{ width: (performanceData.system_metrics?.disk_usage || 0) + '%' }"
                ></div>
              </div>
            </div>

            <!-- 可用内存 -->
            <div class="resource-item">
              <div class="resource-header">
                <span class="resource-label">可用内存</span>
                <span class="resource-value">{{ formatMemory(performanceData.system_metrics?.memory_available) }}</span>
              </div>
            </div>

            <!-- 可用磁盘 -->
            <div class="resource-item">
              <div class="resource-header">
                <span class="resource-label">可用磁盘</span>
                <span class="resource-value">{{ formatMemory(performanceData.system_metrics?.disk_free) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- API端点性能 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">API端点性能</h3>
        </div>
        <div class="chart-content">
          <div class="endpoint-list">
            <div 
              v-for="(stats, endpoint) in performanceData.api_performance?.endpoint_stats" 
              :key="endpoint"
              class="endpoint-item"
            >
              <div class="endpoint-header">
                <span class="endpoint-name">{{ endpoint }}</span>
                <span class="endpoint-count">{{ stats.count }} 次</span>
              </div>
              <div class="endpoint-metrics">
                <div class="endpoint-metric">
                  <span class="metric-label">平均响应时间:</span>
                  <span class="metric-value">{{ formatResponseTime(stats.avg_response_time) }}</span>
                </div>
                <div class="endpoint-metric">
                  <span class="metric-label">错误率:</span>
                  <span class="metric-value" :class="getErrorRateClass(stats.error_rate)">
                    {{ formatPercentage(stats.error_rate) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能告警 -->
    <div class="alerts-section">
      <div class="section-header">
        <h2 class="text-xl font-semibold text-gray-900">性能告警</h2>
        <span class="alert-count">{{ performanceData.alerts?.length || 0 }} 个告警</span>
      </div>

      <div v-if="performanceData.alerts?.length > 0" class="alerts-list">
        <div 
          v-for="alert in performanceData.alerts" 
          :key="`${alert.type}-${alert.metric}-${alert.timestamp}`"
          class="alert-item"
          :class="getAlertSeverityClass(alert.severity)"
        >
          <div class="alert-header">
            <div class="alert-info">
              <i class="fas fa-exclamation-triangle alert-icon"></i>
              <div class="alert-details">
                <h4 class="alert-title">{{ alert.message }}</h4>
                <p class="alert-meta">
                  {{ alert.type }} - {{ alert.metric }} | 
                  {{ formatDateTime(alert.timestamp) }}
                </p>
              </div>
            </div>
            <span class="alert-severity">{{ getSeverityText(alert.severity) }}</span>
          </div>
          <div class="alert-values">
            <span class="current-value">当前值: {{ formatMetricValue(alert.value, alert.metric) }}</span>
            <span class="threshold-value">阈值: {{ formatMetricValue(alert.threshold, alert.metric) }}</span>
          </div>
        </div>
      </div>

      <div v-else class="no-alerts">
        <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
        <p class="text-gray-600">当前没有性能告警</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useNotification } from '@/composables/useNotification'

const { showNotification } = useNotification()

// 响应式数据
const loading = ref(false)
const performanceData = ref({})
let refreshInterval = null

// 获取性能数据
const fetchPerformanceData = async () => {
  try {
    const response = await fetch('/api/performance/metrics')
    const result = await response.json()
    
    if (result.success) {
      performanceData.value = result.data
    } else {
      showNotification('获取性能数据失败', 'error')
    }
  } catch (error) {
    console.error('获取性能数据失败:', error)
    showNotification('获取性能数据失败', 'error')
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  await fetchPerformanceData()
  loading.value = false
}

// 工具函数
const getHealthStatusClass = (status) => {
  const statusClasses = {
    'good': 'text-green-600',
    'warning': 'text-yellow-600',
    'critical': 'text-red-600'
  }
  return statusClasses[status] || 'text-gray-600'
}

const getHealthStatusText = (status) => {
  const statusTexts = {
    'good': '良好',
    'warning': '警告',
    'critical': '严重'
  }
  return statusTexts[status] || '未知'
}

const formatResponseTime = (time) => {
  if (!time) return '0ms'
  
  if (time < 1) {
    return `${Math.round(time * 1000)}ms`
  } else {
    return `${time.toFixed(2)}s`
  }
}

const formatPercentage = (value) => {
  if (!value) return '0%'
  return `${value.toFixed(1)}%`
}

const formatThroughput = (value) => {
  if (!value) return '0'
  return Math.round(value).toLocaleString()
}

const formatMemory = (value) => {
  if (!value) return '0GB'
  return `${value.toFixed(1)}GB`
}

const getErrorRateClass = (rate) => {
  if (!rate) return 'text-green-600'
  if (rate < 1) return 'text-green-600'
  if (rate < 5) return 'text-yellow-600'
  return 'text-red-600'
}

const getAlertSeverityClass = (severity) => {
  const severityClasses = {
    'warning': 'alert-warning',
    'critical': 'alert-critical'
  }
  return severityClasses[severity] || 'alert-info'
}

const getSeverityText = (severity) => {
  const severityTexts = {
    'warning': '警告',
    'critical': '严重'
  }
  return severityTexts[severity] || '信息'
}

const formatDateTime = (dateString) => {
  if (!dateString) return '未知'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN')
  } catch {
    return '无效日期'
  }
}

const formatMetricValue = (value, metric) => {
  if (!value) return '0'
  
  if (metric.includes('time')) {
    return formatResponseTime(value)
  } else if (metric.includes('rate') || metric.includes('usage')) {
    return formatPercentage(value)
  } else {
    return value.toString()
  }
}

// 组件挂载时获取数据并设置定时刷新
onMounted(async () => {
  await refreshData()
  
  // 每30秒刷新一次数据
  refreshInterval = setInterval(fetchPerformanceData, 30000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
})
</script>

<style scoped>
.performance-monitoring {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.metric-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.metric-header {
  display: flex;
  align-items: center;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 16px;
}

.metric-icon.health {
  background: #10b981;
}

.metric-icon.alerts {
  background: #f59e0b;
}

.metric-icon.api {
  background: #3b82f6;
}

.metric-icon.throughput {
  background: #8b5cf6;
}

.metric-title {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 2px;
}

.metric-subtitle {
  font-size: 12px;
  color: #9ca3af;
}

.chart-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.resource-item {
  margin-bottom: 20px;
}

.resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.resource-label {
  font-weight: 500;
  color: #374151;
}

.resource-value {
  font-weight: 600;
  color: #1f2937;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f3f4f6;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-fill.cpu {
  background: #3b82f6;
}

.progress-fill.memory {
  background: #10b981;
}

.progress-fill.disk {
  background: #f59e0b;
}

.endpoint-item {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 12px;
}

.endpoint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.endpoint-name {
  font-weight: 500;
  color: #1f2937;
  font-size: 14px;
}

.endpoint-count {
  font-size: 12px;
  color: #6b7280;
}

.endpoint-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.endpoint-metric {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.alerts-section {
  margin-top: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.alert-count {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.alert-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border-left: 4px solid #d1d5db;
}

.alert-item.alert-warning {
  border-left-color: #f59e0b;
  background: #fffbeb;
}

.alert-item.alert-critical {
  border-left-color: #ef4444;
  background: #fef2f2;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.alert-info {
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  color: #f59e0b;
  margin-right: 12px;
  margin-top: 2px;
}

.alert-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.alert-meta {
  font-size: 12px;
  color: #6b7280;
}

.alert-severity {
  background: #f59e0b;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.alert-values {
  display: flex;
  gap: 24px;
  font-size: 14px;
}

.current-value {
  color: #ef4444;
  font-weight: 500;
}

.threshold-value {
  color: #6b7280;
}

.no-alerts {
  text-align: center;
  padding: 48px 24px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}
</style>
