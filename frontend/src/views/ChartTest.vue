<template>
  <div class="chart-test-page p-6">
    <h1 class="text-2xl font-bold mb-6">Chart.js 测试页面</h1>
    
    <!-- 测试状态 -->
    <div class="mb-6 p-4 rounded-lg" :class="testStatusClass">
      <h2 class="text-lg font-semibold mb-2">测试状态</h2>
      <p>{{ testStatusMessage }}</p>
    </div>
    
    <!-- 图表测试区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 测试图表1 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium mb-4">测试图表 1 - 基础线图</h3>
        <div class="chart-container">
          <LineChart
            :data="testData1"
            :options="chartOptions1"
          />
        </div>
      </div>
      
      <!-- 测试图表2 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium mb-4">测试图表 2 - 多数据集线图</h3>
        <div class="chart-container">
          <LineChart
            :data="testData2"
            :options="chartOptions2"
          />
        </div>
      </div>
    </div>
    
    <!-- 控制按钮 -->
    <div class="mt-6 flex space-x-4">
      <button
        @click="updateData"
        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
      >
        更新数据
      </button>
      <button
        @click="resetData"
        class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
      >
        重置数据
      </button>
      <button
        @click="testError"
        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
      >
        测试错误处理
      </button>
    </div>
    
    <!-- 日志输出 -->
    <div class="mt-6 bg-gray-100 rounded-lg p-4">
      <h3 class="text-lg font-medium mb-2">控制台日志</h3>
      <div class="text-sm text-gray-700 max-h-40 overflow-y-auto">
        <div v-for="(log, index) in logs" :key="index" class="mb-1">
          <span class="text-gray-500">{{ log.time }}</span>
          <span :class="log.type === 'error' ? 'text-red-600' : 'text-gray-800'">
            {{ log.message }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import LineChart from '@/components/charts/LineChart.vue'

// 测试状态
const testStatus = ref('loading')
const logs = ref([])

// 测试数据
const testData1 = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [{
    label: '测试数据',
    data: [65, 59, 80, 81, 56, 55],
    borderColor: '#4f46e5',
    backgroundColor: 'rgba(79, 70, 229, 0.1)',
    tension: 0.3,
    fill: true
  }]
})

const testData2 = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [
    {
      label: '数据集 1',
      data: [65, 59, 80, 81, 56, 55],
      borderColor: '#4f46e5',
      backgroundColor: 'rgba(79, 70, 229, 0.1)',
      tension: 0.3,
      fill: true
    },
    {
      label: '数据集 2',
      data: [28, 48, 40, 19, 86, 27],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.3,
      fill: true
    }
  ]
})

// 图表配置
const chartOptions1 = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    title: {
      display: true,
      text: '基础线图测试'
    }
  }
}

const chartOptions2 = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    title: {
      display: true,
      text: '多数据集线图测试'
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100
    }
  }
}

// 计算属性
const testStatusClass = computed(() => {
  const statusMap = {
    loading: 'bg-yellow-100 border border-yellow-300 text-yellow-800',
    success: 'bg-green-100 border border-green-300 text-green-800',
    error: 'bg-red-100 border border-red-300 text-red-800'
  }
  return statusMap[testStatus.value]
})

const testStatusMessage = computed(() => {
  const messageMap = {
    loading: '正在测试Chart.js组件...',
    success: 'Chart.js组件工作正常！',
    error: 'Chart.js组件存在问题，请检查控制台日志。'
  }
  return messageMap[testStatus.value]
})

// 方法
const addLog = (message, type = 'info') => {
  logs.value.push({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
}

const updateData = () => {
  addLog('更新图表数据')
  
  // 生成随机数据
  const newData1 = Array.from({ length: 6 }, () => Math.floor(Math.random() * 100))
  const newData2 = Array.from({ length: 6 }, () => Math.floor(Math.random() * 100))
  const newData3 = Array.from({ length: 6 }, () => Math.floor(Math.random() * 100))
  
  testData1.value = {
    ...testData1.value,
    datasets: [{
      ...testData1.value.datasets[0],
      data: newData1
    }]
  }
  
  testData2.value = {
    ...testData2.value,
    datasets: [
      {
        ...testData2.value.datasets[0],
        data: newData2
      },
      {
        ...testData2.value.datasets[1],
        data: newData3
      }
    ]
  }
}

const resetData = () => {
  addLog('重置图表数据')
  
  testData1.value = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '测试数据',
      data: [65, 59, 80, 81, 56, 55],
      borderColor: '#4f46e5',
      backgroundColor: 'rgba(79, 70, 229, 0.1)',
      tension: 0.3,
      fill: true
    }]
  }
  
  testData2.value = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [
      {
        label: '数据集 1',
        data: [65, 59, 80, 81, 56, 55],
        borderColor: '#4f46e5',
        backgroundColor: 'rgba(79, 70, 229, 0.1)',
        tension: 0.3,
        fill: true
      },
      {
        label: '数据集 2',
        data: [28, 48, 40, 19, 86, 27],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.3,
        fill: true
      }
    ]
  }
}

const testError = () => {
  addLog('测试错误处理', 'error')
  
  // 设置无效数据来测试错误处理
  testData1.value = null
}

// 生命周期
onMounted(() => {
  addLog('Chart测试页面已挂载')
  
  // 延迟设置成功状态
  setTimeout(() => {
    testStatus.value = 'success'
    addLog('Chart.js组件测试完成')
  }, 1000)
})
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}
</style>
