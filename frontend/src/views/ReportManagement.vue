<template>
  <div class="report-management">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">数据导出与报告</h1>
      <p class="mt-2 text-gray-600">
        生成质量报告，支持Excel/CSV导出，定时推送和自定义模板
      </p>
    </div>

    <!-- 快速统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <i class="fas fa-file-alt text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">本月报告</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.monthly_reports || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <i class="fas fa-download text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总下载量</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total_downloads || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <i class="fas fa-clock text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">定时任务</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.scheduled_reports || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <i class="fas fa-template text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">报告模板</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.templates || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要功能区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- 报告生成器 -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">
            <i class="fas fa-plus-circle mr-2 text-blue-600"></i>
            生成新报告
          </h3>
        </div>
        <div class="p-6">
          <ReportGenerator />
        </div>
      </div>

      <!-- 报告历史 -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">
              <i class="fas fa-history mr-2 text-green-600"></i>
              报告历史
            </h3>
            <button
              @click="refreshReports"
              class="text-gray-400 hover:text-gray-600"
              :disabled="loading.reports"
            >
              <i class="fas fa-sync-alt" :class="{ 'animate-spin': loading.reports }"></i>
            </button>
          </div>
        </div>
        <div class="p-6">
          <div v-if="loading.reports" class="text-center py-8">
            <i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>
            <p class="mt-2 text-gray-500">加载中...</p>
          </div>
          
          <div v-else-if="reports.length === 0" class="text-center py-8">
            <i class="fas fa-file-alt text-gray-300 text-4xl"></i>
            <p class="mt-2 text-gray-500">暂无报告记录</p>
          </div>
          
          <div v-else class="space-y-4">
            <div
              v-for="report in reports"
              :key="report.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h4 class="font-medium text-gray-900">{{ report.name }}</h4>
                  <p class="text-sm text-gray-500 mt-1">
                    {{ formatDate(report.created_at) }}
                  </p>
                  <div class="flex items-center mt-2 space-x-4">
                    <span class="text-xs px-2 py-1 rounded-full"
                          :class="getStatusClass(report.status)">
                      {{ getStatusText(report.status) }}
                    </span>
                    <span class="text-xs text-gray-500">
                      {{ report.format.toUpperCase() }}
                    </span>
                    <span class="text-xs text-gray-500">
                      {{ formatFileSize(report.file_size) }}
                    </span>
                  </div>
                </div>
                <div class="flex space-x-2 ml-4">
                  <button
                    v-if="report.status === 'completed'"
                    @click="downloadReport(report)"
                    class="text-blue-600 hover:text-blue-900 text-sm"
                  >
                    <i class="fas fa-download mr-1"></i>
                    下载
                  </button>
                  <button
                    @click="deleteReport(report.id)"
                    class="text-red-600 hover:text-red-900 text-sm"
                  >
                    <i class="fas fa-trash mr-1"></i>
                    删除
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pagination.total_pages > 1" class="mt-6 flex justify-center">
            <div class="flex space-x-2">
              <button
                v-for="page in visiblePages"
                :key="page"
                @click="changePage(page)"
                class="px-3 py-1 rounded text-sm"
                :class="page === pagination.page 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
              >
                {{ page }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 定时报告管理 -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">
            <i class="fas fa-calendar-alt mr-2 text-purple-600"></i>
            定时报告
          </h3>
          <button
            @click="showScheduleModal = true"
            class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
          >
            <i class="fas fa-plus mr-2"></i>
            新建定时任务
          </button>
        </div>
      </div>
      <div class="p-6">
        <ScheduledReportList />
      </div>
    </div>

    <!-- 报告模板管理 -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">
            <i class="fas fa-layer-group mr-2 text-orange-600"></i>
            报告模板
          </h3>
          <button
            @click="showTemplateModal = true"
            class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
          >
            <i class="fas fa-plus mr-2"></i>
            新建模板
          </button>
        </div>
      </div>
      <div class="p-6">
        <ReportTemplateList />
      </div>
    </div>

    <!-- 模态框 -->
    <ScheduleReportModal
      v-if="showScheduleModal"
      @close="showScheduleModal = false"
      @saved="handleScheduleSaved"
    />

    <ReportTemplateModal
      v-if="showTemplateModal"
      @close="showTemplateModal = false"
      @saved="handleTemplateSaved"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useReportStore } from '@/stores/report'
import { useNotificationStore } from '@/stores/notification'
import ReportGenerator from '@/components/reports/ReportGenerator.vue'
import ScheduledReportList from '@/components/reports/ScheduledReportList.vue'
import ReportTemplateList from '@/components/reports/ReportTemplateList.vue'
import ScheduleReportModal from '@/components/reports/ScheduleReportModal.vue'
import ReportTemplateModal from '@/components/reports/ReportTemplateModal.vue'

const reportStore = useReportStore()
const notificationStore = useNotificationStore()

// 响应式数据
const showScheduleModal = ref(false)
const showTemplateModal = ref(false)

const loading = ref({
  reports: false
})

const pagination = ref({
  page: 1,
  page_size: 10,
  total: 0,
  total_pages: 0
})

// 计算属性
const stats = computed(() => reportStore.stats)
const reports = computed(() => reportStore.reports)

const visiblePages = computed(() => {
  const total = pagination.value.total_pages
  const current = pagination.value.page
  const pages = []
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const refreshReports = async () => {
  loading.value.reports = true
  try {
    await reportStore.fetchReports({
      page: pagination.value.page,
      page_size: pagination.value.page_size
    })
  } finally {
    loading.value.reports = false
  }
}

const downloadReport = async (report) => {
  try {
    await reportStore.downloadReportFile(report.id)
    notificationStore.showSuccess('报告下载成功')
  } catch (error) {
    notificationStore.showError('下载报告失败')
  }
}

const deleteReport = async (reportId) => {
  if (!confirm('确定要删除这个报告吗？')) return
  
  try {
    await reportStore.deleteReport(reportId)
    notificationStore.showSuccess('报告删除成功')
    refreshReports()
  } catch (error) {
    notificationStore.showError('删除报告失败')
  }
}

const changePage = (page) => {
  pagination.value.page = page
  refreshReports()
}

const handleScheduleSaved = () => {
  showScheduleModal.value = false
  notificationStore.showSuccess('定时报告任务创建成功')
}

const handleTemplateSaved = () => {
  showTemplateModal.value = false
  notificationStore.showSuccess('报告模板保存成功')
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getStatusText = (status) => {
  const statusMap = {
    'pending': '等待中',
    'processing': '生成中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

const getStatusClass = (status) => {
  const classMap = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'processing': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    reportStore.fetchStats(),
    refreshReports()
  ])
})
</script>

<style scoped>
.report-management {
  padding: 24px;
}
</style>
