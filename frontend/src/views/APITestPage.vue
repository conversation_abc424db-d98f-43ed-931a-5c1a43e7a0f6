<template>
  <div class="api-test-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-vial mr-3"></i>
        API集成测试
      </h1>
      <p class="page-subtitle">
        测试前后端API接口的集成情况和参数匹配
      </p>
    </div>

    <!-- 测试控制面板 -->
    <div class="test-controls">
      <div class="control-group">
        <button 
          @click="runAllTests" 
          :disabled="testing"
          class="btn btn-primary"
        >
          <i class="fas fa-play mr-2" :class="{ 'fa-spin fa-spinner': testing }"></i>
          {{ testing ? '测试中...' : '运行所有测试' }}
        </button>
        
        <button 
          @click="clearResults" 
          :disabled="testing"
          class="btn btn-secondary"
        >
          <i class="fas fa-trash mr-2"></i>
          清除结果
        </button>
        
        <button 
          @click="exportResults" 
          :disabled="!hasResults"
          class="btn btn-success"
        >
          <i class="fas fa-download mr-2"></i>
          导出结果
        </button>
      </div>

      <div class="api-info">
        <span class="api-url">
          <i class="fas fa-server mr-2"></i>
          API地址: {{ apiBaseUrl }}
        </span>
        <span class="connection-status" :class="connectionStatus">
          <i :class="connectionIcon"></i>
          {{ connectionText }}
        </span>
      </div>
    </div>

    <!-- 测试进度 -->
    <div v-if="testing" class="test-progress">
      <div class="progress-bar">
        <div 
          class="progress-fill" 
          :style="{ width: `${testProgress}%` }"
        ></div>
      </div>
      <div class="progress-text">
        {{ currentTest }} ({{ completedTests }}/{{ totalTests }})
      </div>
    </div>

    <!-- 测试摘要 -->
    <div v-if="testSummary" class="test-summary">
      <div class="summary-cards">
        <div class="summary-card total">
          <div class="card-icon">
            <i class="fas fa-list"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ testSummary.total }}</div>
            <div class="card-label">总测试数</div>
          </div>
        </div>

        <div class="summary-card passed">
          <div class="card-icon">
            <i class="fas fa-check"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ testSummary.passed }}</div>
            <div class="card-label">通过</div>
          </div>
        </div>

        <div class="summary-card failed">
          <div class="card-icon">
            <i class="fas fa-times"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ testSummary.failed }}</div>
            <div class="card-label">失败</div>
          </div>
        </div>

        <div class="summary-card rate">
          <div class="card-icon">
            <i class="fas fa-percentage"></i>
          </div>
          <div class="card-content">
            <div class="card-value">{{ successRate }}%</div>
            <div class="card-label">成功率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试结果详情 -->
    <div v-if="hasResults" class="test-results">
      <div class="results-header">
        <h3 class="results-title">测试结果详情</h3>
        <div class="results-filters">
          <button 
            @click="filterStatus = 'all'"
            :class="{ 'active': filterStatus === 'all' }"
            class="filter-btn"
          >
            全部
          </button>
          <button 
            @click="filterStatus = 'success'"
            :class="{ 'active': filterStatus === 'success' }"
            class="filter-btn success"
          >
            通过
          </button>
          <button 
            @click="filterStatus = 'error'"
            :class="{ 'active': filterStatus === 'error' }"
            class="filter-btn error"
          >
            失败
          </button>
        </div>
      </div>

      <div class="results-list">
        <div 
          v-for="result in filteredResults"
          :key="result.timestamp"
          class="result-item"
          :class="result.status"
        >
          <div class="result-header">
            <div class="result-status">
              <i :class="result.status === 'success' ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
            </div>
            <div class="result-info">
              <div class="result-test">{{ result.test }}</div>
              <div class="result-details">{{ result.details || result.error }}</div>
            </div>
            <div class="result-time">{{ formatTime(result.timestamp) }}</div>
          </div>
          
          <div v-if="result.testCase" class="result-case">
            <h5 class="case-title">测试用例:</h5>
            <pre class="case-data">{{ JSON.stringify(result.testCase, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!hasResults && !testing" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-flask"></i>
      </div>
      <h3 class="empty-title">还没有测试结果</h3>
      <p class="empty-description">
        点击"运行所有测试"按钮开始测试API接口
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import APITester from '@/utils/apiTester'

// 响应式数据
const testing = ref(false)
const testResults = ref([])
const testSummary = ref(null)
const filterStatus = ref('all')
const currentTest = ref('')
const completedTests = ref(0)
const totalTests = ref(0)
const connectionStatus = ref('unknown')

// API配置
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'

// 计算属性
const hasResults = computed(() => testResults.value.length > 0)

const successRate = computed(() => {
  if (!testSummary.value || testSummary.value.total === 0) return 0
  return Math.round((testSummary.value.passed / testSummary.value.total) * 100)
})

const testProgress = computed(() => {
  if (totalTests.value === 0) return 0
  return Math.round((completedTests.value / totalTests.value) * 100)
})

const filteredResults = computed(() => {
  if (filterStatus.value === 'all') return testResults.value
  return testResults.value.filter(result => result.status === filterStatus.value)
})

const connectionIcon = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return 'fas fa-circle text-green-500'
    case 'disconnected': return 'fas fa-circle text-red-500'
    default: return 'fas fa-circle text-gray-400'
  }
})

const connectionText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'disconnected': return '连接失败'
    default: return '未知'
  }
})

// 方法
const runAllTests = async () => {
  testing.value = true
  testResults.value = []
  testSummary.value = null
  currentTest.value = '初始化测试...'
  completedTests.value = 0
  totalTests.value = 9 // 预估测试数量

  try {
    const tester = new APITester()
    
    // 监听测试进度（如果API测试器支持的话）
    const results = await tester.runAllSearchTests()
    
    testResults.value = results
    const summary = tester.getTestResults().summary
    testSummary.value = summary
    
    currentTest.value = '测试完成'
    completedTests.value = totalTests.value
    
  } catch (error) {
    console.error('测试运行失败:', error)
    currentTest.value = '测试失败'
  } finally {
    testing.value = false
  }
}

const clearResults = () => {
  testResults.value = []
  testSummary.value = null
  currentTest.value = ''
  completedTests.value = 0
  totalTests.value = 0
}

const exportResults = () => {
  const data = {
    summary: testSummary.value,
    results: testResults.value,
    apiBaseUrl: apiBaseUrl,
    exportTime: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `api-test-results-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const checkConnection = async () => {
  try {
    const response = await fetch(`${apiBaseUrl}/api/search/stats`)
    connectionStatus.value = response.ok ? 'connected' : 'disconnected'
  } catch (error) {
    connectionStatus.value = 'disconnected'
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN')
}

// 生命周期
onMounted(() => {
  checkConnection()
})
</script>

<style scoped>
.api-test-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--on-surface, #1f2937);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
}

.page-subtitle {
  font-size: 16px;
  color: var(--on-surface-variant, #6b7280);
  margin: 0;
}

.test-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  margin-bottom: 24px;
}

.control-group {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: var(--radius, 6px);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
  border: none;
  display: flex;
  align-items: center;
}

.btn-primary {
  background: var(--primary-600, #4f46e5);
  color: white;
}

.btn-secondary {
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  border: 1px solid var(--border-color, #e5e7eb);
}

.btn-success {
  background: var(--success-600, #16a34a);
  color: white;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.api-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
}

.test-progress {
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: var(--shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: var(--primary-600, #4f46e5);
  transition: width 0.3s ease;
}

.test-summary {
  margin-bottom: 24px;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.summary-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius, 6px);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.summary-card.total .card-icon {
  background: var(--blue-100, #dbeafe);
  color: var(--blue-600, #2563eb);
}

.summary-card.passed .card-icon {
  background: var(--green-100, #dcfce7);
  color: var(--green-600, #16a34a);
}

.summary-card.failed .card-icon {
  background: var(--red-100, #fee2e2);
  color: var(--red-600, #dc2626);
}

.summary-card.rate .card-icon {
  background: var(--purple-100, #f3e8ff);
  color: var(--purple-600, #9333ea);
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--on-surface, #1f2937);
}

.card-label {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

.test-results {
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.results-filters {
  display: flex;
  gap: 8px;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
}

.filter-btn.active {
  background: var(--primary-600, #4f46e5);
  color: white;
  border-color: var(--primary-600, #4f46e5);
}

.results-list {
  max-height: 600px;
  overflow-y: auto;
}

.result-item {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.result-status {
  font-size: 18px;
  margin-top: 2px;
}

.result-item.success .result-status {
  color: var(--success-600, #16a34a);
}

.result-item.error .result-status {
  color: var(--error-600, #dc2626);
}

.result-info {
  flex: 1;
}

.result-test {
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin-bottom: 4px;
}

.result-details {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

.result-time {
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
}

.result-case {
  margin-top: 12px;
  padding: 12px;
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius, 6px);
}

.case-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.case-data {
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  color: var(--gray-400, #9ca3af);
  margin-bottom: 24px;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0 0 12px 0;
}

.empty-description {
  font-size: 16px;
  color: var(--on-surface-variant, #6b7280);
  margin: 0;
}

@media (max-width: 768px) {
  .test-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .results-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
</style>
