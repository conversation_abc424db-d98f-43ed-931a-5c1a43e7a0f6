<template>
  <div class="coverage-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">测试覆盖率统计</h1>
      <p class="page-description">监控和分析项目的测试覆盖率指标，提升代码质量</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="stats-content">
          <h3 class="stats-value">{{ formatCoverageValue(coverageStats.average_line_coverage) }}</h3>
          <p class="stats-label">平均行覆盖率</p>
          <span class="stats-change positive">
            <i class="fas fa-arrow-up"></i>
            +2.3% 较上月
          </span>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-code-branch"></i>
        </div>
        <div class="stats-content">
          <h3 class="stats-value">{{ formatCoverageValue(coverageStats.average_branch_coverage) }}</h3>
          <p class="stats-label">平均分支覆盖率</p>
          <span class="stats-change positive">
            <i class="fas fa-arrow-up"></i>
            +1.8% 较上月
          </span>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-project-diagram"></i>
        </div>
        <div class="stats-content">
          <h3 class="stats-value">{{ coverageStats.total_projects || 0 }}</h3>
          <p class="stats-label">监控项目数</p>
          <span class="stats-change neutral">
            <i class="fas fa-minus"></i>
            无变化
          </span>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon">
          <i class="fas fa-star"></i>
        </div>
        <div class="stats-content">
          <h3 class="stats-value">{{ coverageStats.excellent_projects || 0 }}</h3>
          <p class="stats-label">优秀项目数</p>
          <span class="stats-change positive">
            <i class="fas fa-arrow-up"></i>
            +2 较上月
          </span>
        </div>
      </div>
    </div>

    <!-- 筛选面板 -->
    <FilterPanel
      :filters="filterConfig"
      @filter-change="handleFilterChange"
      @reset="handleFilterReset"
    />

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <!-- 覆盖率趋势图 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">覆盖率趋势</h3>
            <div class="chart-controls">
              <select v-model="trendParams.date_range" @change="handleTrendParamsChange">
                <option value="7d">最近7天</option>
                <option value="30d">最近30天</option>
                <option value="90d">最近90天</option>
                <option value="1y">最近1年</option>
              </select>
              <select v-model="trendParams.coverage_type" @change="handleTrendParamsChange">
                <option value="line">行覆盖率</option>
                <option value="branch">分支覆盖率</option>
                <option value="function">函数覆盖率</option>
              </select>
            </div>
          </div>
          <CoverageTrendChart
            :data="trendChartData"
            :loading="loading.trends"
            height="300"
          />
        </div>

        <!-- 覆盖率分布图 -->
        <div class="chart-container">
          <div class="chart-header">
            <h3 class="chart-title">覆盖率等级分布</h3>
            <div class="chart-controls">
              <select v-model="distributionDimension" @change="handleDistributionChange">
                <option value="level">按等级分布</option>
                <option value="source">按数据源分布</option>
              </select>
            </div>
          </div>
          <CoverageDistributionChart
            :data="distributionChartData"
            :loading="loading.distribution"
            height="300"
          />
        </div>
      </div>

      <!-- 覆盖率热力图 -->
      <div class="chart-row">
        <div class="chart-container full-width">
          <div class="chart-header">
            <h3 class="chart-title">文件覆盖率热力图</h3>
            <div class="chart-controls">
              <button class="btn btn-secondary" @click="handleHeatmapRefresh">
                <i class="fas fa-sync-alt"></i>
                刷新热力图
              </button>
            </div>
          </div>
          <CoverageHeatmapChart
            :data="fileCoverages"
            :loading="loading.files"
            title="文件覆盖率热力图"
            :grid-columns="8"
            @cell-click="handleHeatmapCellClick"
          />
        </div>
      </div>
    </div>

    <!-- 用例覆盖率分析 -->
    <div class="analysis-section">
      <TestCaseAnalysis />
    </div>

    <!-- 覆盖率列表 -->
    <div class="table-section">
      <div class="table-header">
        <h3 class="table-title">覆盖率记录</h3>
        <div class="table-actions">
          <button class="btn btn-primary" @click="handleRefresh">
            <i class="fas fa-sync-alt"></i>
            刷新数据
          </button>
          <button class="btn btn-secondary" @click="handleExport">
            <i class="fas fa-download"></i>
            导出数据
          </button>
        </div>
      </div>

      <DataTable
        :data="coverageMetrics"
        :columns="tableColumns"
        :loading="loading.metrics"
        :pagination="true"
        :sortable="true"
        @sort="handleSort"
        @row-click="handleRowClick"
      />
    </div>

    <!-- 文件覆盖率详情弹窗 -->
    <FileCoverageModal
      v-if="showFileCoverageModal"
      :metric-id="selectedMetricId"
      @close="showFileCoverageModal = false"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useCoverageStore } from '@/stores/coverage'
import FilterPanel from '@/components/common/FilterPanel.vue'
import DataTable from '@/components/common/DataTable.vue'
import CoverageTrendChart from '@/components/charts/CoverageTrendChart.vue'
import CoverageDistributionChart from '@/components/charts/CoverageDistributionChart.vue'
import CoverageHeatmapChart from '@/components/charts/CoverageHeatmapChart.vue'
import TestCaseAnalysis from '@/components/coverage/TestCaseAnalysis.vue'
import FileCoverageModal from '@/components/coverage/FileCoverageModal.vue'

export default {
  name: 'CoverageManagement',
  components: {
    FilterPanel,
    DataTable,
    CoverageTrendChart,
    CoverageDistributionChart,
    CoverageHeatmapChart,
    TestCaseAnalysis,
    FileCoverageModal
  },
  setup() {
    const coverageStore = useCoverageStore()
    
    // 响应式数据
    const distributionDimension = ref('level')
    const showFileCoverageModal = ref(false)
    const selectedMetricId = ref(null)
    
    // 计算属性
    const coverageMetrics = computed(() => coverageStore.coverageMetrics)
    const coverageStats = computed(() => coverageStore.coverageStats)
    const fileCoverages = computed(() => coverageStore.fileCoverages)
    const loading = computed(() => coverageStore.loading)
    const trendParams = computed(() => coverageStore.trendParams)
    const trendChartData = computed(() => coverageStore.getTrendChartData())
    const distributionChartData = computed(() => coverageStore.getDistributionChartData())
    
    // 筛选配置
    const filterConfig = ref([
      {
        key: 'project_id',
        label: '项目',
        type: 'select',
        options: [], // 将从API获取
        placeholder: '选择项目'
      },
      {
        key: 'branch_name',
        label: '分支',
        type: 'input',
        placeholder: '输入分支名称'
      },
      {
        key: 'source',
        label: '数据源',
        type: 'select',
        options: [
          { label: 'Jest', value: 'jest' },
          { label: 'SonarQube', value: 'sonarqube' },
          { label: 'JaCoCo', value: 'jacoco' },
          { label: 'Pytest', value: 'pytest' },
          { label: '手动录入', value: 'manual' }
        ],
        placeholder: '选择数据源'
      }
    ])
    
    // 表格列配置
    const tableColumns = ref([
      {
        key: 'project_id',
        title: '项目',
        sortable: true,
        width: '120px'
      },
      {
        key: 'branch_name',
        title: '分支',
        sortable: true,
        width: '100px'
      },
      {
        key: 'line_coverage',
        title: '行覆盖率',
        sortable: true,
        width: '120px',
        render: (value) => coverageStore.formatCoverageValue(value)
      },
      {
        key: 'branch_coverage',
        title: '分支覆盖率',
        sortable: true,
        width: '120px',
        render: (value) => coverageStore.formatCoverageValue(value)
      },
      {
        key: 'function_coverage',
        title: '函数覆盖率',
        sortable: true,
        width: '120px',
        render: (value) => coverageStore.formatCoverageValue(value)
      },
      {
        key: 'overall_coverage',
        title: '综合覆盖率',
        sortable: true,
        width: '120px',
        render: (value) => coverageStore.formatCoverageValue(value)
      },
      {
        key: 'coverage_level',
        title: '等级',
        sortable: true,
        width: '80px',
        render: (value) => coverageStore.getCoverageLevelText(value)
      },
      {
        key: 'source',
        title: '数据源',
        sortable: true,
        width: '100px'
      },
      {
        key: 'measurement_date',
        title: '测量时间',
        sortable: true,
        width: '150px',
        render: (value) => new Date(value).toLocaleString()
      }
    ])
    
    // 方法
    const formatCoverageValue = (value) => {
      return coverageStore.formatCoverageValue(value)
    }
    
    const handleFilterChange = (filters) => {
      coverageStore.updateQueryParams(filters)
      loadCoverageData()
    }
    
    const handleFilterReset = () => {
      coverageStore.updateQueryParams({
        project_id: null,
        branch_name: 'main',
        source: null
      })
      loadCoverageData()
    }
    
    const handleTrendParamsChange = () => {
      loadTrendData()
    }
    
    const handleDistributionChange = () => {
      loadDistributionData()
    }
    
    const handleSort = (sortConfig) => {
      coverageStore.updateQueryParams({
        sortBy: sortConfig.key,
        sortOrder: sortConfig.order
      })
      loadCoverageData()
    }
    
    const handleRowClick = (row) => {
      selectedMetricId.value = row.id
      showFileCoverageModal.value = true
    }
    
    const handleRefresh = () => {
      loadAllData()
    }
    
    const handleExport = () => {
      // 实现数据导出功能
      console.log('导出覆盖率数据')
    }

    const handleHeatmapRefresh = async () => {
      try {
        // 获取最新的覆盖率指标，然后获取文件覆盖率
        if (coverageMetrics.value.length > 0) {
          const latestMetric = coverageMetrics.value[0]
          await coverageStore.fetchFileCoverages(latestMetric.id)
        }
      } catch (error) {
        console.error('刷新热力图失败:', error)
      }
    }

    const handleHeatmapCellClick = (event) => {
      console.log('热力图单元格点击:', event)
      // 可以在这里实现点击文件后的详细操作
      // 比如显示文件详情、跳转到代码等
    }
    
    // 数据加载方法
    const loadCoverageData = async () => {
      try {
        await coverageStore.fetchCoverageMetrics()
      } catch (error) {
        console.error('加载覆盖率数据失败:', error)
      }
    }
    
    const loadStatsData = async () => {
      try {
        await coverageStore.fetchCoverageStats()
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    }
    
    const loadTrendData = async () => {
      try {
        await coverageStore.fetchCoverageTrends()
      } catch (error) {
        console.error('加载趋势数据失败:', error)
      }
    }
    
    const loadDistributionData = async () => {
      try {
        await coverageStore.fetchCoverageDistribution({
          dimension: distributionDimension.value
        })
      } catch (error) {
        console.error('加载分布数据失败:', error)
      }
    }
    
    const loadAllData = async () => {
      await Promise.all([
        loadCoverageData(),
        loadStatsData(),
        loadTrendData(),
        loadDistributionData()
      ])
    }
    
    // 生命周期
    onMounted(() => {
      loadAllData()
    })
    
    // 监听分布维度变化
    watch(distributionDimension, () => {
      loadDistributionData()
    })
    
    return {
      // 数据
      coverageMetrics,
      coverageStats,
      fileCoverages,
      loading,
      trendParams,
      trendChartData,
      distributionChartData,
      distributionDimension,
      showFileCoverageModal,
      selectedMetricId,
      filterConfig,
      tableColumns,

      // 方法
      formatCoverageValue,
      handleFilterChange,
      handleFilterReset,
      handleTrendParamsChange,
      handleDistributionChange,
      handleSort,
      handleRowClick,
      handleRefresh,
      handleExport,
      handleHeatmapRefresh,
      handleHeatmapCellClick
    }
  }
}
</script>

<style scoped>
.coverage-management {
  padding: 24px;
  background-color: #f8fafc;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stats-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.stats-label {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.stats-change {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-change.positive {
  color: #10b981;
}

.stats-change.negative {
  color: #ef4444;
}

.stats-change.neutral {
  color: #6b7280;
}

.charts-section {
  margin-bottom: 32px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.chart-row .full-width {
  grid-column: 1 / -1;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: 12px;
}

.chart-controls select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.analysis-section {
  margin-bottom: 32px;
}

.table-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .chart-row {
    grid-template-columns: 1fr;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
