@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 导入增强的主题系统 */
@import './styles/theme.css';

/* 自定义CSS变量 */
:root {
  --primary-color: #4f46e5;
  --secondary-color: #f97316;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --dark-color: #1f2937;
  --light-color: #f9fafb;
  --gray-color: #6b7280;
  --header-height: 64px; /* 4rem = 64px */
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  color: #1f2937;
  background-color: #f9fafb;
}

/* 自定义组件样式 */
.metric-card {
  @apply bg-white rounded-lg shadow-card p-6 transition-all duration-300 ease-in-out;
}

.metric-card:hover {
  @apply transform -translate-y-1 shadow-card-hover;
}

.progress-bar {
  @apply h-2 rounded-full bg-gray-200 overflow-hidden;
}

.progress-bar-fill {
  @apply h-full rounded-full transition-all duration-1000 ease-in-out;
}

.progress-bar-fill.primary {
  background-color: var(--primary-color);
}

.progress-bar-fill.success {
  background-color: var(--success-color);
}

.progress-bar-fill.warning {
  background-color: var(--warning-color);
}

.progress-bar-fill.danger {
  background-color: var(--danger-color);
}

/* 导航按钮样式 */
.nav-btn {
  @apply px-4 py-2 rounded-lg transition-all duration-200 ease-in-out;
}

.nav-btn:hover {
  @apply bg-primary-50 text-primary-600;
}

.nav-btn.active {
  @apply bg-primary-100 text-primary-700 font-medium;
}

/* 徽章样式 */
.badge {
  @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-success-100 text-success-800;
}

.badge-warning {
  @apply bg-warning-100 text-warning-800;
}

.badge-danger {
  @apply bg-danger-100 text-danger-800;
}

.badge-primary {
  @apply bg-primary-100 text-primary-800;
}

/* 按钮样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-200 ease-in-out;
}

.btn:hover {
  @apply transform -translate-y-0.5;
}

.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700;
}

.btn-success {
  @apply bg-success-600 text-white hover:bg-success-700;
}

.btn-warning {
  @apply bg-warning-600 text-white hover:bg-warning-700;
}

.btn-danger {
  @apply bg-danger-600 text-white hover:bg-danger-700;
}

/* 表格样式 */
.data-table {
  @apply w-full border-collapse;
}

.data-table th {
  @apply bg-gray-50 px-4 py-3 text-left font-semibold text-gray-900 border-b border-gray-200;
}

.data-table td {
  @apply px-4 py-3 border-b border-gray-200;
}

.data-table tr:hover {
  @apply bg-gray-50;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.slide-up {
  animation: slideUp 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 顶部导航布局样式 */
.header-layout {
  padding-top: var(--header-height);
}

/* 主内容区域调整 */
.main-content {
  padding-top: var(--header-height);
  transition: padding-top 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }

  .data-table th,
  .data-table td {
    @apply px-2 py-2;
  }

  .metric-card {
    @apply p-4;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
