import axios from 'axios'
import { useAppStore } from '@/stores/app'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const appStore = useAppStore()
    
    // 处理网络错误
    if (!error.response) {
      appStore.showError('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }
    
    // 处理HTTP错误状态码
    const { status, data } = error.response
    
    switch (status) {
      case 400:
        appStore.showError(data.detail || '请求参数错误')
        break
      case 401:
        appStore.showError('未授权访问，请重新登录')
        break
      case 403:
        appStore.showError('权限不足，无法访问该资源')
        break
      case 404:
        appStore.showError('请求的资源不存在')
        break
      case 500:
        appStore.showError('服务器内部错误，请稍后重试')
        break
      default:
        appStore.showError(data.detail || '请求失败，请稍后重试')
    }
    
    return Promise.reject(error)
  }
)

// API服务类
class ApiService {
  // 通用查询方法
  async query(endpoint, params = {}) {
    // 过滤掉null、undefined和空字符串的参数
    const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        acc[key] = value
      }
      return acc
    }, {})

    const queryString = new URLSearchParams(filteredParams).toString()
    const url = queryString ? `${endpoint}?${queryString}` : endpoint
    return api.get(url)
  }

  // 质量大盘相关API
  async getDashboardOverview(params = {}) {
    return this.query('/api/dashboard/overview', params)
  }

  async getDashboardTrends(params = {}) {
    return this.query('/api/dashboard/trends', params)
  }

  async getTeamComparison(params = {}) {
    return this.query('/api/dashboard/teams', params)
  }

  async getProjects(params = {}) {
    return this.query('/api/projects', params)
  }

  async getProject(id) {
    return api.get(`/api/projects/${id}`)
  }

  async getTeams(params = {}) {
    return this.query('/api/teams', params)
  }

  async getMetricCards(params = {}) {
    return this.query('/api/metric-cards', params)
  }

  async getQualityTrends(params = {}) {
    return this.query('/api/quality-trends', params)
  }

  // 自动化测试相关API
  async getAutomationOverview(params = {}) {
    return this.query('/api/automation/overview', params)
  }

  async getAutomationExecutionHistory(params = {}) {
    return this.query('/api/automation/execution-history', params)
  }

  async getAutomationTrends(params = {}) {
    return this.query('/api/automation/trends', params)
  }

  async getAutomationTeamStats(params = {}) {
    return this.query('/api/automation/team-stats', params)
  }

  async getTestCases(params = {}) {
    return this.query('/api/test-cases', params)
  }

  async getTestExecutions(params = {}) {
    return this.query('/api/test-executions', params)
  }

  async getAutomationMetrics(params = {}) {
    return this.query('/api/automation-metrics', params)
  }
  
  // 性能监控相关API
  async getPerformanceOverview(params = {}) {
    return this.query('/api/performance/overview', params)
  }

  async getServicePerformance(params = {}) {
    return this.query('/api/performance/services', params)
  }

  async getPerformanceTrends(params = {}) {
    return this.query('/api/performance/trends', params)
  }

  async getPerformanceAlerts(params = {}) {
    return this.query('/api/performance/alerts', params)
  }

  async getRealTimeMetrics(params = {}) {
    return this.query('/api/performance/real-time', params)
  }

  async getPerformanceMetrics(params = {}) {
    return this.query('/api/performance-metrics', params)
  }

  async getServiceMetrics(params = {}) {
    return this.query('/api/service-metrics', params)
  }

  async getSystemMetrics(params = {}) {
    return this.query('/api/system-metrics', params)
  }

  // 质量门禁相关API
  async getQualityGateOverview(params = {}) {
    return this.query('/api/quality-gate/overview', params)
  }

  async getQualityGateRules(params = {}) {
    return this.query('/api/quality-gate/rules', params)
  }

  async getQualityGateResults(params = {}) {
    return this.query('/api/quality-gate/results', params)
  }

  async getQualityGateTrends(params = {}) {
    return this.query('/api/quality-gate/trends', params)
  }

  async getQualityGateExecutions(params = {}) {
    return this.query('/api/quality-gate-executions', params)
  }

  async executeQualityGate(projectName, data = {}) {
    return api.post(`/api/quality-gate/execute/${projectName}`, data)
  }

  async getExecutionStatus(executionId) {
    return api.get(`/api/quality-gate/execution/${executionId}`)
  }

  // 数据导出API
  async exportData(type, params = {}) {
    return this.query(`/api/export/${type}`, params)
  }

  // 数据统计API
  async getStatistics(params = {}) {
    return this.query('/api/statistics', params)
  }

  // 缺陷管理相关API
  async getDefects(params = {}) {
    return this.query('/api/defects', params)
  }

  async getDefectById(id) {
    return api.get(`/api/defects/${id}`)
  }

  async createDefect(data) {
    return api.post('/api/defects', data)
  }

  async updateDefect(id, data) {
    return api.put(`/api/defects/${id}`, data)
  }

  async deleteDefect(id) {
    return api.delete(`/api/defects/${id}`)
  }

  async getDefectTrends(params = {}) {
    return this.query('/api/defects/trends', params)
  }

  async getDefectDistribution(params = {}) {
    return this.query('/api/defects/distribution', params)
  }

  async getDefectStats(params = {}) {
    return this.query('/api/defects/stats', params)
  }

  // 通用HTTP方法
  async get(url, params = {}) {
    if (params && Object.keys(params).length > 0) {
      // 过滤掉null、undefined和空字符串的参数
      const filteredParams = Object.entries(params).reduce((acc, [key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          acc[key] = value
        }
        return acc
      }, {})

      if (Object.keys(filteredParams).length > 0) {
        const queryString = new URLSearchParams(filteredParams).toString()
        url = `${url}?${queryString}`
      }
    }
    return api.get(url)
  }

  async post(url, data = {}) {
    return api.post(url, data)
  }

  async put(url, data = {}) {
    return api.put(url, data)
  }

  async delete(url) {
    return api.delete(url)
  }
}

// 创建API服务实例
const apiService = new ApiService()

export default apiService
