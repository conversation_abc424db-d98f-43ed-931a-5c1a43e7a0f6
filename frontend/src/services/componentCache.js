/**
 * 前端组件缓存服务
 */

class ComponentCacheService {
  constructor() {
    this.cache = new Map()
    this.maxSize = 100 // 最大缓存数量
    this.ttl = 5 * 60 * 1000 // 5分钟TTL
    this.accessOrder = new Map() // 访问顺序，用于LRU
  }

  /**
   * 生成缓存键
   */
  generateKey(component, props = {}) {
    const componentName = component.name || component.__name || 'anonymous'
    const propsHash = this.hashObject(props)
    return `${componentName}:${propsHash}`
  }

  /**
   * 对象哈希
   */
  hashObject(obj) {
    const str = JSON.stringify(obj, Object.keys(obj).sort())
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(36)
  }

  /**
   * 设置缓存
   */
  set(key, value, customTtl = null) {
    const now = Date.now()
    const expireTime = now + (customTtl || this.ttl)

    // 如果缓存已满，删除最久未访问的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    this.cache.set(key, {
      value,
      expireTime,
      createdAt: now
    })

    this.accessOrder.set(key, now)
  }

  /**
   * 获取缓存
   */
  get(key) {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expireTime) {
      this.delete(key)
      return null
    }

    // 更新访问时间
    this.accessOrder.set(key, Date.now())
    
    return item.value
  }

  /**
   * 删除缓存
   */
  delete(key) {
    this.cache.delete(key)
    this.accessOrder.delete(key)
  }

  /**
   * 清空缓存
   */
  clear() {
    this.cache.clear()
    this.accessOrder.clear()
  }

  /**
   * LRU淘汰
   */
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const now = Date.now()
    let expiredCount = 0
    
    for (const [key, item] of this.cache) {
      if (now > item.expireTime) {
        expiredCount++
      }
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      expiredCount,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    }
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, item] of this.cache) {
      if (now > item.expireTime) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
    
    return expiredKeys.length
  }
}

// 创建全局缓存实例
const componentCache = new ComponentCacheService()

// 定期清理过期缓存
setInterval(() => {
  componentCache.cleanup()
}, 60000) // 每分钟清理一次

/**
 * Vue组件缓存装饰器
 */
export function withCache(component, options = {}) {
  const {
    ttl = 5 * 60 * 1000, // 5分钟
    keyGenerator = null,
    enabled = true
  } = options

  return {
    ...component,
    setup(props, context) {
      if (!enabled) {
        return component.setup ? component.setup(props, context) : {}
      }

      const cacheKey = keyGenerator 
        ? keyGenerator(props) 
        : componentCache.generateKey(component, props)

      // 尝试从缓存获取
      const cached = componentCache.get(cacheKey)
      if (cached) {
        return cached
      }

      // 执行原始setup
      const result = component.setup ? component.setup(props, context) : {}

      // 缓存结果
      componentCache.set(cacheKey, result, ttl)

      return result
    }
  }
}

/**
 * 数据缓存Hook
 */
export function useDataCache(key, fetcher, options = {}) {
  const {
    ttl = 5 * 60 * 1000,
    enabled = true,
    refreshOnMount = false
  } = options

  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const fetchData = async (force = false) => {
    if (!enabled && !force) {
      return
    }

    // 尝试从缓存获取
    if (!force) {
      const cached = componentCache.get(key)
      if (cached) {
        data.value = cached
        return cached
      }
    }

    loading.value = true
    error.value = null

    try {
      const result = await fetcher()
      data.value = result
      
      // 缓存结果
      if (enabled) {
        componentCache.set(key, result, ttl)
      }
      
      return result
    } catch (err) {
      error.value = err
      throw err
    } finally {
      loading.value = false
    }
  }

  const refresh = () => fetchData(true)

  const clear = () => {
    componentCache.delete(key)
    data.value = null
  }

  // 组件挂载时获取数据
  onMounted(() => {
    if (refreshOnMount || !componentCache.get(key)) {
      fetchData()
    } else {
      // 从缓存加载
      const cached = componentCache.get(key)
      if (cached) {
        data.value = cached
      }
    }
  })

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    fetchData,
    refresh,
    clear
  }
}

/**
 * 计算属性缓存Hook
 */
export function useMemoizedComputed(fn, deps, ttl = 60000) {
  const cache = new Map()
  
  return computed(() => {
    const depsKey = JSON.stringify(deps.map(dep => unref(dep)))
    const cached = cache.get(depsKey)
    
    if (cached && Date.now() < cached.expireTime) {
      return cached.value
    }
    
    const result = fn()
    cache.set(depsKey, {
      value: result,
      expireTime: Date.now() + ttl
    })
    
    return result
  })
}

/**
 * 图片懒加载缓存
 */
export class ImageCache {
  constructor() {
    this.cache = new Map()
    this.loading = new Set()
  }

  async loadImage(src) {
    // 如果已经缓存，直接返回
    if (this.cache.has(src)) {
      return this.cache.get(src)
    }

    // 如果正在加载，等待加载完成
    if (this.loading.has(src)) {
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.cache.has(src)) {
            resolve(this.cache.get(src))
          } else {
            setTimeout(checkLoaded, 50)
          }
        }
        checkLoaded()
      })
    }

    // 开始加载
    this.loading.add(src)

    try {
      const img = new Image()
      
      return new Promise((resolve, reject) => {
        img.onload = () => {
          this.cache.set(src, img)
          this.loading.delete(src)
          resolve(img)
        }
        
        img.onerror = () => {
          this.loading.delete(src)
          reject(new Error(`Failed to load image: ${src}`))
        }
        
        img.src = src
      })
    } catch (error) {
      this.loading.delete(src)
      throw error
    }
  }

  clear() {
    this.cache.clear()
    this.loading.clear()
  }

  delete(src) {
    this.cache.delete(src)
    this.loading.delete(src)
  }
}

// 创建全局图片缓存实例
export const imageCache = new ImageCache()

/**
 * 路由缓存管理
 */
export class RouteCache {
  constructor() {
    this.cache = new Map()
    this.maxSize = 10
  }

  set(route, component) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(route, {
      component,
      timestamp: Date.now()
    })
  }

  get(route) {
    const item = this.cache.get(route)
    return item ? item.component : null
  }

  delete(route) {
    this.cache.delete(route)
  }

  clear() {
    this.cache.clear()
  }
}

export const routeCache = new RouteCache()

export default componentCache
