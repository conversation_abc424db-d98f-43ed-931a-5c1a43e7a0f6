<template>
  <div class="global-search" :class="{ 'expanded': isExpanded }">
    <!-- 搜索输入框 -->
    <div class="search-input-container">
      <div class="search-input-wrapper">
        <i class="fas fa-search search-icon"></i>
        <input
          ref="searchInputRef"
          v-model="searchQuery"
          type="text"
          class="search-input"
          :placeholder="placeholder"
          @focus="handleFocus"
          @blur="handleBlur"
          @input="handleInput"
          @keydown="handleKeydown"
          @keyup.enter="handleSearch"
          @keyup.esc="handleEscape"
        />

        <!-- 清除按钮 -->
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="clear-btn"
          title="清除搜索"
        >
          <i class="fas fa-times"></i>
        </button>

        <!-- 高级搜索按钮 -->
        <button
          @click="toggleAdvancedSearch"
          class="advanced-search-btn"
          :class="{ 'active': showAdvancedSearch }"
          title="高级搜索"
        >
          <i class="fas fa-sliders-h"></i>
        </button>

        <!-- 搜索按钮 -->
        <button
          @click="handleSearch"
          class="search-btn"
          :disabled="!searchQuery.trim()"
          title="搜索"
        >
          <i class="fas fa-search"></i>
        </button>
      </div>

      <!-- 搜索类型选择 -->
      <div v-if="isExpanded" class="search-types">
        <button
          v-for="type in searchTypes"
          :key="type.value"
          @click="selectSearchType(type.value)"
          class="search-type-btn"
          :class="{ 'active': selectedType === type.value }"
        >
          <i :class="type.icon"></i>
          <span>{{ type.label }}</span>
        </button>
      </div>
    </div>

    <!-- 搜索建议下拉 -->
    <transition name="dropdown">
      <div
        v-if="showSuggestions && (suggestions.length > 0 || recentSearches.length > 0)"
        class="search-suggestions"
      >
        <!-- 搜索建议 -->
        <div v-if="suggestions.length > 0" class="suggestions-section">
          <div class="suggestions-header">
            <span class="suggestions-title">搜索建议</span>
          </div>
          <div class="suggestions-list">
            <button
              v-for="(suggestion, index) in suggestions"
              :key="index"
              @click="selectSuggestion(suggestion)"
              class="suggestion-item"
              :class="{ 'highlighted': highlightedIndex === index }"
            >
              <i :class="getSuggestionIcon(suggestion.type)" class="suggestion-icon"></i>
              <div class="suggestion-content">
                <div class="suggestion-text" v-html="highlightMatch(suggestion.text)"></div>
                <div class="suggestion-meta">{{ suggestion.category }}</div>
              </div>
              <div class="suggestion-type">{{ suggestion.type }}</div>
            </button>
          </div>
        </div>

        <!-- 最近搜索 -->
        <div v-if="recentSearches.length > 0" class="recent-section">
          <div class="recent-header">
            <span class="recent-title">最近搜索</span>
            <button @click="clearRecentSearches" class="clear-recent-btn">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
          <div class="recent-list">
            <button
              v-for="(recent, index) in recentSearches"
              :key="index"
              @click="selectRecentSearch(recent)"
              class="recent-item"
            >
              <i class="fas fa-history recent-icon"></i>
              <span class="recent-text">{{ recent.query }}</span>
              <span class="recent-time">{{ formatTime(recent.timestamp) }}</span>
              <button
                @click.stop="removeRecentSearch(index)"
                class="remove-recent-btn"
                title="删除"
              >
                <i class="fas fa-times"></i>
              </button>
            </button>
          </div>
        </div>

        <!-- 快捷搜索 -->
        <div v-if="quickSearches.length > 0" class="quick-section">
          <div class="quick-header">
            <span class="quick-title">快捷搜索</span>
          </div>
          <div class="quick-list">
            <button
              v-for="quick in quickSearches"
              :key="quick.id"
              @click="selectQuickSearch(quick)"
              class="quick-item"
            >
              <i :class="quick.icon" class="quick-icon"></i>
              <span class="quick-text">{{ quick.label }}</span>
              <span class="quick-count">{{ quick.count }}</span>
            </button>
          </div>
        </div>
      </div>
    </transition>

    <!-- 高级搜索面板 -->
    <transition name="slide-down">
      <div v-if="showAdvancedSearch" class="advanced-search-panel">
        <div class="advanced-search-header">
          <h3 class="advanced-title">高级搜索</h3>
          <button @click="resetAdvancedSearch" class="reset-btn">
            <i class="fas fa-undo"></i>
            重置
          </button>
        </div>

        <div class="advanced-search-content">
          <!-- 搜索范围 -->
          <div class="search-scope">
            <label class="scope-label">搜索范围</label>
            <div class="scope-options">
              <label
                v-for="scope in searchScopes"
                :key="scope.value"
                class="scope-option"
              >
                <input
                  v-model="advancedFilters.scopes"
                  type="checkbox"
                  :value="scope.value"
                  class="scope-checkbox"
                />
                <span class="scope-text">{{ scope.label }}</span>
              </label>
            </div>
          </div>

          <!-- 时间范围 -->
          <div class="time-range">
            <label class="time-label">时间范围</label>
            <div class="time-options">
              <select v-model="advancedFilters.timeRange" class="time-select">
                <option value="">全部时间</option>
                <option value="1d">最近1天</option>
                <option value="7d">最近7天</option>
                <option value="30d">最近30天</option>
                <option value="90d">最近90天</option>
                <option value="custom">自定义</option>
              </select>

              <div v-if="advancedFilters.timeRange === 'custom'" class="custom-time">
                <input
                  v-model="advancedFilters.startDate"
                  type="date"
                  class="date-input"
                />
                <span class="date-separator">至</span>
                <input
                  v-model="advancedFilters.endDate"
                  type="date"
                  class="date-input"
                />
              </div>
            </div>
          </div>

          <!-- 项目筛选 -->
          <div class="project-filter">
            <label class="project-label">项目</label>
            <select v-model="advancedFilters.projectId" class="project-select">
              <option value="">全部项目</option>
              <option
                v-for="project in projects"
                :key="project.id"
                :value="project.id"
              >
                {{ project.name }}
              </option>
            </select>
          </div>

          <!-- 状态筛选 -->
          <div class="status-filter">
            <label class="status-label">状态</label>
            <div class="status-options">
              <label
                v-for="status in statusOptions"
                :key="status.value"
                class="status-option"
              >
                <input
                  v-model="advancedFilters.statuses"
                  type="checkbox"
                  :value="status.value"
                  class="status-checkbox"
                />
                <span class="status-text">{{ status.label }}</span>
              </label>
            </div>
          </div>
        </div>

        <div class="advanced-search-actions">
          <button @click="applyAdvancedSearch" class="apply-btn">
            <i class="fas fa-search"></i>
            应用搜索
          </button>
          <button @click="showAdvancedSearch = false" class="cancel-btn">
            取消
          </button>
        </div>
      </div>
    </transition>

    <!-- 搜索结果预览 -->
    <transition name="fade">
      <div v-if="showPreview && searchResults.length > 0" class="search-preview">
        <div class="preview-header">
          <span class="preview-title">搜索结果预览</span>
          <span class="preview-count">共 {{ totalResults }} 条结果</span>
          <button @click="viewAllResults" class="view-all-btn">
            查看全部
          </button>
        </div>

        <div class="preview-results">
          <div
            v-for="result in searchResults.slice(0, 5)"
            :key="result.id"
            @click="selectResult(result)"
            class="preview-result"
          >
            <i :class="getResultIcon(result.type)" class="result-icon"></i>
            <div class="result-content">
              <div class="result-title" v-html="highlightMatch(result.title)"></div>
              <div class="result-description">{{ result.description }}</div>
              <div class="result-meta">
                <span class="result-type">{{ result.type }}</span>
                <span class="result-time">{{ formatTime(result.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useSearchStore } from '@/stores/search'
import { useProjectStore } from '@/stores/project'
import { useSearchShortcuts } from '@/composables/useKeyboardShortcuts'
import { useSearchHighlight } from '@/composables/useSearchHighlight'
import { useSearchPerformance } from '@/composables/useSearchPerformance'

const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索缺陷、项目、用户...'
  },
  autoFocus: {
    type: Boolean,
    default: false
  },
  showPreview: {
    type: Boolean,
    default: true
  },
  maxSuggestions: {
    type: Number,
    default: 8
  }
})

const emit = defineEmits([
  'search',
  'select-result',
  'focus',
  'blur'
])

const router = useRouter()
const searchStore = useSearchStore()
const projectStore = useProjectStore()
const { searchInputRef, isSearchFocused, focusSearch, blurSearch } = useSearchShortcuts()
const { highlightText, calculateMatchScore } = useSearchHighlight()
const { debounceSearch, optimizedSearch, prefetchSuggestions } = useSearchPerformance()

// 响应式数据
// const searchInput = ref(null) // 使用 useSearchShortcuts 提供的 searchInputRef
const searchQuery = ref('')
const isExpanded = ref(false)
const isFocused = ref(false)
const showSuggestions = ref(false)
const showAdvancedSearch = ref(false)
const highlightedIndex = ref(-1)
const selectedType = ref('all')

// 高级搜索筛选条件
const advancedFilters = ref({
  scopes: ['defects', 'projects', 'users'],
  timeRange: '',
  startDate: '',
  endDate: '',
  projectId: '',
  statuses: []
})

// 搜索类型配置
const searchTypes = ref([
  { value: 'all', label: '全部', icon: 'fas fa-globe' },
  { value: 'defects', label: '缺陷', icon: 'fas fa-bug' },
  { value: 'projects', label: '项目', icon: 'fas fa-project-diagram' },
  { value: 'users', label: '用户', icon: 'fas fa-users' },
  { value: 'coverage', label: '覆盖率', icon: 'fas fa-chart-line' }
])

// 搜索范围配置
const searchScopes = ref([
  { value: 'defects', label: '缺陷管理' },
  { value: 'projects', label: '项目信息' },
  { value: 'users', label: '用户数据' },
  { value: 'coverage', label: '覆盖率统计' },
  { value: 'reports', label: '报告文档' }
])

// 状态选项
const statusOptions = ref([
  { value: 'open', label: '开放' },
  { value: 'in_progress', label: '进行中' },
  { value: 'resolved', label: '已解决' },
  { value: 'closed', label: '已关闭' }
])

// 计算属性
const suggestions = computed(() => searchStore.suggestions)
const recentSearches = computed(() => searchStore.recentSearches)
const quickSearches = computed(() => searchStore.quickSearches)
const searchResults = computed(() => searchStore.searchResults)
const totalResults = computed(() => searchStore.totalResults)
const projects = computed(() => projectStore.projects)

// 监听器
watch(searchQuery, (newQuery) => {
  if (newQuery.trim()) {
    debouncedGetSuggestions(newQuery)
  } else {
    showSuggestions.value = false
  }
})

// 使用性能优化的防抖函数
const debouncedGetSuggestions = debounceSearch((query) => {
  getSuggestions(query)
  // 预取建议
  prefetchSuggestions(query, getSuggestions)
}, 300)

// 方法
const handleFocus = () => {
  isFocused.value = true
  isExpanded.value = true
  showSuggestions.value = true
  isSearchFocused.value = true
  emit('focus')
}

const handleBlur = () => {
  // 延迟隐藏，允许点击建议项
  setTimeout(() => {
    isFocused.value = false
    isSearchFocused.value = false
    if (!showAdvancedSearch.value) {
      isExpanded.value = false
    }
    showSuggestions.value = false
    emit('blur')
  }, 200)
}

const handleInput = () => {
  highlightedIndex.value = -1
}

const handleKeydown = (event) => {
  if (!showSuggestions.value) return

  const totalItems = suggestions.value.length + recentSearches.value.length

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, totalItems - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0) {
        selectHighlightedItem()
      } else {
        handleSearch()
      }
      break
    case 'Tab':
      if (highlightedIndex.value >= 0) {
        event.preventDefault()
        selectHighlightedItem()
      }
      break
  }
}

const handleEscape = () => {
  showSuggestions.value = false
  showAdvancedSearch.value = false
  searchInput.value?.blur()
}

const handleSearch = () => {
  if (!searchQuery.value.trim()) return

  performSearch()
  addToRecentSearches()
  showSuggestions.value = false
}

const clearSearch = () => {
  searchQuery.value = ''
  showSuggestions.value = false
  searchInputRef.value?.focus()
}

const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
  if (showAdvancedSearch.value) {
    isExpanded.value = true
  }
}

const selectSearchType = (type) => {
  selectedType.value = type
  if (searchQuery.value.trim()) {
    performSearch()
  }
}

const getSuggestions = async (query) => {
  try {
    await searchStore.fetchSuggestions({
      query,
      type: selectedType.value,
      limit: props.maxSuggestions
    })
    showSuggestions.value = true
  } catch (error) {
    console.error('获取搜索建议失败:', error)
  }
}

const selectSuggestion = (suggestion) => {
  searchQuery.value = suggestion.text
  selectedType.value = suggestion.type
  handleSearch()
}

const selectRecentSearch = (recent) => {
  searchQuery.value = recent.query
  selectedType.value = recent.type || 'all'
  handleSearch()
}

const selectQuickSearch = (quick) => {
  searchQuery.value = quick.query
  selectedType.value = quick.type || 'all'
  handleSearch()
}

const selectHighlightedItem = () => {
  const totalSuggestions = suggestions.value.length

  if (highlightedIndex.value < totalSuggestions) {
    selectSuggestion(suggestions.value[highlightedIndex.value])
  } else {
    const recentIndex = highlightedIndex.value - totalSuggestions
    selectRecentSearch(recentSearches.value[recentIndex])
  }
}

const performSearch = async () => {
  const searchParams = {
    query: searchQuery.value.trim(),
    type: selectedType.value,
    ...advancedFilters.value
  }

  try {
    await searchStore.performSearch(searchParams)
    emit('search', searchParams)

    // 如果有结果且启用预览，显示预览
    if (props.showPreview && searchResults.value.length > 0) {
      // 预览会自动显示
    } else {
      // 直接跳转到搜索结果页面
      router.push({
        name: 'SearchResults',
        query: { q: searchQuery.value, type: selectedType.value }
      })
    }
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

const addToRecentSearches = () => {
  searchStore.addRecentSearch({
    query: searchQuery.value.trim(),
    type: selectedType.value,
    timestamp: Date.now()
  })
}

const clearRecentSearches = () => {
  searchStore.clearRecentSearches()
}

const removeRecentSearch = (index) => {
  searchStore.removeRecentSearch(index)
}

const resetAdvancedSearch = () => {
  advancedFilters.value = {
    scopes: ['defects', 'projects', 'users'],
    timeRange: '',
    startDate: '',
    endDate: '',
    projectId: '',
    statuses: []
  }
}

const applyAdvancedSearch = () => {
  showAdvancedSearch.value = false
  if (searchQuery.value.trim()) {
    performSearch()
  }
}

const selectResult = (result) => {
  emit('select-result', result)

  // 根据结果类型跳转到相应页面
  switch (result.type) {
    case 'defect':
      router.push({ name: 'DefectDetail', params: { id: result.id } })
      break
    case 'project':
      router.push({ name: 'ProjectDetail', params: { id: result.id } })
      break
    case 'user':
      router.push({ name: 'UserProfile', params: { id: result.id } })
      break
    default:
      console.log('选择结果:', result)
  }
}

const viewAllResults = () => {
  router.push({
    name: 'SearchResults',
    query: { q: searchQuery.value, type: selectedType.value }
  })
}

const getSuggestionIcon = (type) => {
  const iconMap = {
    defect: 'fas fa-bug',
    project: 'fas fa-project-diagram',
    user: 'fas fa-user',
    coverage: 'fas fa-chart-line',
    report: 'fas fa-file-alt'
  }
  return iconMap[type] || 'fas fa-search'
}

const getResultIcon = (type) => {
  return getSuggestionIcon(type)
}

const highlightMatch = (text) => {
  return highlightText(text, searchQuery.value, {
    highlightClass: 'search-highlight-primary'
  })
}

const formatTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 生命周期
onMounted(() => {
  if (props.autoFocus) {
    nextTick(() => {
      searchInputRef.value?.focus()
    })
  }

  // 加载项目列表
  projectStore.fetchProjects()

  // 加载快捷搜索
  searchStore.loadQuickSearches()

  // 监听快捷键事件
  window.addEventListener('open-global-search', () => {
    nextTick(() => {
      searchInputRef.value?.focus()
      isExpanded.value = true
      showSuggestions.value = true
    })
  })

  window.addEventListener('perform-search', handleSearch)
  window.addEventListener('clear-or-close-search', () => {
    if (searchQuery.value) {
      clearSearch()
    } else {
      handleBlur()
    }
  })

  window.addEventListener('select-next-suggestion', () => {
    const totalItems = suggestions.value.length + recentSearches.value.length
    if (totalItems > 0) {
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, totalItems - 1)
    }
  })

  window.addEventListener('select-previous-suggestion', () => {
    highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
  })

  window.addEventListener('select-current-suggestion', () => {
    if (highlightedIndex.value >= 0) {
      selectHighlightedItem()
    }
  })
})

onUnmounted(() => {
  clearTimeout(suggestionTimer)
})

// 暴露方法给父组件
defineExpose({
  focus: () => searchInputRef.value?.focus(),
  blur: () => searchInputRef.value?.blur(),
  clear: clearSearch,
  search: handleSearch
})
</script>

<style scoped>
.global-search {
  position: relative;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.global-search.expanded {
  z-index: 1000;
}

/* 搜索输入容器 */
.search-input-container {
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--border-color, #e5e7eb);
  transition: all var(--transition-normal, 0.25s ease);
}

.global-search.expanded .search-input-container {
  border-radius: var(--radius-lg, 12px) var(--radius-lg, 12px) 0 0;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  border-color: var(--primary-300, #a5b4fc);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 8px;
}

.search-icon {
  color: var(--gray-400, #9ca3af);
  font-size: 16px;
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 16px 0;
  font-size: 16px;
  color: var(--on-surface, #1f2937);
  background: transparent;
  placeholder-color: var(--gray-400, #9ca3af);
}

.search-input::placeholder {
  color: var(--gray-400, #9ca3af);
}

.clear-btn,
.advanced-search-btn,
.search-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: var(--radius, 6px);
  cursor: pointer;
  color: var(--gray-500, #6b7280);
  transition: all var(--transition-fast, 0.15s ease);
  flex-shrink: 0;
}

.clear-btn:hover,
.advanced-search-btn:hover {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
}

.advanced-search-btn.active {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-600, #4f46e5);
}

.search-btn {
  background: var(--primary-600, #4f46e5);
  color: white;
  padding: 8px 12px;
}

.search-btn:hover:not(:disabled) {
  background: var(--primary-700, #4338ca);
}

.search-btn:disabled {
  background: var(--gray-300, #d1d5db);
  cursor: not-allowed;
}

/* 搜索类型选择 */
.search-types {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-variant, #f9fafb);
}

.search-type-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
}

.search-type-btn:hover {
  border-color: var(--primary-300, #a5b4fc);
  color: var(--primary-600, #4f46e5);
}

.search-type-btn.active {
  background: var(--primary-600, #4f46e5);
  border-color: var(--primary-600, #4f46e5);
  color: white;
}

/* 搜索建议下拉 */
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--surface, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-top: none;
  border-radius: 0 0 var(--radius-lg, 12px) var(--radius-lg, 12px);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  max-height: 400px;
  overflow-y: auto;
  z-index: 1001;
}

.suggestions-section,
.recent-section,
.quick-section {
  border-bottom: 1px solid var(--divider-color, #f3f4f6);
}

.suggestions-section:last-child,
.recent-section:last-child,
.quick-section:last-child {
  border-bottom: none;
}

.suggestions-header,
.recent-header,
.quick-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px 8px;
}

.suggestions-title,
.recent-title,
.quick-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-500, #6b7280);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.clear-recent-btn {
  background: none;
  border: none;
  color: var(--gray-400, #9ca3af);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm, 4px);
  transition: color var(--transition-fast, 0.15s ease);
}

.clear-recent-btn:hover {
  color: var(--gray-600, #4b5563);
}

.suggestions-list,
.recent-list,
.quick-list {
  padding: 0 8px 8px;
}

.suggestion-item,
.recent-item,
.quick-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 8px;
  border: none;
  border-radius: var(--radius, 6px);
  background: none;
  cursor: pointer;
  text-align: left;
  transition: background-color var(--transition-fast, 0.15s ease);
}

.suggestion-item:hover,
.recent-item:hover,
.quick-item:hover,
.suggestion-item.highlighted {
  background: var(--surface-variant, #f9fafb);
}

.suggestion-icon,
.recent-icon,
.quick-icon {
  width: 16px;
  color: var(--gray-400, #9ca3af);
  margin-right: 12px;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-text {
  font-size: 14px;
  color: var(--on-surface, #1f2937);
  margin-bottom: 2px;
}

.suggestion-text :deep(mark) {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  padding: 0 2px;
  border-radius: 2px;
}

.suggestion-meta {
  font-size: 12px;
  color: var(--gray-500, #6b7280);
}

.suggestion-type {
  font-size: 11px;
  color: var(--gray-400, #9ca3af);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-left: 8px;
}

.recent-text {
  flex: 1;
  font-size: 14px;
  color: var(--on-surface, #1f2937);
  margin-right: 8px;
}

.recent-time {
  font-size: 12px;
  color: var(--gray-500, #6b7280);
  margin-right: 8px;
}

.remove-recent-btn {
  background: none;
  border: none;
  color: var(--gray-400, #9ca3af);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-sm, 4px);
  opacity: 0;
  transition: all var(--transition-fast, 0.15s ease);
}

.recent-item:hover .remove-recent-btn {
  opacity: 1;
}

.remove-recent-btn:hover {
  color: var(--red-500, #ef4444);
  background: var(--red-50, #fef2f2);
}

.quick-text {
  flex: 1;
  font-size: 14px;
  color: var(--on-surface, #1f2937);
  margin-right: 8px;
}

.quick-count {
  font-size: 12px;
  color: var(--gray-500, #6b7280);
  background: var(--gray-100, #f3f4f6);
  padding: 2px 6px;
  border-radius: var(--radius-sm, 4px);
}

/* 高级搜索面板 */
.advanced-search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--surface, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-top: none;
  border-radius: 0 0 var(--radius-lg, 12px) var(--radius-lg, 12px);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  z-index: 1001;
}

.advanced-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--divider-color, #f3f4f6);
  background: var(--surface-variant, #f9fafb);
}

.advanced-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: 1px solid var(--border-color, #e5e7eb);
  padding: 6px 12px;
  border-radius: var(--radius, 6px);
  color: var(--gray-600, #4b5563);
  cursor: pointer;
  font-size: 14px;
  transition: all var(--transition-fast, 0.15s ease);
}

.reset-btn:hover {
  background: var(--gray-50, #f9fafb);
  border-color: var(--gray-300, #d1d5db);
}

.advanced-search-content {
  padding: 20px;
  display: grid;
  gap: 20px;
}

.search-scope,
.time-range,
.project-filter,
.status-filter {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scope-label,
.time-label,
.project-label,
.status-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--on-surface, #1f2937);
}

.scope-options,
.status-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.scope-option,
.status-option {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.scope-checkbox,
.status-checkbox {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-600, #4f46e5);
}

.scope-text,
.status-text {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

.time-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.time-select,
.project-select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  font-size: 14px;
  background: var(--surface, white);
  color: var(--on-surface, #1f2937);
}

.custom-time {
  display: flex;
  align-items: center;
  gap: 12px;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: var(--radius, 6px);
  font-size: 14px;
  background: var(--surface, white);
  color: var(--on-surface, #1f2937);
}

.date-separator {
  font-size: 14px;
  color: var(--gray-500, #6b7280);
}

.advanced-search-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--divider-color, #f3f4f6);
  background: var(--surface-variant, #f9fafb);
}

.apply-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: var(--primary-600, #4f46e5);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: var(--radius, 6px);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast, 0.15s ease);
}

.apply-btn:hover {
  background: var(--primary-700, #4338ca);
}

.cancel-btn {
  background: none;
  border: 1px solid var(--border-color, #e5e7eb);
  padding: 8px 16px;
  border-radius: var(--radius, 6px);
  color: var(--gray-600, #4b5563);
  cursor: pointer;
  font-size: 14px;
  transition: all var(--transition-fast, 0.15s ease);
}

.cancel-btn:hover {
  background: var(--gray-50, #f9fafb);
  border-color: var(--gray-300, #d1d5db);
}

/* 搜索结果预览 */
.search-preview {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--surface, white);
  border: 1px solid var(--border-color, #e5e7eb);
  border-top: none;
  border-radius: 0 0 var(--radius-lg, 12px) var(--radius-lg, 12px);
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  max-height: 400px;
  overflow-y: auto;
  z-index: 1001;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--divider-color, #f3f4f6);
  background: var(--surface-variant, #f9fafb);
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
}

.preview-count {
  font-size: 12px;
  color: var(--gray-500, #6b7280);
}

.view-all-btn {
  background: var(--primary-600, #4f46e5);
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: var(--radius, 6px);
  font-size: 12px;
  cursor: pointer;
  transition: background-color var(--transition-fast, 0.15s ease);
}

.view-all-btn:hover {
  background: var(--primary-700, #4338ca);
}

.preview-results {
  padding: 8px;
}

.preview-result {
  display: flex;
  align-items: flex-start;
  padding: 12px 8px;
  border-radius: var(--radius, 6px);
  cursor: pointer;
  transition: background-color var(--transition-fast, 0.15s ease);
}

.preview-result:hover {
  background: var(--surface-variant, #f9fafb);
}

.result-icon {
  width: 16px;
  color: var(--gray-400, #9ca3af);
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--on-surface, #1f2937);
  margin-bottom: 4px;
  line-height: 1.4;
}

.result-title :deep(mark) {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  padding: 0 2px;
  border-radius: 2px;
}

.result-description {
  font-size: 13px;
  color: var(--gray-600, #4b5563);
  margin-bottom: 6px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.result-type {
  color: var(--primary-600, #4f46e5);
  background: var(--primary-50, #eef2ff);
  padding: 2px 6px;
  border-radius: var(--radius-sm, 4px);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.result-time {
  color: var(--gray-500, #6b7280);
}

/* 动画效果 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all var(--transition-normal, 0.25s ease);
  transform-origin: top;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scaleY(0.95) translateY(-10px);
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all var(--transition-normal, 0.25s ease);
  transform-origin: top;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: scaleY(0.95);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal, 0.25s ease);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .global-search {
    max-width: 100%;
  }

  .search-input-wrapper {
    padding: 0 12px;
  }

  .search-input {
    padding: 12px 0;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .search-types {
    padding: 8px 12px;
    gap: 6px;
  }

  .search-type-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .advanced-search-content {
    padding: 16px;
    gap: 16px;
  }

  .scope-options,
  .status-options {
    gap: 8px;
  }

  .custom-time {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .date-separator {
    text-align: center;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .global-search:not([data-theme]) {
    --surface: #1f2937;
    --surface-variant: #374151;
    --on-surface: #f9fafb;
    --on-surface-variant: #d1d5db;
    --border-color: #4b5563;
    --divider-color: #374151;
  }
}
</style>