<template>
  <div class="search-analytics">
    <!-- 分析头部 -->
    <div class="analytics-header">
      <h3 class="analytics-title">
        <i class="fas fa-chart-line mr-2"></i>
        搜索分析
      </h3>
      <div class="header-actions">
        <button @click="refreshAnalytics" class="btn btn-secondary" :disabled="loading">
          <i class="fas fa-sync-alt mr-2" :class="{ 'fa-spin': loading }"></i>
          刷新
        </button>
        <button @click="exportAnalytics" class="btn btn-primary">
          <i class="fas fa-download mr-2"></i>
          导出
        </button>
      </div>
    </div>

    <!-- 性能指标卡片 -->
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-icon success">
          <i class="fas fa-search"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ performanceStats.totalSearches }}</div>
          <div class="metric-label">总搜索次数</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon primary">
          <i class="fas fa-clock"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ performanceStats.averageResponseTime }}ms</div>
          <div class="metric-label">平均响应时间</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon warning">
          <i class="fas fa-memory"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ performanceStats.cacheStats.hitRate }}</div>
          <div class="metric-label">缓存命中率</div>
        </div>
      </div>

      <div class="metric-card">
        <div class="metric-icon info">
          <i class="fas fa-users"></i>
        </div>
        <div class="metric-content">
          <div class="metric-value">{{ performanceStats.activeRequests }}</div>
          <div class="metric-label">活跃请求</div>
        </div>
      </div>
    </div>

    <!-- 搜索趋势图表 -->
    <div class="analytics-section">
      <div class="section-header">
        <h4 class="section-title">搜索趋势</h4>
        <div class="time-range-selector">
          <select v-model="selectedTimeRange" @change="updateTrendData">
            <option value="1h">最近1小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
        </div>
      </div>
      
      <div class="chart-container">
        <canvas ref="trendChart" class="trend-chart"></canvas>
      </div>
    </div>

    <!-- 热门搜索词 -->
    <div class="analytics-section">
      <div class="section-header">
        <h4 class="section-title">热门搜索词</h4>
        <span class="section-subtitle">基于搜索频率排序</span>
      </div>
      
      <div class="popular-queries">
        <div 
          v-for="(query, index) in popularQueries"
          :key="query.query"
          class="query-item"
          :class="`rank-${index + 1}`"
        >
          <div class="query-rank">{{ index + 1 }}</div>
          <div class="query-content">
            <div class="query-text">{{ query.query }}</div>
            <div class="query-meta">
              <span class="query-count">{{ query.count }} 次搜索</span>
              <span class="query-type">{{ getTypeLabel(query.type) }}</span>
            </div>
          </div>
          <div class="query-trend">
            <i :class="getTrendIcon(query.trend)" :title="getTrendText(query.trend)"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索类型分布 -->
    <div class="analytics-section">
      <div class="section-header">
        <h4 class="section-title">搜索类型分布</h4>
      </div>
      
      <div class="type-distribution">
        <div 
          v-for="type in typeDistribution"
          :key="type.type"
          class="type-item"
        >
          <div class="type-info">
            <i :class="getTypeIcon(type.type)" class="type-icon"></i>
            <span class="type-label">{{ getTypeLabel(type.type) }}</span>
          </div>
          <div class="type-stats">
            <div class="type-bar">
              <div 
                class="type-fill"
                :style="{ width: `${type.percentage}%` }"
                :class="getTypeClass(type.type)"
              ></div>
            </div>
            <span class="type-percentage">{{ type.percentage }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索性能详情 -->
    <div class="analytics-section">
      <div class="section-header">
        <h4 class="section-title">性能详情</h4>
        <button @click="showPerformanceDetails = !showPerformanceDetails" class="toggle-btn">
          <i :class="showPerformanceDetails ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
        </button>
      </div>
      
      <transition name="slide-down">
        <div v-if="showPerformanceDetails" class="performance-details">
          <div class="detail-grid">
            <div class="detail-item">
              <span class="detail-label">缓存大小:</span>
              <span class="detail-value">{{ performanceStats.cacheStats.size }} / {{ performanceStats.cacheStats.maxSize }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">缓存命中:</span>
              <span class="detail-value">{{ performanceStats.cacheStats.hits }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">缓存未命中:</span>
              <span class="detail-value">{{ performanceStats.cacheStats.misses }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">队列请求:</span>
              <span class="detail-value">{{ performanceStats.queuedRequests }}</span>
            </div>
          </div>
          
          <div class="performance-actions">
            <button @click="clearCache" class="btn btn-warning">
              <i class="fas fa-trash mr-2"></i>
              清空缓存
            </button>
            <button @click="resetMetrics" class="btn btn-secondary">
              <i class="fas fa-undo mr-2"></i>
              重置指标
            </button>
          </div>
        </div>
      </transition>
    </div>

    <!-- 实时搜索活动 -->
    <div class="analytics-section">
      <div class="section-header">
        <h4 class="section-title">实时搜索活动</h4>
        <div class="activity-status">
          <span class="status-indicator" :class="{ 'active': hasRecentActivity }"></span>
          <span class="status-text">{{ hasRecentActivity ? '活跃' : '空闲' }}</span>
        </div>
      </div>
      
      <div class="activity-feed">
        <div 
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
          <div class="activity-content">
            <span class="activity-query">"{{ activity.query }}"</span>
            <span class="activity-type">{{ getTypeLabel(activity.type) }}</span>
            <span class="activity-results">{{ activity.resultCount }} 结果</span>
          </div>
          <div class="activity-duration">{{ activity.duration }}ms</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useSearchPerformance } from '@/composables/useSearchPerformance'
import { useSearchStore } from '@/stores/search'

const searchStore = useSearchStore()
const { getPerformanceStats, clearCache, resetMetrics } = useSearchPerformance()

// 响应式数据
const loading = ref(false)
const selectedTimeRange = ref('24h')
const showPerformanceDetails = ref(false)
const trendChart = ref(null)

// 模拟数据
const popularQueries = ref([
  { query: '登录问题', count: 156, type: 'defects', trend: 'up' },
  { query: '性能优化', count: 89, type: 'all', trend: 'stable' },
  { query: 'API接口', count: 67, type: 'projects', trend: 'down' },
  { query: '用户权限', count: 45, type: 'users', trend: 'up' },
  { query: '测试覆盖率', count: 34, type: 'coverage', trend: 'stable' }
])

const typeDistribution = ref([
  { type: 'defects', count: 245, percentage: 45 },
  { type: 'projects', count: 134, percentage: 25 },
  { type: 'users', count: 89, percentage: 16 },
  { type: 'coverage', count: 76, percentage: 14 }
])

const recentActivities = ref([
  {
    id: 1,
    query: '登录验证',
    type: 'defects',
    resultCount: 12,
    duration: 156,
    timestamp: Date.now() - 30000
  },
  {
    id: 2,
    query: '项目管理',
    type: 'projects',
    resultCount: 8,
    duration: 89,
    timestamp: Date.now() - 120000
  }
])

// 计算属性
const performanceStats = computed(() => getPerformanceStats.value)

const hasRecentActivity = computed(() => {
  const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
  return recentActivities.value.some(activity => activity.timestamp > fiveMinutesAgo)
})

// 方法
const refreshAnalytics = async () => {
  loading.value = true
  try {
    // 刷新分析数据
    await new Promise(resolve => setTimeout(resolve, 1000))
  } finally {
    loading.value = false
  }
}

const exportAnalytics = () => {
  const data = {
    performance: performanceStats.value,
    popularQueries: popularQueries.value,
    typeDistribution: typeDistribution.value,
    exportTime: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `search-analytics-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const updateTrendData = () => {
  // 更新趋势图表数据
  console.log('更新趋势数据:', selectedTimeRange.value)
}

const getTypeLabel = (type) => {
  const labels = {
    defects: '缺陷',
    projects: '项目',
    users: '用户',
    coverage: '覆盖率',
    all: '全部'
  }
  return labels[type] || type
}

const getTypeIcon = (type) => {
  const icons = {
    defects: 'fas fa-bug',
    projects: 'fas fa-project-diagram',
    users: 'fas fa-users',
    coverage: 'fas fa-chart-line',
    all: 'fas fa-globe'
  }
  return icons[type] || 'fas fa-search'
}

const getTypeClass = (type) => {
  const classes = {
    defects: 'type-defects',
    projects: 'type-projects',
    users: 'type-users',
    coverage: 'type-coverage'
  }
  return classes[type] || ''
}

const getTrendIcon = (trend) => {
  const icons = {
    up: 'fas fa-arrow-up text-green-500',
    down: 'fas fa-arrow-down text-red-500',
    stable: 'fas fa-minus text-gray-500'
  }
  return icons[trend] || 'fas fa-minus text-gray-500'
}

const getTrendText = (trend) => {
  const texts = {
    up: '上升趋势',
    down: '下降趋势',
    stable: '保持稳定'
  }
  return texts[trend] || '无变化'
}

const formatTime = (timestamp) => {
  const now = Date.now()
  const diff = now - timestamp
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return `${Math.floor(diff / 86400000)}天前`
}

// 生命周期
onMounted(() => {
  // 初始化图表
  // initTrendChart()
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.search-analytics {
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow, 0 1px 3px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-variant, #f9fafb);
}

.analytics-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--radius, 6px);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
  border: none;
  display: flex;
  align-items: center;
}

.btn-primary {
  background: var(--primary-600, #4f46e5);
  color: white;
}

.btn-secondary {
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  border: 1px solid var(--border-color, #e5e7eb);
}

.btn-warning {
  background: var(--warning-600, #d97706);
  color: white;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius, 6px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius, 6px);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.metric-icon.success {
  background: var(--success-100, #dcfce7);
  color: var(--success-600, #16a34a);
}

.metric-icon.primary {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-600, #4f46e5);
}

.metric-icon.warning {
  background: var(--warning-100, #fef3c7);
  color: var(--warning-600, #d97706);
}

.metric-icon.info {
  background: var(--info-100, #dbeafe);
  color: var(--info-600, #2563eb);
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--on-surface, #1f2937);
  line-height: 1;
}

.metric-label {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
  margin-top: 4px;
}

.analytics-section {
  padding: 24px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.analytics-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0;
}

.section-subtitle {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

.chart-container {
  height: 200px;
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius, 6px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color, #e5e7eb);
}

.popular-queries {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.query-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius, 6px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.query-rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-600, #4f46e5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 16px;
}

.query-content {
  flex: 1;
}

.query-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--on-surface, #1f2937);
  margin-bottom: 4px;
}

.query-meta {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
}

.type-distribution {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.type-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.type-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  margin-left: 20px;
}

.type-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200, #e5e7eb);
  border-radius: 4px;
  overflow: hidden;
}

.type-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.type-fill.type-defects {
  background: var(--red-500, #ef4444);
}

.type-fill.type-projects {
  background: var(--blue-500, #3b82f6);
}

.type-fill.type-users {
  background: var(--green-500, #10b981);
}

.type-fill.type-coverage {
  background: var(--purple-500, #8b5cf6);
}

.performance-details {
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius, 6px);
  padding: 20px;
  margin-top: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 12px;
  background: var(--surface, white);
  border-radius: var(--radius, 6px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.activity-feed {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius, 6px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.activity-time {
  width: 80px;
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
}

.activity-content {
  flex: 1;
  display: flex;
  gap: 12px;
  align-items: center;
}

.activity-query {
  font-weight: 500;
  color: var(--on-surface, #1f2937);
}

.activity-duration {
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--gray-400, #9ca3af);
  margin-right: 8px;
}

.status-indicator.active {
  background: var(--success-500, #10b981);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
