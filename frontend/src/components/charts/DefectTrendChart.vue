<template>
  <div class="defect-trend-chart bg-white rounded-lg shadow p-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-900">缺陷趋势分析</h3>
      <div class="flex space-x-2">
        <select
          v-model="selectedTimeRange"
          @change="handleTimeRangeChange"
          class="form-select text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="7d">最近7天</option>
          <option value="30d">最近30天</option>
          <option value="90d">最近90天</option>
          <option value="1y">最近1年</option>
        </select>
        <button
          @click="refreshData"
          :disabled="loading"
          class="px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 rounded-md transition-colors disabled:opacity-50"
        >
          <svg v-if="!loading" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          <svg v-else class="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </button>
      </div>
    </div>

    <div class="chart-container" style="height: 400px;">
      <Line
        v-if="chartData && !loading"
        :data="chartData"
        :options="chartOptions"
      />
      <div v-else-if="loading" class="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
      <div v-else class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <p class="mt-2">暂无趋势数据</p>
        </div>
      </div>
    </div>

    <!-- 趋势统计信息 -->
    <div v-if="trendData && trendData.summary" class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm font-medium text-gray-500">总缺陷数</div>
        <div class="text-2xl font-bold text-gray-900">{{ trendData.summary.total_defects }}</div>
        <div class="text-sm" :class="getTrendClass(trendData.summary.total_change)">
          {{ formatChange(trendData.summary.total_change) }}
        </div>
      </div>
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm font-medium text-gray-500">新增缺陷</div>
        <div class="text-2xl font-bold text-orange-600">{{ trendData.summary.new_defects }}</div>
        <div class="text-sm" :class="getTrendClass(trendData.summary.new_change)">
          {{ formatChange(trendData.summary.new_change) }}
        </div>
      </div>
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm font-medium text-gray-500">已解决缺陷</div>
        <div class="text-2xl font-bold text-green-600">{{ trendData.summary.resolved_defects }}</div>
        <div class="text-sm" :class="getTrendClass(trendData.summary.resolved_change)">
          {{ formatChange(trendData.summary.resolved_change) }}
        </div>
      </div>
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm font-medium text-gray-500">解决率</div>
        <div class="text-2xl font-bold text-blue-600">{{ trendData.summary.resolution_rate }}%</div>
        <div class="text-sm" :class="getTrendClass(trendData.summary.rate_change)">
          {{ formatChange(trendData.summary.rate_change) }}%
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Line } from 'vue-chartjs'
import { useDefectStore } from '@/stores/defect'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'

const props = defineProps({
  projectId: {
    type: Number,
    default: null
  },
  height: {
    type: Number,
    default: 400
  }
})

const emit = defineEmits(['data-updated'])

const defectStore = useDefectStore()
const selectedTimeRange = ref('30d')

// 计算属性
const loading = computed(() => defectStore.loading.trends)
const trendData = computed(() => defectStore.trendData)

const chartData = computed(() => {
  if (!trendData.value) return null
  return defectStore.formatTrendChartData(trendData.value)
})

// 图表配置
const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    mode: 'index',
    intersect: false,
  },
  plugins: {
    legend: {
      position: 'top',
      labels: {
        usePointStyle: true,
        padding: 20
      }
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      callbacks: {
        title: function(context) {
          return context[0].label
        },
        label: function(context) {
          return `${context.dataset.label}: ${context.parsed.y}`
        }
      }
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '日期'
      },
      grid: {
        display: false
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '缺陷数量'
      },
      beginAtZero: true,
      ticks: {
        stepSize: 1
      }
    }
  },
  elements: {
    line: {
      tension: 0.4
    },
    point: {
      radius: 4,
      hoverRadius: 6
    }
  }
}

// 方法
const fetchTrendData = async () => {
  try {
    await defectStore.fetchTrendData({
      project_id: props.projectId,
      time_range: selectedTimeRange.value
    })
    emit('data-updated', trendData.value)
  } catch (error) {
    console.error('获取缺陷趋势数据失败:', error)
  }
}

const handleTimeRangeChange = () => {
  fetchTrendData()
}

const refreshData = () => {
  fetchTrendData()
}

const getTrendClass = (change) => {
  if (change > 0) return 'text-red-600'
  if (change < 0) return 'text-green-600'
  return 'text-gray-600'
}

const formatChange = (change) => {
  if (change === 0) return '无变化'
  const prefix = change > 0 ? '+' : ''
  return `${prefix}${change}`
}

// 监听项目ID变化
watch(() => props.projectId, () => {
  fetchTrendData()
})

// 组件挂载时获取数据
onMounted(() => {
  fetchTrendData()
})

// 暴露方法给父组件
defineExpose({
  refresh: fetchTrendData
})
</script>

<style scoped>
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.chart-container {
  position: relative;
}
</style>
