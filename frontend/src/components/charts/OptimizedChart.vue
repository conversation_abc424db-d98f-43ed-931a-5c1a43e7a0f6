<template>
  <div 
    ref="chartContainer"
    class="optimized-chart-container"
    :style="{ height: height + 'px' }"
  >
    <!-- 图表加载状态 -->
    <div 
      v-if="isLoading"
      class="chart-loading"
    >
      <div class="flex items-center justify-center h-full">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-2 text-gray-600">图表加载中...</span>
      </div>
    </div>

    <!-- 图表错误状态 -->
    <div 
      v-else-if="error"
      class="chart-error"
    >
      <div class="flex items-center justify-center h-full text-red-500">
        <i class="fas fa-exclamation-triangle mr-2"></i>
        <span>图表加载失败</span>
        <button 
          @click="retryRender"
          class="ml-2 text-primary-600 hover:text-primary-800"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 实际图表 -->
    <canvas 
      v-else
      ref="chartCanvas"
      :width="canvasWidth"
      :height="canvasHeight"
      class="chart-canvas"
    ></canvas>

    <!-- 图表工具栏 -->
    <div 
      v-if="showToolbar && !isLoading && !error"
      class="chart-toolbar"
    >
      <button 
        @click="downloadChart"
        class="toolbar-btn"
        title="下载图表"
      >
        <i class="fas fa-download"></i>
      </button>
      <button 
        @click="toggleFullscreen"
        class="toolbar-btn"
        title="全屏显示"
      >
        <i class="fas fa-expand"></i>
      </button>
      <button 
        @click="refreshChart"
        class="toolbar-btn"
        title="刷新图表"
      >
        <i class="fas fa-refresh"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import { Chart, registerables } from 'chart.js'

// 注册Chart.js组件
Chart.register(...registerables)

const props = defineProps({
  type: {
    type: String,
    default: 'line'
  },
  data: {
    type: Object,
    required: true
  },
  options: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: Number,
    default: 400
  },
  width: {
    type: Number,
    default: null
  },
  responsive: {
    type: Boolean,
    default: true
  },
  maintainAspectRatio: {
    type: Boolean,
    default: false
  },
  showToolbar: {
    type: Boolean,
    default: true
  },
  enableDataSampling: {
    type: Boolean,
    default: true
  },
  maxDataPoints: {
    type: Number,
    default: 1000
  },
  animationDuration: {
    type: Number,
    default: 300
  }
})

const emit = defineEmits(['chart-created', 'chart-updated', 'chart-destroyed'])

const chartContainer = ref(null)
const chartCanvas = ref(null)
const chartInstance = ref(null)
const isLoading = ref(true)
const error = ref(null)
const resizeObserver = ref(null)

// 计算画布尺寸
const canvasWidth = computed(() => {
  return props.width || (chartContainer.value?.clientWidth || 800)
})

const canvasHeight = computed(() => {
  return props.height || 400
})

// 数据采样函数
const sampleData = (data) => {
  if (!props.enableDataSampling || !data.datasets) {
    return data
  }

  const sampledData = { ...data }
  sampledData.datasets = data.datasets.map(dataset => {
    if (dataset.data && dataset.data.length > props.maxDataPoints) {
      const step = Math.ceil(dataset.data.length / props.maxDataPoints)
      const sampledDataset = { ...dataset }
      sampledDataset.data = dataset.data.filter((_, index) => index % step === 0)
      
      // 同时采样标签
      if (data.labels && data.labels.length === dataset.data.length) {
        sampledData.labels = data.labels.filter((_, index) => index % step === 0)
      }
      
      return sampledDataset
    }
    return dataset
  })

  return sampledData
}

// 获取优化的图表选项
const getOptimizedOptions = () => {
  const baseOptions = {
    responsive: props.responsive,
    maintainAspectRatio: props.maintainAspectRatio,
    animation: {
      duration: props.animationDuration
    },
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        display: true,
        position: 'top'
      },
      tooltip: {
        enabled: true,
        mode: 'index',
        intersect: false,
        animation: {
          duration: 0 // 禁用tooltip动画以提高性能
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: true,
          drawBorder: false
        }
      },
      y: {
        display: true,
        grid: {
          display: true,
          drawBorder: false
        }
      }
    },
    // 性能优化选项
    parsing: false, // 禁用数据解析以提高性能
    normalized: true, // 启用数据标准化
    spanGaps: true // 跨越空数据点
  }

  // 合并用户提供的选项
  return mergeOptions(baseOptions, props.options)
}

// 深度合并选项
const mergeOptions = (target, source) => {
  const result = { ...target }
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeOptions(result[key] || {}, source[key])
    } else {
      result[key] = source[key]
    }
  }
  
  return result
}

// 创建图表
const createChart = async () => {
  if (!chartCanvas.value || !props.data) {
    return
  }

  try {
    isLoading.value = true
    error.value = null

    // 销毁现有图表
    if (chartInstance.value) {
      chartInstance.value.destroy()
    }

    // 等待DOM更新
    await nextTick()

    // 采样数据
    const sampledData = sampleData(props.data)

    // 创建新图表
    chartInstance.value = new Chart(chartCanvas.value, {
      type: props.type,
      data: sampledData,
      options: getOptimizedOptions()
    })

    emit('chart-created', chartInstance.value)
  } catch (err) {
    error.value = err
    console.error('Chart creation error:', err)
  } finally {
    isLoading.value = false
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance.value) {
    return
  }

  try {
    const sampledData = sampleData(props.data)
    
    // 更新数据
    chartInstance.value.data = sampledData
    chartInstance.value.options = getOptimizedOptions()
    
    // 使用动画更新
    chartInstance.value.update('active')
    
    emit('chart-updated', chartInstance.value)
  } catch (err) {
    error.value = err
    console.error('Chart update error:', err)
  }
}

// 重试渲染
const retryRender = () => {
  createChart()
}

// 刷新图表
const refreshChart = () => {
  updateChart()
}

// 下载图表
const downloadChart = () => {
  if (!chartInstance.value) {
    return
  }

  const link = document.createElement('a')
  link.download = `chart-${Date.now()}.png`
  link.href = chartInstance.value.toBase64Image()
  link.click()
}

// 全屏显示
const toggleFullscreen = () => {
  if (!chartContainer.value) {
    return
  }

  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    chartContainer.value.requestFullscreen()
  }
}

// 初始化ResizeObserver
const initResizeObserver = () => {
  if ('ResizeObserver' in window && chartContainer.value) {
    resizeObserver.value = new ResizeObserver(() => {
      if (chartInstance.value) {
        chartInstance.value.resize()
      }
    })
    
    resizeObserver.value.observe(chartContainer.value)
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 监听选项变化
watch(() => props.options, () => {
  updateChart()
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  chartInstance: computed(() => chartInstance.value),
  updateChart,
  downloadChart,
  toggleFullscreen,
  refreshChart
})

onMounted(() => {
  createChart()
  initResizeObserver()
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.destroy()
    emit('chart-destroyed')
  }
  
  if (resizeObserver.value) {
    resizeObserver.value.disconnect()
  }
})
</script>

<style scoped>
.optimized-chart-container {
  position: relative;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-loading,
.chart-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
}

.chart-canvas {
  display: block;
  width: 100%;
  height: 100%;
}

.chart-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.optimized-chart-container:hover .chart-toolbar {
  opacity: 1;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: white;
  color: #374151;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
