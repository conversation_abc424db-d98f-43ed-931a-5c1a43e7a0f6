<template>
  <div class="coverage-heatmap-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <div v-else-if="!hasData" class="chart-empty">
      <div class="empty-icon">
        <i class="fas fa-th"></i>
      </div>
      <p>暂无热力图数据</p>
    </div>
    
    <div v-else class="heatmap-container">
      <!-- 热力图标题和控制 -->
      <div class="heatmap-header">
        <h3 class="heatmap-title">{{ title }}</h3>
        <div class="heatmap-controls">
          <select v-model="selectedMetric" @change="updateHeatmap">
            <option value="line_coverage">行覆盖率</option>
            <option value="branch_coverage">分支覆盖率</option>
            <option value="function_coverage">函数覆盖率</option>
          </select>
        </div>
      </div>
      
      <!-- 热力图网格 -->
      <div class="heatmap-grid" :style="gridStyle">
        <div
          v-for="(cell, index) in heatmapCells"
          :key="index"
          class="heatmap-cell"
          :class="getCellClass(cell)"
          :style="getCellStyle(cell)"
          :title="getCellTooltip(cell)"
          @click="handleCellClick(cell)"
        >
          <div class="cell-content">
            <div class="cell-name">{{ cell.name }}</div>
            <div class="cell-value">{{ formatCoverageValue(cell.value) }}</div>
          </div>
        </div>
      </div>
      
      <!-- 热力图图例 -->
      <div class="heatmap-legend">
        <div class="legend-title">覆盖率等级</div>
        <div class="legend-items">
          <div
            v-for="level in coverageLevels"
            :key="level.key"
            class="legend-item"
          >
            <div
              class="legend-color"
              :style="{ backgroundColor: level.color }"
            ></div>
            <span class="legend-label">{{ level.label }}</span>
            <span class="legend-range">{{ level.range }}</span>
          </div>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="heatmap-stats">
        <div class="stat-item">
          <span class="stat-label">总文件数:</span>
          <span class="stat-value">{{ totalFiles }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均覆盖率:</span>
          <span class="stat-value">{{ formatCoverageValue(averageCoverage) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最高覆盖率:</span>
          <span class="stat-value">{{ formatCoverageValue(maxCoverage) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最低覆盖率:</span>
          <span class="stat-value">{{ formatCoverageValue(minCoverage) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { useCoverageStore } from '@/stores/coverage'

export default {
  name: 'CoverageHeatmapChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '覆盖率热力图'
    },
    gridColumns: {
      type: Number,
      default: 8
    }
  },
  emits: ['cell-click'],
  setup(props, { emit }) {
    const coverageStore = useCoverageStore()
    const selectedMetric = ref('line_coverage')
    
    // 计算属性
    const hasData = computed(() => {
      return props.data && props.data.length > 0
    })
    
    const heatmapCells = computed(() => {
      if (!hasData.value) return []
      
      return props.data.map(item => ({
        id: item.id,
        name: item.file_name || item.name,
        path: item.file_path || item.path,
        value: item[selectedMetric.value] || 0,
        package: item.package_name || '',
        totalLines: item.total_lines || 0,
        coveredLines: item.covered_lines || 0,
        originalData: item
      }))
    })
    
    const gridStyle = computed(() => ({
      gridTemplateColumns: `repeat(${Math.min(props.gridColumns, heatmapCells.value.length)}, 1fr)`
    }))
    
    const totalFiles = computed(() => heatmapCells.value.length)
    
    const averageCoverage = computed(() => {
      if (heatmapCells.value.length === 0) return 0
      const total = heatmapCells.value.reduce((sum, cell) => sum + cell.value, 0)
      return total / heatmapCells.value.length
    })
    
    const maxCoverage = computed(() => {
      if (heatmapCells.value.length === 0) return 0
      return Math.max(...heatmapCells.value.map(cell => cell.value))
    })
    
    const minCoverage = computed(() => {
      if (heatmapCells.value.length === 0) return 0
      return Math.min(...heatmapCells.value.map(cell => cell.value))
    })
    
    // 覆盖率等级配置
    const coverageLevels = [
      {
        key: 'excellent',
        label: '优秀',
        range: '90-100%',
        color: '#10b981',
        min: 90,
        max: 100
      },
      {
        key: 'good',
        label: '良好',
        range: '80-89%',
        color: '#3b82f6',
        min: 80,
        max: 89
      },
      {
        key: 'fair',
        label: '一般',
        range: '70-79%',
        color: '#f59e0b',
        min: 70,
        max: 79
      },
      {
        key: 'poor',
        label: '较差',
        range: '60-69%',
        color: '#f97316',
        min: 60,
        max: 69
      },
      {
        key: 'critical',
        label: '危险',
        range: '0-59%',
        color: '#ef4444',
        min: 0,
        max: 59
      }
    ]
    
    // 方法
    const formatCoverageValue = (value) => {
      return coverageStore.formatCoverageValue(value)
    }
    
    const getCellClass = (cell) => {
      const level = getCoverageLevel(cell.value)
      return [`coverage-${level}`, 'heatmap-cell-interactive']
    }
    
    const getCellStyle = (cell) => {
      const level = getCoverageLevel(cell.value)
      const levelConfig = coverageLevels.find(l => l.key === level)
      const opacity = calculateOpacity(cell.value, level)
      
      return {
        backgroundColor: levelConfig ? levelConfig.color : '#e5e7eb',
        opacity: opacity
      }
    }
    
    const getCoverageLevel = (value) => {
      if (value >= 90) return 'excellent'
      if (value >= 80) return 'good'
      if (value >= 70) return 'fair'
      if (value >= 60) return 'poor'
      return 'critical'
    }
    
    const calculateOpacity = (value, level) => {
      const levelConfig = coverageLevels.find(l => l.key === level)
      if (!levelConfig) return 0.5
      
      // 在等级范围内计算透明度
      const range = levelConfig.max - levelConfig.min
      const position = value - levelConfig.min
      const normalizedPosition = range > 0 ? position / range : 0
      
      // 透明度范围：0.3 - 1.0
      return 0.3 + (normalizedPosition * 0.7)
    }
    
    const getCellTooltip = (cell) => {
      return [
        `文件: ${cell.name}`,
        `路径: ${cell.path}`,
        `${getMetricLabel()}: ${formatCoverageValue(cell.value)}`,
        `总行数: ${cell.totalLines}`,
        `覆盖行数: ${cell.coveredLines}`,
        cell.package ? `包名: ${cell.package}` : ''
      ].filter(Boolean).join('\n')
    }
    
    const getMetricLabel = () => {
      const metricLabels = {
        line_coverage: '行覆盖率',
        branch_coverage: '分支覆盖率',
        function_coverage: '函数覆盖率'
      }
      return metricLabels[selectedMetric.value] || '覆盖率'
    }
    
    const handleCellClick = (cell) => {
      emit('cell-click', {
        cell,
        metric: selectedMetric.value,
        data: cell.originalData
      })
    }
    
    const updateHeatmap = () => {
      // 触发重新计算
      console.log(`切换到${getMetricLabel()}视图`)
    }
    
    // 监听数据变化
    watch(
      () => props.data,
      () => {
        if (hasData.value) {
          console.log('热力图数据更新')
        }
      },
      { deep: true }
    )
    
    return {
      selectedMetric,
      hasData,
      heatmapCells,
      gridStyle,
      totalFiles,
      averageCoverage,
      maxCoverage,
      minCoverage,
      coverageLevels,
      formatCoverageValue,
      getCellClass,
      getCellStyle,
      getCellTooltip,
      handleCellClick,
      updateHeatmap
    }
  }
}
</script>

<style scoped>
.coverage-heatmap-chart {
  width: 100%;
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.heatmap-container {
  width: 100%;
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.heatmap-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.heatmap-controls select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.heatmap-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 20px;
  min-height: 200px;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 60px;
}

.heatmap-cell-interactive:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  border-color: rgba(255, 255, 255, 0.8);
}

.cell-content {
  text-align: center;
  color: white;
  font-size: 11px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  padding: 4px;
}

.cell-name {
  font-size: 10px;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.cell-value {
  font-size: 12px;
  font-weight: 600;
}

.heatmap-legend {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
}

.legend-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.legend-range {
  font-size: 12px;
  color: #6b7280;
}

.heatmap-stats {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: #f8fafc;
  border-radius: 6px;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .heatmap-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .heatmap-grid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr)) !important;
  }
  
  .heatmap-cell {
    min-height: 50px;
  }
  
  .cell-content {
    font-size: 10px;
  }
  
  .cell-name {
    font-size: 9px;
  }
  
  .cell-value {
    font-size: 10px;
  }
  
  .legend-items {
    flex-direction: column;
    gap: 8px;
  }
  
  .heatmap-stats {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .coverage-heatmap-chart {
    padding: 16px;
  }
  
  .heatmap-grid {
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr)) !important;
    gap: 2px;
  }
  
  .heatmap-cell {
    min-height: 40px;
  }
  
  .cell-content {
    font-size: 9px;
  }
  
  .cell-name {
    display: none; /* 在小屏幕上隐藏文件名 */
  }
  
  .cell-value {
    font-size: 9px;
  }
}
</style>
