<template>
  <div class="coverage-trend-chart">
    <div v-if="isLoading" class="chart-loading">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <div v-else-if="!hasData" class="chart-empty">
      <div class="empty-icon">
        <i class="fas fa-chart-line"></i>
      </div>
      <p>暂无覆盖率趋势数据</p>
    </div>
    
    <canvas
      v-else
      ref="chartCanvas"
      :style="{ height: height + 'px' }"
    ></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { Chart as ChartJS, Colors } from 'chart.js'
import { useCoverageStore } from '@/stores/coverage' // 引入 Store
import { getDefaultChartOptions, createSafeChartData } from '@/utils/chartConfig' // 复用工具函数

const props = defineProps({
  projectId: {
    type: [Number, String],
    required: true
  },
  height: {
    type: [Number, String],
    default: 300
  },
  config: {
    type: Object,
    default: () => ({
      title: '覆盖率趋势',
      dateRange: '30d', // 默认时间范围
      groupBy: 'day',   // 默认分组方式
      customDateStart: '', // 自定义开始日期
      customDateEnd: '',   // 自定义结束日期
      primaryColor: '#4ade80', // 默认主颜色 (一个绿色调)
      // 可以根据需要添加更多配置项，例如 showTrendLine, legendPosition 等
    })
  }
})

const coverageStore = useCoverageStore()
const chartCanvas = ref(null)
const chartInstance = ref(null)

const isLoading = computed(() => coverageStore.loading)
const trendData = computed(() => coverageStore.trendData)

const chartTitle = computed(() => props.config?.title || '覆盖率趋势分析')

// 处理从 store 获取的数据，转换为 Chart.js 格式
const processedChartData = computed(() => {
  if (!trendData.value || !trendData.value.labels || !trendData.value.datasets) {
    return {
      labels: [],
      datasets: []
    }
  }
  // 覆盖率通常只有一个数据集，但为了通用性，我们遍历 datasets
  // 并应用配置中的颜色
  const datasets = trendData.value.datasets.map((dataset, index) => ({
    ...dataset,
    borderColor: props.config?.primaryColor || dataset.borderColor || '#4ade80',
    backgroundColor: props.config?.primaryColor ? `${props.config.primaryColor}33` : (dataset.backgroundColor || '#4ade8033'), // 20% 透明度
    tension: 0.3,
    fill: true,
  }))

  return {
    labels: trendData.value.labels,
    datasets: datasets
  }
})

const hasData = computed(() => {
  return processedChartData.value?.labels?.length > 0 && processedChartData.value?.datasets?.length > 0
})

// 获取图表数据
const fetchCoverageData = async () => {
  if (!props.projectId) return

  const params = {
    project_id: props.projectId,
    group_by: props.config?.groupBy || 'day',
    date_range: props.config?.dateRange || '30d',
  }

  if (props.config?.dateRange === 'custom' && props.config?.customDateStart && props.config?.customDateEnd) {
    params.start_date = props.config.customDateStart
    params.end_date = props.config.customDateEnd
    delete params.date_range // 如果是自定义范围，则移除 date_range
  }
  
  try {
    await coverageStore.fetchTrendData(params)
  } catch (error) {
    console.error('获取覆盖率趋势数据失败:', error)
    // 可以在这里添加用户反馈，例如使用 toast 通知
  }
}

// 创建或更新图表
const createOrUpdateChart = () => {
  if (!chartCanvas.value || !hasData.value) {
    if (chartInstance.value) {
      chartInstance.value.destroy()
      chartInstance.value = null
    }
    return
  }

  const chartData = createSafeChartData(processedChartData.value, {
    labels: ['暂无数据'],
    datasets: [{
      label: '覆盖率',
      data: [0],
      borderColor: props.config?.primaryColor || '#e5e7eb',
      backgroundColor: props.config?.primaryColor ? `${props.config.primaryColor}1A` : 'rgba(229, 231, 235, 0.1)',
      tension: 0.3,
      fill: true
    }]
  })

  const options = {
    ...getDefaultChartOptions('line'), // 使用通用的默认配置
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: chartTitle.value, // 使用配置的标题
        font: { size: 16, weight: 'bold' },
        padding: { top: 10, bottom: 20 }
      },
      legend: {
        display: true,
        position: 'top',
        labels: { usePointStyle: true, padding: 20, font: { size: 12 } }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || ''
            const value = context.parsed.y
            return `${label}: ${value != null ? value.toFixed(1) : 'N/A'}%`
          }
        }
      },
      colors: { // 启用 Chart.js Colors 插件
        enabled: true
      }
    },
    scales: {
      x: {
        display: true,
        title: { display: true, text: '时间', font: { size: 12, weight: 'bold' } },
        grid: { display: true, color: 'rgba(0, 0, 0, 0.05)' },
        ticks: { font: { size: 11 }, maxTicksLimit: 10 }
      },
      y: {
        display: true,
        title: { display: true, text: '覆盖率 (%)', font: { size: 12, weight: 'bold' } },
        min: 0,
        max: 100, // 覆盖率通常在 0-100%
        grid: { display: true, color: 'rgba(0, 0, 0, 0.05)' },
        ticks: { font: { size: 11 }, callback: function(value) { return value + '%' } }
      }
    },
    elements: {
      point: { radius: 3, hoverRadius: 5, borderWidth: 1, hoverBorderWidth: 2 },
      line: { borderWidth: 2, tension: 0.3 }
    },
    animation: { duration: 800, easing: 'easeInOutQuart' }
  }

  if (chartInstance.value) {
    chartInstance.value.data = chartData
    chartInstance.value.options = options // 确保选项也更新
    chartInstance.value.update()
  } else {
    // 注册 Colors 插件 (如果尚未全局注册)
    if (!ChartJS.registry.plugins.get('colors')) {
        ChartJS.register(Colors);
    }
    chartInstance.value = new ChartJS(chartCanvas.value, {
      type: 'line',
      data: chartData,
      options: options
    })
  }
}

// 监听 projectId 和 config 的变化
watch(
  () => [props.projectId, props.config],
  () => {
    fetchCoverageData()
  },
  { deep: true, immediate: true } // 立即执行一次以在挂载时获取数据
)

// 监听加载状态和数据变化以更新图表
watch(
  [isLoading, processedChartData],
  () => {
    if (!isLoading.value) {
       // 使用 nextTick 或 setTimeout 确保 DOM 更新后再创建/更新图表
      setTimeout(createOrUpdateChart, 0);
    }
  },
  { deep: true }
)

onMounted(() => {
  // 初始加载时，如果数据已存在（例如从缓存或父组件快速提供），则尝试渲染
  if (!isLoading.value && hasData.value) {
    createOrUpdateChart()
  }
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.destroy()
    chartInstance.value = null
  }
})

</script>

<style scoped>
.coverage-trend-chart {
  position: relative;
  width: 100%;
}

.chart-loading,
.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px; /* 确保在加载或空状态时有最小高度 */
  height: var(--chart-height, 300px); /* 使用CSS变量或props.height */
  color: #6b7280; /* text-gray-500 */
  border: 1px dashed #d1d5db; /* border-gray-300 */
  border-radius: 8px; /* rounded-lg */
  background-color: #f9fafb; /* bg-gray-50 */
  padding: 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb; /* border-gray-200 */
  border-top: 3px solid var(--primary-color, #4ade80); /* 使用配置的主颜色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
  color: #9ca3af; /* text-gray-400 */
}

.chart-empty p {
  font-size: 16px;
  margin: 0;
  color: #4b5563; /* text-gray-600 */
}

canvas {
  max-width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-loading,
  .chart-empty {
    min-height: 200px;
    height: var(--chart-height-mobile, 250px);
  }
  
  .empty-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .chart-empty p {
    font-size: 14px;
  }
  .coverage-trend-chart :deep(canvas) { /* 确保 canvas 在父容器内正确缩放 */
    max-height: var(--chart-height-mobile, 250px);
  }
}
</style>
