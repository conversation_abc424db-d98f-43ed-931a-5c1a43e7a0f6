<template>
  <div class="chart-wrapper">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ChartJS, defaultChartOptions, validateChartData, createSafeChartData } from '@/utils/chartConfig'

export default {
  name: 'LineChart',
  props: {
    data: {
      type: Object,
      required: true,
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    width: {
      type: Number,
      default: 400,
    },
    height: {
      type: Number,
      default: 200,
    },
  },
  setup(props) {
    const chartCanvas = ref(null)
    let chartInstance = null
    
    const createChart = () => {
      if (!chartCanvas.value) {
        console.warn('LineChart: Canvas not available')
        return
      }

      try {
        // 销毁现有图表
        if (chartInstance) {
          chartInstance.destroy()
          chartInstance = null
        }

        // 创建安全的图表数据
        const safeData = createSafeChartData(props.data)

        // 合并配置选项
        const mergedOptions = {
          ...defaultChartOptions,
          ...props.options,
        }

        // 创建新图表
        chartInstance = new ChartJS(chartCanvas.value, {
          type: 'line',
          data: safeData,
          options: mergedOptions,
        })

        console.log('LineChart: Chart created successfully')
      } catch (error) {
        console.error('LineChart: Failed to create chart', error)
      }
    }
    
    const updateChart = () => {
      if (!chartInstance) {
        console.warn('LineChart: Cannot update chart - instance not available')
        return
      }

      try {
        const safeData = createSafeChartData(props.data)
        chartInstance.data = safeData
        chartInstance.update('none')
        console.log('LineChart: Chart updated successfully')
      } catch (error) {
        console.error('LineChart: Failed to update chart', error)
        // 如果更新失败，尝试重新创建
        createChart()
      }
    }

    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        console.log('LineChart: Data changed', newData)
        if (chartInstance) {
          updateChart()
        } else {
          createChart()
        }
      },
      { deep: true, immediate: false }
    )

    // 监听配置变化
    watch(
      () => props.options,
      (newOptions) => {
        console.log('LineChart: Options changed', newOptions)
        createChart()
      },
      { deep: true }
    )

    onMounted(() => {
      console.log('LineChart: Component mounted')
      // 使用nextTick确保DOM已完全渲染
      setTimeout(() => {
        if (chartCanvas.value) {
          createChart()
        }
      }, 50)
    })

    onUnmounted(() => {
      console.log('LineChart: Component unmounting')
      if (chartInstance) {
        try {
          chartInstance.destroy()
        } catch (error) {
          console.warn('LineChart: Error destroying chart', error)
        }
        chartInstance = null
      }
    })
    
    return {
      chartCanvas,
    }
  },
}
</script>

<style scoped>
.chart-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

canvas {
  max-width: 100%;
  height: auto;
}
</style>
