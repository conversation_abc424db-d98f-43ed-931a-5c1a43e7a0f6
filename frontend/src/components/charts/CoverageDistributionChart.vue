<template>
  <div class="coverage-distribution-chart">
    <div v-if="loading" class="chart-loading">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <div v-else-if="!hasData" class="chart-empty">
      <div class="empty-icon">
        <i class="fas fa-chart-pie"></i>
      </div>
      <p>暂无分布数据</p>
    </div>
    
    <div v-else class="chart-container">
      <canvas
        ref="chartCanvas"
        :style="{ height: height + 'px' }"
      ></canvas>
      
      <!-- 图例 -->
      <div class="chart-legend">
        <div
          v-for="(item, index) in legendData"
          :key="index"
          class="legend-item"
        >
          <div
            class="legend-color"
            :style="{ backgroundColor: item.color }"
          ></div>
          <span class="legend-label">{{ item.label }}</span>
          <span class="legend-value">{{ item.count }} ({{ item.percentage }}%)</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { Chart as ChartJS } from 'chart.js'
import { validateChartData, createSafeChartData, getDefaultChartOptions } from '@/utils/chartConfig'

export default {
  name: 'CoverageDistributionChart',
  props: {
    data: {
      type: Object,
      default: () => ({
        labels: [],
        datasets: []
      })
    },
    loading: {
      type: Boolean,
      default: false
    },
    height: {
      type: [Number, String],
      default: 300
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const chartCanvas = ref(null)
    const chartInstance = ref(null)
    
    // 计算属性
    const hasData = computed(() => {
      return props.data?.labels?.length > 0 && props.data?.datasets?.length > 0
    })
    
    const legendData = computed(() => {
      if (!hasData.value || !props.data.datasets[0]) return []
      
      const dataset = props.data.datasets[0]
      return props.data.labels.map((label, index) => ({
        label,
        count: dataset.data[index] || 0,
        percentage: calculatePercentage(dataset.data[index], dataset.data),
        color: dataset.backgroundColor[index] || '#e5e7eb'
      }))
    })
    
    // 计算百分比
    const calculatePercentage = (value, data) => {
      const total = data.reduce((sum, val) => sum + (val || 0), 0)
      return total > 0 ? Math.round((value / total) * 100) : 0
    }
    
    // 默认图表配置
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        title: {
          display: false
        },
        legend: {
          display: false // 使用自定义图例
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            label: function(context) {
              const label = context.label || ''
              const value = context.parsed
              const total = context.dataset.data.reduce((sum, val) => sum + val, 0)
              const percentage = total > 0 ? Math.round((value / total) * 100) : 0
              return `${label}: ${value} (${percentage}%)`
            }
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      },
      elements: {
        arc: {
          borderWidth: 2,
          borderColor: '#ffffff',
          hoverBorderWidth: 3
        }
      }
    }
    
    // 创建图表
    const createChart = () => {
      if (!chartCanvas.value || !hasData.value) return
      
      // 销毁现有图表
      if (chartInstance.value) {
        chartInstance.value.destroy()
        chartInstance.value = null
      }
      
      try {
        // 验证和处理数据
        const chartData = createSafeChartData(props.data, {
          labels: ['暂无数据'],
          datasets: [{
            data: [1],
            backgroundColor: ['#e5e7eb'],
            borderColor: ['#ffffff'],
            borderWidth: 2
          }]
        })
        
        // 合并配置选项
        const mergedOptions = {
          ...defaultOptions,
          ...getDefaultChartOptions('doughnut'),
          ...props.options
        }
        
        // 创建图表实例
        chartInstance.value = new ChartJS(chartCanvas.value, {
          type: 'doughnut',
          data: chartData,
          options: mergedOptions
        })
        
        console.log('覆盖率分布图表创建成功')
        
      } catch (error) {
        console.error('创建覆盖率分布图表失败:', error)
      }
    }
    
    // 更新图表数据
    const updateChart = () => {
      if (!chartInstance.value || !hasData.value) {
        createChart()
        return
      }
      
      try {
        const chartData = createSafeChartData(props.data)
        
        if (chartData) {
          chartInstance.value.data = chartData
          chartInstance.value.update('active')
          console.log('覆盖率分布图表数据更新成功')
        }
        
      } catch (error) {
        console.error('更新覆盖率分布图表失败:', error)
        // 如果更新失败，尝试重新创建
        createChart()
      }
    }
    
    // 销毁图表
    const destroyChart = () => {
      if (chartInstance.value) {
        chartInstance.value.destroy()
        chartInstance.value = null
        console.log('覆盖率分布图表已销毁')
      }
    }
    
    // 监听数据变化
    watch(
      () => props.data,
      (newData) => {
        if (newData && !props.loading) {
          updateChart()
        }
      },
      { deep: true }
    )
    
    // 监听加载状态
    watch(
      () => props.loading,
      (loading) => {
        if (!loading && hasData.value) {
          // 延迟创建图表，确保DOM已更新
          setTimeout(createChart, 100)
        }
      }
    )
    
    // 生命周期
    onMounted(() => {
      if (!props.loading && hasData.value) {
        setTimeout(createChart, 100)
      }
    })
    
    onUnmounted(() => {
      destroyChart()
    })
    
    return {
      chartCanvas,
      hasData,
      legendData
    }
  }
}
</script>

<style scoped>
.coverage-distribution-chart {
  position: relative;
  width: 100%;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #9ca3af;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.chart-empty p {
  font-size: 16px;
  margin: 0;
}

.chart-container {
  display: flex;
  align-items: center;
  gap: 24px;
}

canvas {
  max-width: 60%;
  flex-shrink: 0;
}

.chart-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f3f4f6;
}

.legend-item:last-child {
  border-bottom: none;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.legend-value {
  font-size: 13px;
  color: #6b7280;
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    flex-direction: column;
    gap: 16px;
  }
  
  canvas {
    max-width: 100%;
    height: 200px !important;
  }
  
  .chart-legend {
    width: 100%;
  }
  
  .legend-item {
    padding: 6px 0;
  }
  
  .legend-label {
    font-size: 13px;
  }
  
  .legend-value {
    font-size: 12px;
  }
  
  .chart-loading,
  .chart-empty {
    height: 250px;
  }
  
  .empty-icon {
    font-size: 36px;
    margin-bottom: 12px;
  }
  
  .chart-empty p {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .chart-container {
    gap: 12px;
  }
  
  .legend-item {
    gap: 6px;
    padding: 4px 0;
  }
  
  .legend-color {
    width: 12px;
    height: 12px;
  }
  
  .legend-label {
    font-size: 12px;
  }
  
  .legend-value {
    font-size: 11px;
  }
}
</style>
