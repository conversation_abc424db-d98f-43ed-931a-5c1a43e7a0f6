<template>
  <div class="defect-distribution-chart bg-white rounded-lg shadow p-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-900">缺陷分布统计</h3>
      <div class="flex space-x-2">
        <select
          v-model="selectedDimension"
          @change="handleDimensionChange"
          class="form-select text-sm border-gray-300 rounded-md focus:border-blue-500 focus:ring-blue-500"
        >
          <option value="severity">按严重程度</option>
          <option value="status">按状态</option>
          <option value="priority">按优先级</option>
          <option value="type">按类型</option>
        </select>
        <button
          @click="toggleChartType"
          class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
        >
          {{ chartType === 'doughnut' ? '柱状图' : '环形图' }}
        </button>
      </div>
    </div>

    <div class="chart-container" style="height: 400px;">
      <Doughnut
        v-if="chartData && !loading && chartType === 'doughnut'"
        :data="chartData"
        :options="doughnutOptions"
      />
      <Bar
        v-else-if="chartData && !loading && chartType === 'bar'"
        :data="barChartData"
        :options="barOptions"
      />
      <div v-else-if="loading" class="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
      <div v-else class="flex items-center justify-center h-full text-gray-500">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
          </svg>
          <p class="mt-2">暂无分布数据</p>
        </div>
      </div>
    </div>

    <!-- 分布统计表格 -->
    <div v-if="distributionData && distributionData.distribution" class="mt-6">
      <h4 class="text-md font-medium text-gray-900 mb-3">详细统计</h4>
      <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ getDimensionLabel(selectedDimension) }}
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                数量
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                占比
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(item, index) in distributionData.distribution" :key="index">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div 
                    class="w-3 h-3 rounded-full mr-2"
                    :style="{ backgroundColor: getItemColor(item.label, selectedDimension) }"
                  ></div>
                  <span class="text-sm font-medium text-gray-900">
                    {{ formatLabel(item.label) }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ item.count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ getPercentage(item.count) }}%
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Doughnut, Bar } from 'vue-chartjs'
import { useDefectStore } from '@/stores/defect'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'

const props = defineProps({
  projectId: {
    type: Number,
    default: null
  },
  height: {
    type: Number,
    default: 400
  }
})

const emit = defineEmits(['data-updated'])

const defectStore = useDefectStore()
const selectedDimension = ref('severity')
const chartType = ref('doughnut')

// 计算属性
const loading = computed(() => defectStore.loading.distribution)
const distributionData = computed(() => defectStore.distributionData)

const chartData = computed(() => {
  if (!distributionData.value) return null
  return defectStore.formatDistributionChartData(distributionData.value)
})

const barChartData = computed(() => {
  if (!chartData.value) return null
  
  return {
    labels: chartData.value.labels,
    datasets: [{
      label: '缺陷数量',
      data: chartData.value.datasets[0].data,
      backgroundColor: chartData.value.datasets[0].backgroundColor,
      borderColor: chartData.value.datasets[0].backgroundColor,
      borderWidth: 1
    }]
  }
})

const totalCount = computed(() => {
  if (!distributionData.value?.distribution) return 0
  return distributionData.value.distribution.reduce((sum, item) => sum + item.count, 0)
})

// 方法定义
const getDimensionLabel = (dimension) => {
  const labels = {
    severity: '严重程度',
    status: '状态',
    priority: '优先级',
    type: '类型'
  }
  return labels[dimension] || dimension
}

// 图表配置
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right',
      labels: {
        usePointStyle: true,
        padding: 20
      }
    },
    tooltip: {
      callbacks: {
        label: function(context) {
          const label = context.label || ''
          const value = context.parsed
          const total = context.dataset.data.reduce((a, b) => a + b, 0)
          const percentage = ((value / total) * 100).toFixed(1)
          return `${label}: ${value} (${percentage}%)`
        }
      }
    }
  },
  cutout: '50%'
}

const barOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      callbacks: {
        label: function(context) {
          const value = context.parsed.y
          const total = context.dataset.data.reduce((a, b) => a + b, 0)
          const percentage = ((value / total) * 100).toFixed(1)
          return `数量: ${value} (${percentage}%)`
        }
      }
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: getDimensionLabel(selectedDimension.value)
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '缺陷数量'
      },
      beginAtZero: true,
      ticks: {
        stepSize: 1
      }
    }
  }
}))

// 方法
const fetchDistributionData = async () => {
  try {
    await defectStore.fetchDistributionData({
      project_id: props.projectId,
      dimension: selectedDimension.value
    })
    emit('data-updated', distributionData.value)
  } catch (error) {
    console.error('获取缺陷分布数据失败:', error)
  }
}

const handleDimensionChange = () => {
  fetchDistributionData()
}

const toggleChartType = () => {
  chartType.value = chartType.value === 'doughnut' ? 'bar' : 'doughnut'
}

const formatLabel = (label) => {
  const labelMap = {
    // 严重程度
    critical: '严重',
    high: '高',
    medium: '中',
    low: '低',
    // 状态
    open: '打开',
    in_progress: '处理中',
    resolved: '已解决',
    closed: '已关闭',
    reopened: '重新打开',
    // 优先级
    urgent: '紧急',
    normal: '普通',
    // 类型
    bug: '缺陷',
    feature: '功能',
    improvement: '改进',
    task: '任务'
  }
  return labelMap[label] || label
}

const getItemColor = (label, dimension) => {
  if (dimension === 'severity') {
    return defectStore.getSeverityColor(label)
  } else if (dimension === 'status') {
    return defectStore.getStatusColor(label)
  }
  
  // 其他维度使用默认颜色
  const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899']
  const index = distributionData.value?.distribution.findIndex(item => item.label === label) || 0
  return colors[index % colors.length]
}

const getPercentage = (count) => {
  if (totalCount.value === 0) return 0
  return ((count / totalCount.value) * 100).toFixed(1)
}

// 监听项目ID变化
watch(() => props.projectId, () => {
  fetchDistributionData()
})

// 组件挂载时获取数据
onMounted(() => {
  fetchDistributionData()
})

// 暴露方法给父组件
defineExpose({
  refresh: fetchDistributionData
})
</script>

<style scoped>
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.chart-container {
  position: relative;
}
</style>
