<template>
  <div class="report-template-list">
    <div v-if="loading" class="text-center py-8">
      <LoadingSpinner text="加载中..." />
    </div>
    
    <div v-else-if="templates.length === 0" class="text-center py-8">
      <i class="fas fa-layer-group text-gray-300 text-4xl"></i>
      <p class="mt-2 text-gray-500">暂无报告模板</p>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div
        v-for="template in templates"
        :key="template.id"
        class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
        @click="selectTemplate(template)"
      >
        <div class="flex justify-between items-start mb-3">
          <div class="flex-1">
            <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
            <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
          </div>
          
          <div class="flex space-x-1 ml-2">
            <button
              @click.stop="editTemplate(template)"
              class="text-blue-600 hover:text-blue-900 text-sm"
            >
              <i class="fas fa-edit"></i>
            </button>
            
            <button
              @click.stop="duplicateTemplate(template)"
              class="text-green-600 hover:text-green-900 text-sm"
            >
              <i class="fas fa-copy"></i>
            </button>
            
            <button
              @click.stop="deleteTemplate(template.id)"
              class="text-red-600 hover:text-red-900 text-sm"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        
        <!-- 模板信息 -->
        <div class="space-y-2">
          <div class="flex items-center text-xs text-gray-600">
            <i class="fas fa-file mr-2"></i>
            <span>格式: {{ template.formats.join(', ').toUpperCase() }}</span>
          </div>
          
          <div class="flex items-center text-xs text-gray-600">
            <i class="fas fa-puzzle-piece mr-2"></i>
            <span>包含: {{ template.sections.length }} 个部分</span>
          </div>
          
          <div class="flex items-center text-xs text-gray-600">
            <i class="fas fa-download mr-2"></i>
            <span>使用次数: {{ template.usage_count || 0 }}</span>
          </div>
          
          <div class="flex items-center text-xs text-gray-600">
            <i class="fas fa-clock mr-2"></i>
            <span>更新: {{ formatDate(template.updated_at) }}</span>
          </div>
        </div>
        
        <!-- 预览部分 -->
        <div class="mt-3 pt-3 border-t border-gray-100">
          <div class="text-xs text-gray-500 mb-2">包含部分:</div>
          <div class="flex flex-wrap gap-1">
            <span
              v-for="section in template.sections.slice(0, 4)"
              :key="section"
              class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
            >
              {{ getSectionName(section) }}
            </span>
            <span
              v-if="template.sections.length > 4"
              class="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
            >
              +{{ template.sections.length - 4 }}
            </span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="mt-4 pt-3 border-t border-gray-100">
          <button
            @click.stop="useTemplate(template)"
            class="w-full bg-blue-600 text-white text-sm py-2 px-3 rounded hover:bg-blue-700 transition-colors"
          >
            <i class="fas fa-play mr-2"></i>
            使用模板
          </button>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div v-if="pagination.total_pages > 1" class="mt-6 flex justify-center">
      <div class="flex space-x-2">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="changePage(page)"
          class="px-3 py-1 rounded text-sm"
          :class="page === pagination.page 
            ? 'bg-blue-600 text-white' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
        >
          {{ page }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useReportStore } from '@/stores/report'
import { useNotificationStore } from '@/stores/notification'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'

const reportStore = useReportStore()
const notificationStore = useNotificationStore()

// 响应式数据
const loading = ref(false)
const templates = ref([])

const pagination = ref({
  page: 1,
  page_size: 12,
  total: 0,
  total_pages: 0
})

// 计算属性
const visiblePages = computed(() => {
  const total = pagination.value.total_pages
  const current = pagination.value.page
  const pages = []
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const fetchTemplates = async () => {
  loading.value = true
  try {
    // 模拟API调用 - 实际应该调用reportStore的方法
    const mockData = {
      templates: [
        {
          id: 1,
          name: '标准质量报告',
          description: '包含所有质量指标的标准报告模板',
          formats: ['excel', 'pdf'],
          sections: ['overview', 'defects', 'coverage', 'alerts', 'trends'],
          usage_count: 45,
          updated_at: '2025-06-01T10:00:00'
        },
        {
          id: 2,
          name: '缺陷分析报告',
          description: '专注于缺陷分析的报告模板',
          formats: ['excel', 'csv'],
          sections: ['overview', 'defects'],
          usage_count: 23,
          updated_at: '2025-05-28T15:30:00'
        },
        {
          id: 3,
          name: '覆盖率报告',
          description: '测试覆盖率专项报告模板',
          formats: ['excel'],
          sections: ['overview', 'coverage', 'trends'],
          usage_count: 18,
          updated_at: '2025-05-25T09:15:00'
        },
        {
          id: 4,
          name: '高管汇报',
          description: '适合高管查看的简化报告模板',
          formats: ['pdf'],
          sections: ['overview', 'trends'],
          usage_count: 12,
          updated_at: '2025-05-20T14:45:00'
        }
      ],
      total: 4,
      page: 1,
      page_size: 12,
      total_pages: 1
    }
    
    templates.value = mockData.templates
    pagination.value = {
      page: mockData.page,
      page_size: mockData.page_size,
      total: mockData.total,
      total_pages: mockData.total_pages
    }
  } catch (error) {
    notificationStore.showError('获取报告模板失败')
  } finally {
    loading.value = false
  }
}

const selectTemplate = (template) => {
  // 选择模板事件
  console.log('选择模板:', template)
}

const useTemplate = (template) => {
  // 使用模板生成报告
  console.log('使用模板:', template)
  notificationStore.showSuccess(`已应用模板: ${template.name}`)
}

const editTemplate = (template) => {
  // 编辑模板
  console.log('编辑模板:', template)
  notificationStore.showInfo('编辑功能开发中...')
}

const duplicateTemplate = (template) => {
  // 复制模板
  console.log('复制模板:', template)
  notificationStore.showSuccess(`已复制模板: ${template.name}`)
}

const deleteTemplate = async (templateId) => {
  if (!confirm('确定要删除这个报告模板吗？')) return
  
  try {
    // 调用API删除模板
    console.log('删除模板:', templateId)
    notificationStore.showSuccess('报告模板已删除')
    await fetchTemplates()
  } catch (error) {
    notificationStore.showError('删除报告模板失败')
  }
}

const changePage = (page) => {
  pagination.value.page = page
  fetchTemplates()
}

// 工具方法
const getSectionName = (sectionId) => {
  const sectionNames = {
    'overview': '概览',
    'defects': '缺陷',
    'coverage': '覆盖率',
    'alerts': '预警',
    'trends': '趋势',
    'performance': '性能',
    'automation': '自动化'
  }
  return sectionNames[sectionId] || sectionId
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchTemplates()
})
</script>

<style scoped>
.report-template-list {
  /* 样式已在模板中使用 Tailwind CSS */
}
</style>
