<template>
  <div class="report-generator">
    <div class="bg-white rounded-lg shadow">
      <!-- 头部 -->
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-900">质量报告生成</h2>
        <p class="mt-1 text-sm text-gray-600">生成项目质量分析报告，支持多种格式导出</p>
      </div>

      <!-- 报告配置表单 -->
      <div class="p-6">
        <form @submit.prevent="generateReport" class="space-y-6">
          <!-- 项目选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              选择项目 <span class="text-red-500">*</span>
            </label>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div
                v-for="project in projects"
                :key="project.id"
                class="flex items-center"
              >
                <input
                  :id="`project-${project.id}`"
                  v-model="selectedProjects"
                  :value="project.id"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label
                  :for="`project-${project.id}`"
                  class="ml-2 text-sm text-gray-900"
                >
                  {{ project.name }}
                </label>
              </div>
            </div>
            <p v-if="selectedProjects.length === 0" class="mt-1 text-sm text-red-600">
              请至少选择一个项目
            </p>
          </div>

          <!-- 日期范围 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              日期范围
            </label>
            <select
              v-model="dateRange"
              class="form-select w-full md:w-48"
            >
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
              <option value="1y">最近1年</option>
            </select>
          </div>

          <!-- 报告格式 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              报告格式
            </label>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div
                v-for="format in supportedFormats"
                :key="format.id"
                class="relative"
              >
                <input
                  :id="`format-${format.id}`"
                  v-model="selectedFormat"
                  :value="format.id"
                  type="radio"
                  class="sr-only"
                  :disabled="format.status === '开发中'"
                >
                <label
                  :for="`format-${format.id}`"
                  class="flex items-center p-4 border rounded-lg cursor-pointer transition-colors"
                  :class="{
                    'border-blue-500 bg-blue-50': selectedFormat === format.id,
                    'border-gray-300 hover:border-gray-400': selectedFormat !== format.id && format.status !== '开发中',
                    'border-gray-200 bg-gray-50 cursor-not-allowed': format.status === '开发中'
                  }"
                >
                  <div class="flex-1">
                    <div class="flex items-center">
                      <span class="font-medium text-gray-900">{{ format.name }}</span>
                      <span
                        v-if="format.status === '开发中'"
                        class="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded"
                      >
                        开发中
                      </span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">{{ format.description }}</p>
                    <div class="flex flex-wrap gap-1 mt-2">
                      <span
                        v-for="feature in format.features"
                        :key="feature"
                        class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                      >
                        {{ feature }}
                      </span>
                    </div>
                  </div>
                </label>
              </div>
            </div>
          </div>

          <!-- 报告内容 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              报告内容
            </label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div
                v-for="section in availableSections"
                :key="section.id"
                class="flex items-center"
              >
                <input
                  :id="`section-${section.id}`"
                  v-model="selectedSections"
                  :value="section.id"
                  type="checkbox"
                  :disabled="section.required"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label
                  :for="`section-${section.id}`"
                  class="ml-2 text-sm"
                  :class="{
                    'text-gray-900': !section.required,
                    'text-gray-600': section.required
                  }"
                >
                  {{ section.name }}
                  <span v-if="section.required" class="text-red-500">*</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 通知邮箱 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              通知邮箱（可选）
            </label>
            <input
              v-model="notificationEmail"
              type="email"
              placeholder="<EMAIL>"
              class="form-input w-full md:w-96"
            >
            <p class="mt-1 text-sm text-gray-500">
              报告生成完成后将发送邮件通知
            </p>
          </div>

          <!-- 操作按钮 -->
          <div class="flex flex-col sm:flex-row gap-3">
            <button
              type="submit"
              :disabled="!canGenerate || loading.generate"
              class="btn btn-primary"
            >
              <i
                class="fas fa-download mr-2"
                :class="{ 'animate-spin fa-spinner': loading.generate }"
              ></i>
              {{ loading.generate ? '生成中...' : '立即下载' }}
            </button>

            <button
              type="button"
              @click="generateAsync"
              :disabled="!canGenerate || loading.async"
              class="btn btn-secondary"
            >
              <i
                class="fas fa-clock mr-2"
                :class="{ 'animate-spin fa-spinner': loading.async }"
              ></i>
              {{ loading.async ? '提交中...' : '异步生成' }}
            </button>

            <button
              type="button"
              @click="resetForm"
              class="btn btn-outline"
            >
              <i class="fas fa-undo mr-2"></i>
              重置
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 报告模板 -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">报告模板</h3>
        <p class="mt-1 text-sm text-gray-600">使用预定义模板快速生成报告</p>
      </div>

      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div
            v-for="template in reportTemplates"
            :key="template.id"
            class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors cursor-pointer"
            @click="applyTemplate(template)"
          >
            <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
            <p class="text-sm text-gray-600 mt-1">{{ template.description }}</p>
            <div class="flex flex-wrap gap-1 mt-3">
              <span
                v-for="format in template.formats"
                :key="format"
                class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
              >
                {{ format.toUpperCase() }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 报告历史 -->
    <div class="mt-8 bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">最近生成的报告</h3>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                报告名称
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                格式
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                项目数
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                文件大小
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                生成时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="report in recentReports" :key="report.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ report.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">
                  {{ report.format.toUpperCase() }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ report.project_count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ report.file_size }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(report.generated_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button class="text-blue-600 hover:text-blue-900">
                  下载
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProjectStore } from '@/stores/project'
import { useReportStore } from '@/stores/report'
import { useNotificationStore } from '@/stores/notification'

const projectStore = useProjectStore()
const reportStore = useReportStore()
const notificationStore = useNotificationStore()

// 表单数据
const selectedProjects = ref([])
const dateRange = ref('30d')
const selectedFormat = ref('excel')
const selectedSections = ref(['overview', 'defects', 'coverage', 'alerts'])
const notificationEmail = ref('')

// 加载状态
const loading = ref({
  generate: false,
  async: false
})

// 计算属性
const projects = computed(() => projectStore.projects)
const supportedFormats = computed(() => reportStore.supportedFormats)
const availableSections = computed(() => reportStore.availableSections)
const reportTemplates = computed(() => reportStore.templates)
const recentReports = computed(() => reportStore.recentReports)

const canGenerate = computed(() => {
  return selectedProjects.value.length > 0 && selectedFormat.value && selectedSections.value.length > 0
})

// 方法
const generateReport = async () => {
  if (!canGenerate.value) return

  loading.value.generate = true
  try {
    await reportStore.downloadReport({
      project_ids: selectedProjects.value.join(','),
      date_range: dateRange.value,
      format: selectedFormat.value,
      include_sections: selectedSections.value.join(',')
    })
    
    notificationStore.showSuccess('报告下载成功')
  } catch (error) {
    notificationStore.showError('报告生成失败')
  } finally {
    loading.value.generate = false
  }
}

const generateAsync = async () => {
  if (!canGenerate.value) return

  loading.value.async = true
  try {
    await reportStore.generateReportAsync({
      project_ids: selectedProjects.value,
      date_range: dateRange.value,
      format: selectedFormat.value,
      include_sections: selectedSections.value,
      notification_email: notificationEmail.value || undefined
    })
    
    notificationStore.showSuccess('报告生成任务已提交，完成后将通过邮件通知')
  } catch (error) {
    notificationStore.showError('提交报告生成任务失败')
  } finally {
    loading.value.async = false
  }
}

const applyTemplate = (template) => {
  selectedSections.value = [...template.sections]
  selectedFormat.value = template.formats[0]
  notificationStore.showInfo(`已应用模板: ${template.name}`)
}

const resetForm = () => {
  selectedProjects.value = []
  dateRange.value = '30d'
  selectedFormat.value = 'excel'
  selectedSections.value = ['overview', 'defects', 'coverage', 'alerts']
  notificationEmail.value = ''
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    projectStore.fetchProjects(),
    reportStore.fetchSupportedFormats(),
    reportStore.fetchAvailableSections(),
    reportStore.fetchTemplates(),
    reportStore.fetchStats()
  ])
})
</script>

<style scoped>
.report-generator {
  padding: 24px;
}
</style>
