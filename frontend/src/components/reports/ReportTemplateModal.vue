<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- 标题 -->
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ template ? '编辑报告模板' : '新建报告模板' }}
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 表单 -->
        <form @submit.prevent="saveTemplate" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                模板名称 <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入模板名称"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                模板类型
              </label>
              <select
                v-model="formData.type"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="standard">标准模板</option>
                <option value="custom">自定义模板</option>
                <option value="executive">高管模板</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              模板描述
            </label>
            <textarea
              v-model="formData.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入模板描述"
            ></textarea>
          </div>

          <!-- 支持格式 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">支持格式</h4>
            
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input
                  v-model="formData.formats"
                  value="excel"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-2 text-sm text-gray-700">Excel</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="formData.formats"
                  value="csv"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-2 text-sm text-gray-700">CSV</span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="formData.formats"
                  value="pdf"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-2 text-sm text-gray-700">PDF</span>
              </label>
            </div>
          </div>

          <!-- 报告内容 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">报告内容</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div
                v-for="section in availableSections"
                :key="section.id"
                class="flex items-center justify-between p-3 border border-gray-200 rounded"
              >
                <div class="flex items-center">
                  <input
                    v-model="formData.sections"
                    :value="section.id"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ section.name }}</div>
                    <div class="text-xs text-gray-500">{{ section.description }}</div>
                  </div>
                </div>
                
                <div v-if="formData.sections.includes(section.id)" class="flex items-center space-x-2">
                  <label class="text-xs text-gray-500">
                    必需:
                    <input
                      v-model="formData.required_sections"
                      :value="section.id"
                      type="checkbox"
                      class="ml-1 h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- 模板配置 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">模板配置</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  默认日期范围
                </label>
                <select
                  v-model="formData.default_date_range"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="7d">最近7天</option>
                  <option value="30d">最近30天</option>
                  <option value="90d">最近90天</option>
                  <option value="1y">最近1年</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  默认格式
                </label>
                <select
                  v-model="formData.default_format"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                  <option value="pdf">PDF</option>
                </select>
              </div>
            </div>

            <div class="mt-4">
              <label class="flex items-center">
                <input
                  v-model="formData.is_public"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-2 text-sm text-gray-700">公开模板（其他用户可使用）</span>
              </label>
            </div>
          </div>

          <!-- 预览 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">模板预览</h4>
            
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="text-sm text-gray-600 mb-2">
                <strong>{{ formData.name || '未命名模板' }}</strong>
              </div>
              <div class="text-xs text-gray-500 mb-3">
                {{ formData.description || '暂无描述' }}
              </div>
              
              <div class="flex items-center space-x-4 text-xs text-gray-600">
                <span>
                  <i class="fas fa-file mr-1"></i>
                  格式: {{ formData.formats.join(', ').toUpperCase() || '未选择' }}
                </span>
                <span>
                  <i class="fas fa-puzzle-piece mr-1"></i>
                  部分: {{ formData.sections.length }} 个
                </span>
                <span>
                  <i class="fas fa-calendar mr-1"></i>
                  范围: {{ getDateRangeText(formData.default_date_range) }}
                </span>
              </div>
              
              <div v-if="formData.sections.length > 0" class="mt-3">
                <div class="text-xs text-gray-500 mb-1">包含部分:</div>
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="sectionId in formData.sections"
                    :key="sectionId"
                    class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                  >
                    {{ getSectionName(sectionId) }}
                    <span v-if="formData.required_sections.includes(sectionId)" class="ml-1 text-red-600">*</span>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 按钮 -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="loading || !canSave"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              {{ template ? '更新' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useReportStore } from '@/stores/report'

const props = defineProps({
  template: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'saved'])

const reportStore = useReportStore()

// 响应式数据
const loading = ref(false)

const formData = ref({
  name: '',
  description: '',
  type: 'standard',
  formats: ['excel'],
  sections: ['overview'],
  required_sections: [],
  default_date_range: '30d',
  default_format: 'excel',
  is_public: false
})

const availableSections = ref([
  { id: 'overview', name: '概览', description: '项目基本信息和关键指标' },
  { id: 'defects', name: '缺陷分析', description: '缺陷统计和趋势分析' },
  { id: 'coverage', name: '覆盖率', description: '测试覆盖率数据和分析' },
  { id: 'alerts', name: '预警', description: '质量预警和异常情况' },
  { id: 'trends', name: '趋势', description: '质量指标趋势分析' },
  { id: 'performance', name: '性能', description: '性能指标和监控数据' },
  { id: 'automation', name: '自动化', description: '自动化测试执行情况' }
])

// 计算属性
const canSave = computed(() => {
  return formData.value.name.trim() && 
         formData.value.formats.length > 0 && 
         formData.value.sections.length > 0
})

// 监听器
watch(() => props.template, (newTemplate) => {
  if (newTemplate) {
    formData.value = { ...newTemplate }
  }
}, { immediate: true })

// 方法
const saveTemplate = async () => {
  loading.value = true
  try {
    const templateData = { ...formData.value }

    // 这里应该调用API保存模板
    console.log('保存报告模板:', templateData)
    
    emit('saved')
  } catch (error) {
    console.error('保存报告模板失败:', error)
  } finally {
    loading.value = false
  }
}

const getSectionName = (sectionId) => {
  const section = availableSections.value.find(s => s.id === sectionId)
  return section ? section.name : sectionId
}

const getDateRangeText = (range) => {
  const rangeTexts = {
    '7d': '最近7天',
    '30d': '最近30天',
    '90d': '最近90天',
    '1y': '最近1年'
  }
  return rangeTexts[range] || range
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped>
/* 模态框样式已在模板中使用 Tailwind CSS */
</style>
