<template>
  <div class="scheduled-report-list">
    <div v-if="loading" class="text-center py-8">
      <LoadingSpinner text="加载中..." />
    </div>
    
    <div v-else-if="scheduledReports.length === 0" class="text-center py-8">
      <i class="fas fa-calendar-alt text-gray-300 text-4xl"></i>
      <p class="mt-2 text-gray-500">暂无定时报告任务</p>
    </div>
    
    <div v-else class="space-y-4">
      <div
        v-for="report in scheduledReports"
        :key="report.id"
        class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
      >
        <div class="flex justify-between items-start">
          <div class="flex-1">
            <h4 class="font-medium text-gray-900">{{ report.name }}</h4>
            <p class="text-sm text-gray-500 mt-1">{{ report.description }}</p>
            
            <div class="flex items-center mt-3 space-x-4">
              <span class="text-xs px-2 py-1 rounded-full"
                    :class="getStatusClass(report.status)">
                {{ getStatusText(report.status) }}
              </span>
              
              <span class="text-xs text-gray-500">
                <i class="fas fa-clock mr-1"></i>
                {{ report.schedule }}
              </span>
              
              <span class="text-xs text-gray-500">
                <i class="fas fa-file mr-1"></i>
                {{ report.format.toUpperCase() }}
              </span>
              
              <span class="text-xs text-gray-500">
                <i class="fas fa-project-diagram mr-1"></i>
                {{ report.project_count }} 个项目
              </span>
            </div>
            
            <div class="mt-2 text-xs text-gray-500">
              下次执行: {{ formatDate(report.next_run_at) }}
            </div>
          </div>
          
          <div class="flex space-x-2 ml-4">
            <button
              @click="editReport(report)"
              class="text-blue-600 hover:text-blue-900 text-sm"
            >
              <i class="fas fa-edit mr-1"></i>
              编辑
            </button>
            
            <button
              v-if="report.status === 'active'"
              @click="pauseReport(report.id)"
              class="text-yellow-600 hover:text-yellow-900 text-sm"
            >
              <i class="fas fa-pause mr-1"></i>
              暂停
            </button>
            
            <button
              v-if="report.status === 'paused'"
              @click="resumeReport(report.id)"
              class="text-green-600 hover:text-green-900 text-sm"
            >
              <i class="fas fa-play mr-1"></i>
              恢复
            </button>
            
            <button
              @click="deleteReport(report.id)"
              class="text-red-600 hover:text-red-900 text-sm"
            >
              <i class="fas fa-trash mr-1"></i>
              删除
            </button>
          </div>
        </div>
        
        <!-- 执行历史 -->
        <div v-if="report.recent_executions && report.recent_executions.length > 0" 
             class="mt-4 pt-4 border-t border-gray-100">
          <h5 class="text-sm font-medium text-gray-700 mb-2">最近执行记录</h5>
          <div class="space-y-1">
            <div
              v-for="execution in report.recent_executions.slice(0, 3)"
              :key="execution.id"
              class="flex justify-between items-center text-xs"
            >
              <span class="text-gray-600">
                {{ formatDate(execution.executed_at) }}
              </span>
              <span class="px-2 py-1 rounded-full"
                    :class="getExecutionStatusClass(execution.status)">
                {{ getExecutionStatusText(execution.status) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div v-if="pagination.total_pages > 1" class="mt-6 flex justify-center">
      <div class="flex space-x-2">
        <button
          v-for="page in visiblePages"
          :key="page"
          @click="changePage(page)"
          class="px-3 py-1 rounded text-sm"
          :class="page === pagination.page 
            ? 'bg-blue-600 text-white' 
            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'"
        >
          {{ page }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useReportStore } from '@/stores/report'
import { useNotificationStore } from '@/stores/notification'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'

const reportStore = useReportStore()
const notificationStore = useNotificationStore()

// 响应式数据
const loading = ref(false)
const scheduledReports = ref([])

const pagination = ref({
  page: 1,
  page_size: 10,
  total: 0,
  total_pages: 0
})

// 计算属性
const visiblePages = computed(() => {
  const total = pagination.value.total_pages
  const current = pagination.value.page
  const pages = []
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const fetchScheduledReports = async () => {
  loading.value = true
  try {
    // 模拟API调用 - 实际应该调用reportStore的方法
    const mockData = {
      reports: [
        {
          id: 1,
          name: '每日质量报告',
          description: '每日自动生成的质量概览报告',
          schedule: '每天 09:00',
          format: 'excel',
          status: 'active',
          project_count: 5,
          next_run_at: '2025-06-06T09:00:00',
          recent_executions: [
            { id: 1, executed_at: '2025-06-05T09:00:00', status: 'success' },
            { id: 2, executed_at: '2025-06-04T09:00:00', status: 'success' },
            { id: 3, executed_at: '2025-06-03T09:00:00', status: 'failed' }
          ]
        },
        {
          id: 2,
          name: '周度缺陷分析',
          description: '每周生成的缺陷趋势分析报告',
          schedule: '每周一 10:00',
          format: 'pdf',
          status: 'paused',
          project_count: 3,
          next_run_at: '2025-06-09T10:00:00',
          recent_executions: [
            { id: 4, executed_at: '2025-06-02T10:00:00', status: 'success' }
          ]
        }
      ],
      total: 2,
      page: 1,
      page_size: 10,
      total_pages: 1
    }
    
    scheduledReports.value = mockData.reports
    pagination.value = {
      page: mockData.page,
      page_size: mockData.page_size,
      total: mockData.total,
      total_pages: mockData.total_pages
    }
  } catch (error) {
    notificationStore.showError('获取定时报告列表失败')
  } finally {
    loading.value = false
  }
}

const editReport = (report) => {
  // 触发编辑事件
  console.log('编辑定时报告:', report)
  notificationStore.showInfo('编辑功能开发中...')
}

const pauseReport = async (reportId) => {
  try {
    // 调用API暂停报告
    console.log('暂停报告:', reportId)
    notificationStore.showSuccess('定时报告已暂停')
    await fetchScheduledReports()
  } catch (error) {
    notificationStore.showError('暂停定时报告失败')
  }
}

const resumeReport = async (reportId) => {
  try {
    // 调用API恢复报告
    console.log('恢复报告:', reportId)
    notificationStore.showSuccess('定时报告已恢复')
    await fetchScheduledReports()
  } catch (error) {
    notificationStore.showError('恢复定时报告失败')
  }
}

const deleteReport = async (reportId) => {
  if (!confirm('确定要删除这个定时报告任务吗？')) return
  
  try {
    // 调用API删除报告
    console.log('删除报告:', reportId)
    notificationStore.showSuccess('定时报告已删除')
    await fetchScheduledReports()
  } catch (error) {
    notificationStore.showError('删除定时报告失败')
  }
}

const changePage = (page) => {
  pagination.value.page = page
  fetchScheduledReports()
}

// 工具方法
const getStatusClass = (status) => {
  const classes = {
    'active': 'bg-green-100 text-green-800',
    'paused': 'bg-yellow-100 text-yellow-800',
    'disabled': 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    'active': '运行中',
    'paused': '已暂停',
    'disabled': '已禁用'
  }
  return texts[status] || status
}

const getExecutionStatusClass = (status) => {
  const classes = {
    'success': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800',
    'running': 'bg-blue-100 text-blue-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getExecutionStatusText = (status) => {
  const texts = {
    'success': '成功',
    'failed': '失败',
    'running': '运行中'
  }
  return texts[status] || status
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  fetchScheduledReports()
})
</script>

<style scoped>
.scheduled-report-list {
  /* 样式已在模板中使用 Tailwind CSS */
}
</style>
