<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- 标题 -->
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900">
            新建定时报告
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 表单 -->
        <form @submit.prevent="saveSchedule" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                任务名称 <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入任务名称"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                报告格式 <span class="text-red-500">*</span>
              </label>
              <select
                v-model="formData.format"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择格式</option>
                <option value="excel">Excel</option>
                <option value="csv">CSV</option>
                <option value="pdf">PDF</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              任务描述
            </label>
            <textarea
              v-model="formData.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入任务描述"
            ></textarea>
          </div>

          <!-- 调度配置 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">调度配置</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  执行频率 <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="formData.frequency"
                  required
                  @change="updateScheduleOptions"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择频率</option>
                  <option value="daily">每日</option>
                  <option value="weekly">每周</option>
                  <option value="monthly">每月</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  执行时间 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model="formData.time"
                  type="time"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
              </div>
            </div>

            <div v-if="formData.frequency === 'weekly'" class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                执行日期 <span class="text-red-500">*</span>
              </label>
              <div class="flex space-x-2">
                <label
                  v-for="(day, index) in weekDays"
                  :key="index"
                  class="flex items-center"
                >
                  <input
                    v-model="formData.weekdays"
                    :value="index"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-1 text-sm text-gray-700">{{ day }}</span>
                </label>
              </div>
            </div>

            <div v-if="formData.frequency === 'monthly'" class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                执行日期 <span class="text-red-500">*</span>
              </label>
              <select
                v-model="formData.monthday"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择日期</option>
                <option v-for="day in 31" :key="day" :value="day">
                  {{ day }} 日
                </option>
                <option value="last">月末</option>
              </select>
            </div>
          </div>

          <!-- 项目选择 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">项目选择</h4>
            
            <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded p-3">
              <label class="flex items-center">
                <input
                  v-model="selectAllProjects"
                  type="checkbox"
                  @change="toggleAllProjects"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-2 text-sm font-medium text-gray-700">全选</span>
              </label>
              
              <div class="border-t pt-2">
                <label
                  v-for="project in projects"
                  :key="project.id"
                  class="flex items-center"
                >
                  <input
                    v-model="formData.project_ids"
                    :value="project.id"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-sm text-gray-700">{{ project.name }}</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 报告内容 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">报告内容</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <label
                v-for="section in availableSections"
                :key="section.id"
                class="flex items-center"
              >
                <input
                  v-model="formData.sections"
                  :value="section.id"
                  type="checkbox"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-2 text-sm text-gray-700">{{ section.name }}</span>
              </label>
            </div>
          </div>

          <!-- 通知配置 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">通知配置</h4>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                通知邮箱
              </label>
              <input
                v-model="formData.notification_email"
                type="email"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="多个邮箱用逗号分隔"
              >
            </div>
          </div>

          <!-- 按钮 -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              创建任务
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProjectStore } from '@/stores/project'
import { useReportStore } from '@/stores/report'

const emit = defineEmits(['close', 'saved'])

const projectStore = useProjectStore()
const reportStore = useReportStore()

// 响应式数据
const loading = ref(false)
const selectAllProjects = ref(false)

const formData = ref({
  name: '',
  description: '',
  format: '',
  frequency: '',
  time: '09:00',
  weekdays: [],
  monthday: '',
  project_ids: [],
  sections: ['overview'],
  notification_email: ''
})

const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

const availableSections = ref([
  { id: 'overview', name: '概览' },
  { id: 'defects', name: '缺陷分析' },
  { id: 'coverage', name: '覆盖率' },
  { id: 'alerts', name: '预警' },
  { id: 'trends', name: '趋势' },
  { id: 'performance', name: '性能' }
])

// 计算属性
const projects = computed(() => projectStore.projects)

// 方法
const updateScheduleOptions = () => {
  // 重置相关字段
  formData.value.weekdays = []
  formData.value.monthday = ''
}

const toggleAllProjects = () => {
  if (selectAllProjects.value) {
    formData.value.project_ids = projects.value.map(p => p.id)
  } else {
    formData.value.project_ids = []
  }
}

const saveSchedule = async () => {
  loading.value = true
  try {
    // 构建调度表达式
    let schedule = ''
    if (formData.value.frequency === 'daily') {
      schedule = `每天 ${formData.value.time}`
    } else if (formData.value.frequency === 'weekly') {
      const days = formData.value.weekdays.map(d => weekDays[d]).join('、')
      schedule = `每周${days} ${formData.value.time}`
    } else if (formData.value.frequency === 'monthly') {
      const day = formData.value.monthday === 'last' ? '月末' : `${formData.value.monthday}日`
      schedule = `每月${day} ${formData.value.time}`
    }

    const scheduleData = {
      ...formData.value,
      schedule
    }

    // 这里应该调用API保存定时任务
    console.log('保存定时报告:', scheduleData)
    
    emit('saved')
  } catch (error) {
    console.error('保存定时报告失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  await projectStore.fetchProjects()
})
</script>

<style scoped>
/* 模态框样式已在模板中使用 Tailwind CSS */
</style>
