<template>
  <div class="bg-white rounded-lg shadow overflow-hidden">
    <!-- 表格头部 -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <div class="relative" v-if="searchable">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索..."
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
            <MagnifyingGlassIcon class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
          
          <!-- 筛选器 -->
          <select
            v-if="filters.length > 0"
            v-model="selectedFilter"
            class="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">全部</option>
            <option v-for="filter in filters" :key="filter.value" :value="filter.value">
              {{ filter.label }}
            </option>
          </select>
          
          <!-- 导出按钮 -->
          <button
            v-if="exportable"
            @click="exportData"
            class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowDownTrayIcon class="h-4 w-4 mr-2" />
            导出
          </button>
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              @click="handleSort(column.key)"
              :class="[
                'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
              ]"
            >
              <div class="flex items-center space-x-1">
                <span>{{ column.title }}</span>
                <div v-if="column.sortable" class="flex flex-col">
                  <ChevronUpIcon 
                    :class="[
                      'h-3 w-3',
                      sortBy === column.key && sortOrder === 'asc' ? 'text-blue-500' : 'text-gray-400'
                    ]"
                  />
                  <ChevronDownIcon 
                    :class="[
                      'h-3 w-3 -mt-1',
                      sortBy === column.key && sortOrder === 'desc' ? 'text-blue-500' : 'text-gray-400'
                    ]"
                  />
                </div>
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="loading" class="animate-pulse">
            <td :colspan="columns.length" class="px-6 py-4 text-center text-gray-500">
              <LoadingSpinner class="inline-block" />
              加载中...
            </td>
          </tr>
          <tr v-else-if="filteredData.length === 0">
            <td :colspan="columns.length" class="px-6 py-4 text-center text-gray-500">
              暂无数据
            </td>
          </tr>
          <tr 
            v-else
            v-for="(item, index) in paginatedData" 
            :key="item.id || index"
            class="hover:bg-gray-50"
          >
            <td 
              v-for="column in columns" 
              :key="column.key"
              class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
            >
              <slot 
                :name="`cell-${column.key}`" 
                :item="item" 
                :value="getNestedValue(item, column.key)"
              >
                {{ formatCellValue(item, column) }}
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div v-if="paginated && filteredData.length > 0" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          显示 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, filteredData.length) }} 条，
          共 {{ filteredData.length }} 条记录
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <span class="text-sm text-gray-700">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </span>
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { 
  MagnifyingGlassIcon, 
  ArrowDownTrayIcon,
  ChevronUpIcon,
  ChevronDownIcon
} from '@heroicons/vue/24/outline'
import LoadingSpinner from './LoadingSpinner.vue'

const props = defineProps({
  title: {
    type: String,
    default: '数据表格'
  },
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  searchable: {
    type: Boolean,
    default: true
  },
  paginated: {
    type: Boolean,
    default: true
  },
  pageSize: {
    type: Number,
    default: 10
  },
  exportable: {
    type: Boolean,
    default: false
  },
  filters: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['sort', 'filter', 'export'])

// 响应式数据
const searchQuery = ref('')
const selectedFilter = ref('')
const sortBy = ref('')
const sortOrder = ref('desc')
const currentPage = ref(1)

// 计算属性
const filteredData = computed(() => {
  if (!props.data || !Array.isArray(props.data)) {
    return []
  }
  let result = [...props.data]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item => 
      props.columns.some(column => {
        const value = getNestedValue(item, column.key)
        return String(value).toLowerCase().includes(query)
      })
    )
  }
  
  // 筛选器过滤
  if (selectedFilter.value) {
    result = result.filter(item => {
      const filterConfig = props.filters.find(f => f.value === selectedFilter.value)
      return filterConfig ? filterConfig.filter(item) : true
    })
  }
  
  // 排序
  if (sortBy.value) {
    result.sort((a, b) => {
      const aVal = getNestedValue(a, sortBy.value)
      const bVal = getNestedValue(b, sortBy.value)
      
      if (sortOrder.value === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })
  }
  
  return result
})

const totalPages = computed(() => 
  Math.ceil(filteredData.value.length / props.pageSize)
)

const paginatedData = computed(() => {
  if (!props.paginated) return filteredData.value
  
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredData.value.slice(start, end)
})

// 方法
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

const formatCellValue = (item, column) => {
  const value = getNestedValue(item, column.key)
  
  if (column.formatter) {
    return column.formatter(value, item)
  }
  
  return value
}

const handleSort = (key) => {
  const column = props.columns.find(c => c.key === key)
  if (!column?.sortable) return
  
  if (sortBy.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = key
    sortOrder.value = 'desc'
  }
  
  emit('sort', { sortBy: sortBy.value, sortOrder: sortOrder.value })
}

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const exportData = () => {
  emit('export', filteredData.value)
}

// 监听筛选变化
watch([selectedFilter, searchQuery], () => {
  currentPage.value = 1
  emit('filter', {
    search: searchQuery.value,
    filter: selectedFilter.value
  })
})
</script>
