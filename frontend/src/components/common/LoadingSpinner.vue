<template>
  <div
    class="loading-overlay"
    :class="{ 'fullscreen': fullscreen }"
    :style="overlayStyle"
  >
    <div class="loading-container" :class="containerClass">
      <!-- 加载动画 -->
      <div class="spinner-wrapper">
        <div
          v-if="type === 'spinner'"
          class="spinner"
          :class="sizeClass"
        ></div>

        <div
          v-else-if="type === 'dots'"
          class="dots-spinner"
          :class="sizeClass"
        >
          <div class="dot" v-for="i in 3" :key="i"></div>
        </div>

        <div
          v-else-if="type === 'pulse'"
          class="pulse-spinner"
          :class="sizeClass"
        ></div>
      </div>

      <!-- 加载文本 -->
      <p v-if="showMessage" class="loading-message">{{ message }}</p>

      <!-- 进度条 -->
      <div v-if="showProgress && progress !== null" class="progress-wrapper">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
        <div class="progress-text">{{ progress }}%</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 加载动画类型
  type: {
    type: String,
    default: 'spinner',
    validator: (value) => ['spinner', 'dots', 'pulse'].includes(value)
  },
  // 大小
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 加载文本
  message: {
    type: String,
    default: '加载中...'
  },
  // 是否显示文本
  showMessage: {
    type: Boolean,
    default: true
  },
  // 是否显示进度
  showProgress: {
    type: Boolean,
    default: false
  },
  // 进度值 (0-100)
  progress: {
    type: Number,
    default: null,
    validator: (value) => value === null || (value >= 0 && value <= 100)
  },
  // 是否全屏覆盖
  fullscreen: {
    type: Boolean,
    default: true
  },
  // 背景透明度
  opacity: {
    type: Number,
    default: 0.5,
    validator: (value) => value >= 0 && value <= 1
  }
})

// 计算属性
const sizeClass = computed(() => `size-${props.size}`)

const containerClass = computed(() => ({
  'with-progress': props.showProgress
}))

const overlayStyle = computed(() => ({
  backgroundColor: `rgba(0, 0, 0, ${props.opacity})`
}))
</script>

<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-overlay.fullscreen {
  position: fixed;
}

.loading-container {
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  padding: 2rem;
  box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--border-color, #e5e7eb);
  text-align: center;
  min-width: 200px;
}

.spinner-wrapper {
  margin-bottom: 1rem;
}

/* 旋转加载器 */
.spinner {
  border: 3px solid var(--gray-200, #e5e7eb);
  border-top: 3px solid var(--primary-600, #4f46e5);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

.spinner.size-small {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.spinner.size-medium {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.spinner.size-large {
  width: 56px;
  height: 56px;
  border-width: 4px;
}

/* 点状加载器 */
.dots-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.dots-spinner.size-small .dot {
  width: 6px;
  height: 6px;
}

.dots-spinner.size-medium .dot {
  width: 8px;
  height: 8px;
}

.dots-spinner.size-large .dot {
  width: 12px;
  height: 12px;
}

.dot {
  background: var(--primary-600, #4f46e5);
  border-radius: 50%;
  animation: dots-bounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

/* 脉冲加载器 */
.pulse-spinner {
  background: var(--primary-600, #4f46e5);
  border-radius: 50%;
  animation: pulse-scale 1s ease-in-out infinite;
  margin: 0 auto;
}

.pulse-spinner.size-small {
  width: 24px;
  height: 24px;
}

.pulse-spinner.size-medium {
  width: 40px;
  height: 40px;
}

.pulse-spinner.size-large {
  width: 56px;
  height: 56px;
}

/* 加载文本 */
.loading-message {
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
}

/* 进度条 */
.progress-wrapper {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--gray-200, #e5e7eb);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: var(--primary-600, #4f46e5);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--on-surface-variant, #6b7280);
  font-weight: 500;
}

/* 动画定义 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(0);
    opacity: 1;
  }
  50% {
    transform: scale(1);
    opacity: 0.7;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .spinner,
  .dot,
  .pulse-spinner {
    animation: none;
  }

  .spinner {
    border-top-color: var(--primary-600, #4f46e5);
  }

  .dot,
  .pulse-spinner {
    opacity: 0.8;
  }
}
</style>
