<template>
  <div 
    ref="containerRef"
    class="lazy-load-container"
    :class="{ 'lazy-load-loading': isLoading }"
  >
    <!-- 占位符 -->
    <div 
      v-if="!isLoaded && showPlaceholder"
      class="lazy-load-placeholder"
      :style="placeholderStyle"
    >
      <slot name="placeholder">
        <div class="lazy-load-skeleton">
          <div class="animate-pulse">
            <div class="bg-gray-200 rounded h-4 w-3/4 mb-2"></div>
            <div class="bg-gray-200 rounded h-4 w-1/2 mb-2"></div>
            <div class="bg-gray-200 rounded h-4 w-5/6"></div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 实际内容 -->
    <div 
      v-if="isLoaded"
      class="lazy-load-content"
      :class="{ 'fade-in': enableFadeIn }"
    >
      <slot :loading="isLoading" :error="error" :retry="retry">
        <!-- 默认内容 -->
      </slot>
    </div>

    <!-- 错误状态 -->
    <div 
      v-if="error && showError"
      class="lazy-load-error"
    >
      <slot name="error" :error="error" :retry="retry">
        <div class="text-center py-8">
          <div class="text-red-500 mb-2">
            <i class="fas fa-exclamation-triangle text-2xl"></i>
          </div>
          <p class="text-gray-600 mb-4">加载失败</p>
          <button 
            @click="retry"
            class="btn btn-primary"
          >
            重试
          </button>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

const props = defineProps({
  // 懒加载触发距离（像素）
  threshold: {
    type: Number,
    default: 100
  },
  // 是否启用懒加载
  enabled: {
    type: Boolean,
    default: true
  },
  // 是否显示占位符
  showPlaceholder: {
    type: Boolean,
    default: true
  },
  // 占位符样式
  placeholderStyle: {
    type: Object,
    default: () => ({
      minHeight: '200px'
    })
  },
  // 是否启用淡入动画
  enableFadeIn: {
    type: Boolean,
    default: true
  },
  // 是否显示错误状态
  showError: {
    type: Boolean,
    default: true
  },
  // 加载函数
  loadFunction: {
    type: Function,
    default: null
  },
  // 是否立即加载（不等待进入视口）
  immediate: {
    type: Boolean,
    default: false
  },
  // 根元素边距（用于Intersection Observer）
  rootMargin: {
    type: String,
    default: '0px'
  }
})

const emit = defineEmits(['load', 'loaded', 'error', 'visible'])

const containerRef = ref(null)
const isLoaded = ref(false)
const isLoading = ref(false)
const error = ref(null)
const observer = ref(null)

// 计算是否应该显示内容
const shouldLoad = computed(() => {
  return props.immediate || !props.enabled || isLoaded.value
})

// 初始化Intersection Observer
const initObserver = () => {
  if (!props.enabled || props.immediate) {
    loadContent()
    return
  }

  if ('IntersectionObserver' in window) {
    observer.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            emit('visible')
            loadContent()
            // 加载后停止观察
            if (observer.value) {
              observer.value.unobserve(entry.target)
            }
          }
        })
      },
      {
        threshold: 0.1,
        rootMargin: props.rootMargin
      }
    )

    if (containerRef.value) {
      observer.value.observe(containerRef.value)
    }
  } else {
    // 不支持Intersection Observer时直接加载
    loadContent()
  }
}

// 加载内容
const loadContent = async () => {
  if (isLoaded.value || isLoading.value) {
    return
  }

  isLoading.value = true
  error.value = null

  try {
    emit('load')
    
    if (props.loadFunction) {
      await props.loadFunction()
    }
    
    isLoaded.value = true
    emit('loaded')
  } catch (err) {
    error.value = err
    emit('error', err)
    console.error('LazyLoad error:', err)
  } finally {
    isLoading.value = false
  }
}

// 重试加载
const retry = () => {
  error.value = null
  isLoaded.value = false
  loadContent()
}

// 重置状态
const reset = () => {
  isLoaded.value = false
  isLoading.value = false
  error.value = null
  
  if (props.enabled && !props.immediate) {
    initObserver()
  }
}

// 监听enabled属性变化
watch(() => props.enabled, (newVal) => {
  if (newVal && !isLoaded.value) {
    initObserver()
  } else if (!newVal && observer.value) {
    observer.value.disconnect()
  }
})

// 监听immediate属性变化
watch(() => props.immediate, (newVal) => {
  if (newVal && !isLoaded.value) {
    loadContent()
  }
})

// 暴露方法给父组件
defineExpose({
  loadContent,
  retry,
  reset,
  isLoaded: computed(() => isLoaded.value),
  isLoading: computed(() => isLoading.value),
  error: computed(() => error.value)
})

onMounted(() => {
  initObserver()
})

onUnmounted(() => {
  if (observer.value) {
    observer.value.disconnect()
  }
})
</script>

<style scoped>
.lazy-load-container {
  position: relative;
}

.lazy-load-loading {
  pointer-events: none;
}

.lazy-load-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
}

.lazy-load-skeleton {
  width: 100%;
  padding: 1rem;
}

.lazy-load-content {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.lazy-load-content.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.lazy-load-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 骨架屏动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
