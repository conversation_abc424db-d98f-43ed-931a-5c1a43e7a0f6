<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900">筛选条件</h3>
      <button
        @click="resetFilters"
        class="text-sm text-blue-600 hover:text-blue-800"
      >
        重置
      </button>
    </div>

    <div class="space-y-4">
      <!-- 日期范围筛选 -->
      <div v-if="showDateRange">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          时间范围
        </label>
        <div class="grid grid-cols-2 gap-2">
          <input
            v-model="filters.startDate"
            type="date"
            class="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
          <input
            v-model="filters.endDate"
            type="date"
            class="border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
        </div>
        <div class="mt-2 flex flex-wrap gap-2">
          <button
            v-for="preset in datePresets"
            :key="preset.value"
            @click="setDatePreset(preset.value)"
            :class="[
              'px-3 py-1 text-xs rounded-full border',
              filters.datePreset === preset.value
                ? 'bg-blue-100 border-blue-300 text-blue-800'
                : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
            ]"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>

      <!-- 项目筛选 -->
      <div v-if="showProject">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          项目
        </label>
        <select
          v-model="filters.projectId"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">全部项目</option>
          <option v-for="project in projects" :key="project.id" :value="project.id">
            {{ project.name }}
          </option>
        </select>
      </div>

      <!-- 团队筛选 -->
      <div v-if="showTeam">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          团队
        </label>
        <select
          v-model="filters.teamId"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">全部团队</option>
          <option v-for="team in teams" :key="team.id" :value="team.id">
            {{ team.name }}
          </option>
        </select>
      </div>

      <!-- 状态筛选 -->
      <div v-if="showStatus">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          状态
        </label>
        <div class="space-y-2">
          <label
            v-for="status in statusOptions"
            :key="status.value"
            class="flex items-center"
          >
            <input
              v-model="filters.status"
              :value="status.value"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <span class="ml-2 text-sm text-gray-700">{{ status.label }}</span>
          </label>
        </div>
      </div>

      <!-- 指标类型筛选 -->
      <div v-if="showMetricType">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          指标类型
        </label>
        <select
          v-model="filters.metricType"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">全部类型</option>
          <option v-for="type in metricTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>

      <!-- 自定义筛选器 -->
      <div v-for="customFilter in customFilters" :key="customFilter.key">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ customFilter.label }}
        </label>
        
        <!-- 下拉选择 -->
        <select
          v-if="customFilter.type === 'select'"
          v-model="filters[customFilter.key]"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">{{ customFilter.placeholder || '请选择' }}</option>
          <option 
            v-for="option in customFilter.options" 
            :key="option.value" 
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
        
        <!-- 多选框 -->
        <div v-else-if="customFilter.type === 'checkbox'" class="space-y-2">
          <label
            v-for="option in customFilter.options"
            :key="option.value"
            class="flex items-center"
          >
            <input
              v-model="filters[customFilter.key]"
              :value="option.value"
              type="checkbox"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            >
            <span class="ml-2 text-sm text-gray-700">{{ option.label }}</span>
          </label>
        </div>
        
        <!-- 输入框 -->
        <input
          v-else-if="customFilter.type === 'input'"
          v-model="filters[customFilter.key]"
          :type="customFilter.inputType || 'text'"
          :placeholder="customFilter.placeholder"
          class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
      </div>
    </div>

    <!-- 应用按钮 -->
    <div class="mt-6 flex space-x-3">
      <button
        @click="applyFilters"
        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        应用筛选
      </button>
      <button
        @click="clearFilters"
        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        清空
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { format, subDays, subWeeks, subMonths } from 'date-fns'

const props = defineProps({
  showDateRange: {
    type: Boolean,
    default: true
  },
  showProject: {
    type: Boolean,
    default: false
  },
  showTeam: {
    type: Boolean,
    default: false
  },
  showStatus: {
    type: Boolean,
    default: false
  },
  showMetricType: {
    type: Boolean,
    default: false
  },
  projects: {
    type: Array,
    default: () => []
  },
  teams: {
    type: Array,
    default: () => []
  },
  statusOptions: {
    type: Array,
    default: () => [
      { value: 'active', label: '活跃' },
      { value: 'inactive', label: '非活跃' },
      { value: 'archived', label: '已归档' }
    ]
  },
  metricTypes: {
    type: Array,
    default: () => [
      { value: 'coverage', label: '覆盖率' },
      { value: 'performance', label: '性能' },
      { value: 'quality_gate', label: '质量门禁' },
      { value: 'efficiency', label: '效能' }
    ]
  },
  customFilters: {
    type: Array,
    default: () => []
  },
  initialFilters: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['filter-change', 'filter-apply', 'filter-reset'])

// 日期预设选项
const datePresets = [
  { label: '今天', value: '1d' },
  { label: '最近7天', value: '7d' },
  { label: '最近30天', value: '30d' },
  { label: '最近90天', value: '90d' }
]

// 筛选器状态
const filters = reactive({
  startDate: '',
  endDate: '',
  datePreset: '7d',
  projectId: '',
  teamId: '',
  status: [],
  metricType: '',
  ...props.initialFilters
})

// 方法
const setDatePreset = (preset) => {
  filters.datePreset = preset
  const today = new Date()
  
  switch (preset) {
    case '1d':
      filters.startDate = format(today, 'yyyy-MM-dd')
      filters.endDate = format(today, 'yyyy-MM-dd')
      break
    case '7d':
      filters.startDate = format(subDays(today, 7), 'yyyy-MM-dd')
      filters.endDate = format(today, 'yyyy-MM-dd')
      break
    case '30d':
      filters.startDate = format(subDays(today, 30), 'yyyy-MM-dd')
      filters.endDate = format(today, 'yyyy-MM-dd')
      break
    case '90d':
      filters.startDate = format(subDays(today, 90), 'yyyy-MM-dd')
      filters.endDate = format(today, 'yyyy-MM-dd')
      break
  }
}

const applyFilters = () => {
  emit('filter-apply', { ...filters })
}

const clearFilters = () => {
  Object.keys(filters).forEach(key => {
    if (Array.isArray(filters[key])) {
      filters[key] = []
    } else {
      filters[key] = ''
    }
  })
  filters.datePreset = '7d'
  setDatePreset('7d')
  emit('filter-apply', { ...filters })
}

const resetFilters = () => {
  Object.assign(filters, props.initialFilters)
  emit('filter-reset')
}

// 监听筛选器变化
watch(filters, (newFilters) => {
  emit('filter-change', { ...newFilters })
}, { deep: true })

// 初始化日期范围
setDatePreset('7d')
</script>
