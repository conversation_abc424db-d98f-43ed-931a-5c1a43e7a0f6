<template>
  <div class="theme-toggle">
    <!-- 主题切换按钮 -->
    <button
      @click="toggleDropdown"
      class="theme-toggle-btn"
      :class="{ 'active': isDropdownOpen }"
      :aria-label="'当前主题: ' + currentTheme.label"
      :aria-expanded="isDropdownOpen"
    >
      <i :class="currentTheme.icon" class="theme-icon"></i>
      <span v-if="showLabel" class="theme-label">{{ currentTheme.label }}</span>
      <i class="fas fa-chevron-down dropdown-arrow" :class="{ 'rotated': isDropdownOpen }"></i>
    </button>

    <!-- 主题选择下拉菜单 -->
    <transition name="dropdown">
      <div
        v-if="isDropdownOpen"
        class="theme-dropdown"
        @click.stop
      >
        <div class="dropdown-header">
          <h3 class="dropdown-title">选择主题</h3>
          <p class="dropdown-subtitle">个性化您的界面体验</p>
        </div>
        
        <div class="theme-options">
          <button
            v-for="theme in themes"
            :key="theme.value"
            @click="selectTheme(theme.value)"
            class="theme-option"
            :class="{ 'active': currentThemeValue === theme.value }"
            :aria-label="'切换到' + theme.label + '主题'"
          >
            <div class="theme-option-icon">
              <i :class="theme.icon"></i>
            </div>
            <div class="theme-option-content">
              <div class="theme-option-label">{{ theme.label }}</div>
              <div class="theme-option-description">{{ theme.description }}</div>
            </div>
            <div class="theme-option-check" v-if="currentThemeValue === theme.value">
              <i class="fas fa-check"></i>
            </div>
          </button>
        </div>

        <!-- 主题预览 -->
        <div class="theme-preview">
          <div class="preview-header">主题预览</div>
          <div class="preview-content">
            <div class="preview-card" :data-theme="previewTheme">
              <div class="preview-card-header">
                <div class="preview-title">示例卡片</div>
                <div class="preview-badge">NEW</div>
              </div>
              <div class="preview-card-body">
                <div class="preview-text">这是一个主题预览示例</div>
                <div class="preview-button">按钮样式</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 自动主题设置 -->
        <div class="auto-theme-section">
          <label class="auto-theme-toggle">
            <input
              type="checkbox"
              v-model="autoTheme"
              @change="toggleAutoTheme"
              class="auto-theme-checkbox"
            />
            <span class="auto-theme-label">
              <i class="fas fa-magic mr-2"></i>
              跟随系统主题
            </span>
          </label>
          <p class="auto-theme-description">
            自动根据系统设置切换明暗主题
          </p>
        </div>
      </div>
    </transition>

    <!-- 遮罩层 -->
    <div
      v-if="isDropdownOpen"
      class="theme-overlay"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  // 是否显示标签文字
  showLabel: {
    type: Boolean,
    default: false
  },
  // 按钮大小
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 按钮样式
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'minimal', 'outlined'].includes(value)
  }
})

const emit = defineEmits(['themeChange'])

// 响应式状态
const isDropdownOpen = ref(false)
const currentThemeValue = ref('auto')
const autoTheme = ref(true)
const previewTheme = ref('light')

// 主题配置
const themes = [
  {
    value: 'light',
    label: '浅色主题',
    description: '明亮清爽的界面风格',
    icon: 'fas fa-sun'
  },
  {
    value: 'dark',
    label: '深色主题',
    description: '护眼的深色界面风格',
    icon: 'fas fa-moon'
  },
  {
    value: 'auto',
    label: '自动主题',
    description: '跟随系统设置自动切换',
    icon: 'fas fa-adjust'
  },
  {
    value: 'high-contrast',
    label: '高对比度',
    description: '提高可读性的高对比度主题',
    icon: 'fas fa-eye'
  }
]

// 计算属性
const currentTheme = computed(() => {
  return themes.find(theme => theme.value === currentThemeValue.value) || themes[0]
})

// 媒体查询监听
let mediaQuery = null

// 生命周期
onMounted(() => {
  // 从本地存储加载主题设置
  loadThemeFromStorage()
  
  // 监听系统主题变化
  setupMediaQueryListener()
  
  // 应用初始主题
  applyTheme()
  
  // 监听点击外部关闭下拉菜单
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  if (mediaQuery) {
    mediaQuery.removeEventListener('change', handleSystemThemeChange)
  }
  document.removeEventListener('click', handleClickOutside)
})

// 监听器
watch(currentThemeValue, () => {
  applyTheme()
  saveThemeToStorage()
  emit('themeChange', currentThemeValue.value)
})

// 方法
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value
  if (isDropdownOpen.value) {
    previewTheme.value = currentThemeValue.value
  }
}

const closeDropdown = () => {
  isDropdownOpen.value = false
}

const selectTheme = (themeValue) => {
  currentThemeValue.value = themeValue
  autoTheme.value = themeValue === 'auto'
  closeDropdown()
}

const toggleAutoTheme = () => {
  if (autoTheme.value) {
    currentThemeValue.value = 'auto'
  } else {
    // 如果关闭自动主题，切换到当前系统主题
    const systemTheme = getSystemTheme()
    currentThemeValue.value = systemTheme
  }
}

const applyTheme = () => {
  const theme = resolveTheme(currentThemeValue.value)
  document.documentElement.setAttribute('data-theme', theme)
  
  // 更新meta标签的主题色
  updateThemeColor(theme)
}

const resolveTheme = (themeValue) => {
  if (themeValue === 'auto') {
    return getSystemTheme()
  }
  return themeValue
}

const getSystemTheme = () => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }
  return 'light'
}

const setupMediaQueryListener = () => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', handleSystemThemeChange)
  }
}

const handleSystemThemeChange = () => {
  if (currentThemeValue.value === 'auto') {
    applyTheme()
  }
}

const updateThemeColor = (theme) => {
  const themeColors = {
    light: '#4f46e5',
    dark: '#6366f1',
    'high-contrast': '#0000ff'
  }
  
  let metaThemeColor = document.querySelector('meta[name="theme-color"]')
  if (!metaThemeColor) {
    metaThemeColor = document.createElement('meta')
    metaThemeColor.name = 'theme-color'
    document.head.appendChild(metaThemeColor)
  }
  
  metaThemeColor.content = themeColors[theme] || themeColors.light
}

const saveThemeToStorage = () => {
  try {
    localStorage.setItem('theme', currentThemeValue.value)
    localStorage.setItem('autoTheme', autoTheme.value.toString())
  } catch (error) {
    console.warn('无法保存主题设置到本地存储:', error)
  }
}

const loadThemeFromStorage = () => {
  try {
    const savedTheme = localStorage.getItem('theme')
    const savedAutoTheme = localStorage.getItem('autoTheme')
    
    if (savedTheme) {
      currentThemeValue.value = savedTheme
    }
    
    if (savedAutoTheme !== null) {
      autoTheme.value = savedAutoTheme === 'true'
    }
  } catch (error) {
    console.warn('无法从本地存储加载主题设置:', error)
  }
}

const handleClickOutside = (event) => {
  if (!event.target.closest('.theme-toggle')) {
    closeDropdown()
  }
}

// 预览主题变化
watch(previewTheme, (newTheme) => {
  // 这里可以添加预览逻辑
})
</script>

<style scoped>
.theme-toggle {
  position: relative;
  display: inline-block;
}

.theme-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--on-surface);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
}

.theme-toggle-btn:hover {
  background: var(--surface-variant);
  border-color: var(--border-color-hover);
}

.theme-toggle-btn.active {
  background: var(--primary-50);
  border-color: var(--primary-200);
  color: var(--primary-700);
}

.theme-icon {
  font-size: 16px;
}

.theme-label {
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform var(--transition-fast);
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  width: 320px;
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-dropdown);
  overflow: hidden;
}

.dropdown-header {
  padding: 16px;
  border-bottom: 1px solid var(--divider-color);
}

.dropdown-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--on-surface);
  margin-bottom: 4px;
}

.dropdown-subtitle {
  font-size: var(--text-sm);
  color: var(--on-surface-variant);
}

.theme-options {
  padding: 8px;
}

.theme-option {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px;
  background: transparent;
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.theme-option:hover {
  background: var(--surface-variant);
}

.theme-option.active {
  background: var(--primary-50);
  color: var(--primary-700);
}

.theme-option-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--surface-variant);
  border-radius: var(--radius);
  margin-right: 12px;
  font-size: 14px;
}

.theme-option.active .theme-option-icon {
  background: var(--primary-100);
  color: var(--primary-600);
}

.theme-option-content {
  flex: 1;
}

.theme-option-label {
  font-weight: 500;
  color: var(--on-surface);
  margin-bottom: 2px;
}

.theme-option-description {
  font-size: var(--text-xs);
  color: var(--on-surface-variant);
}

.theme-option-check {
  color: var(--primary-600);
  font-size: 14px;
}

.theme-preview {
  padding: 16px;
  border-top: 1px solid var(--divider-color);
  border-bottom: 1px solid var(--divider-color);
}

.preview-header {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--on-surface);
  margin-bottom: 12px;
}

.preview-card {
  background: var(--surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  overflow: hidden;
  font-size: var(--text-sm);
}

.preview-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--surface-variant);
  border-bottom: 1px solid var(--border-color);
}

.preview-title {
  font-weight: 500;
  color: var(--on-surface);
}

.preview-badge {
  padding: 2px 8px;
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}

.preview-card-body {
  padding: 12px;
}

.preview-text {
  color: var(--on-surface-variant);
  margin-bottom: 8px;
}

.preview-button {
  display: inline-block;
  padding: 6px 12px;
  background: var(--primary-600);
  color: white;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}

.auto-theme-section {
  padding: 16px;
}

.auto-theme-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 8px;
}

.auto-theme-checkbox {
  margin-right: 8px;
}

.auto-theme-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--on-surface);
}

.auto-theme-description {
  font-size: var(--text-xs);
  color: var(--on-surface-variant);
  margin-left: 24px;
}

.theme-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: calc(var(--z-dropdown) - 1);
}

/* 下拉动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all var(--transition-normal);
  transform-origin: top right;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-10px);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: scale(1) translateY(0);
}
</style>
