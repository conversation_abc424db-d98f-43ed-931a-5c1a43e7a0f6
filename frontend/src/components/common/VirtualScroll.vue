<template>
  <div 
    ref="containerRef" 
    class="virtual-scroll-container"
    :style="{ height: containerHeight + 'px', overflow: 'auto' }"
    @scroll="handleScroll"
  >
    <!-- 虚拟滚动内容 -->
    <div 
      class="virtual-scroll-content"
      :style="{ 
        height: totalHeight + 'px',
        position: 'relative'
      }"
    >
      <!-- 可见项目 -->
      <div
        v-for="item in visibleItems"
        :key="getItemKey(item.data)"
        class="virtual-scroll-item"
        :style="{
          position: 'absolute',
          top: item.top + 'px',
          left: '0',
          right: '0',
          height: itemHeight + 'px'
        }"
      >
        <slot :item="item.data" :index="item.index">
          {{ item.data }}
        </slot>
      </div>
    </div>

    <!-- 加载更多指示器 -->
    <div 
      v-if="loading" 
      class="virtual-scroll-loading"
      style="position: absolute; bottom: 0; left: 0; right: 0; text-align: center; padding: 16px;"
    >
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        加载中...
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
  items: {
    type: Array,
    default: () => []
  },
  itemHeight: {
    type: Number,
    default: 60
  },
  containerHeight: {
    type: Number,
    default: 400
  },
  buffer: {
    type: Number,
    default: 5
  },
  loading: {
    type: Boolean,
    default: false
  },
  hasMore: {
    type: Boolean,
    default: false
  },
  keyField: {
    type: String,
    default: 'id'
  }
})

const emit = defineEmits(['load-more', 'scroll'])

const containerRef = ref(null)
const scrollTop = ref(0)

// 计算总高度
const totalHeight = computed(() => {
  return props.items.length * props.itemHeight
})

// 计算可见区域的起始和结束索引
const visibleRange = computed(() => {
  const containerHeight = props.containerHeight
  const itemHeight = props.itemHeight
  const buffer = props.buffer

  const startIndex = Math.max(0, Math.floor(scrollTop.value / itemHeight) - buffer)
  const endIndex = Math.min(
    props.items.length - 1,
    Math.ceil((scrollTop.value + containerHeight) / itemHeight) + buffer
  )

  return { startIndex, endIndex }
})

// 计算可见项目
const visibleItems = computed(() => {
  const { startIndex, endIndex } = visibleRange.value
  const items = []

  for (let i = startIndex; i <= endIndex; i++) {
    if (props.items[i]) {
      items.push({
        index: i,
        data: props.items[i],
        top: i * props.itemHeight
      })
    }
  }

  return items
})

// 获取项目的唯一键
const getItemKey = (item) => {
  if (typeof item === 'object' && item !== null) {
    return item[props.keyField] || item.id || JSON.stringify(item)
  }
  return item
}

// 处理滚动事件
const handleScroll = (event) => {
  const target = event.target
  scrollTop.value = target.scrollTop

  emit('scroll', {
    scrollTop: target.scrollTop,
    scrollHeight: target.scrollHeight,
    clientHeight: target.clientHeight
  })

  // 检查是否需要加载更多数据
  if (props.hasMore && !props.loading) {
    const threshold = 100 // 距离底部100px时触发加载
    const isNearBottom = target.scrollTop + target.clientHeight >= target.scrollHeight - threshold
    
    if (isNearBottom) {
      emit('load-more')
    }
  }
}

// 滚动到指定项目
const scrollToItem = (index) => {
  if (containerRef.value) {
    const targetScrollTop = index * props.itemHeight
    containerRef.value.scrollTop = targetScrollTop
  }
}

// 滚动到顶部
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = containerRef.value.scrollHeight
  }
}

// 监听items变化，保持滚动位置
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength > oldLength && containerRef.value) {
    // 如果是在底部加载更多，保持在底部
    nextTick(() => {
      const container = containerRef.value
      const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 50
      if (isAtBottom) {
        scrollToBottom()
      }
    })
  }
})

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  getVisibleRange: () => visibleRange.value
})

onMounted(() => {
  // 初始化滚动位置
  if (containerRef.value) {
    scrollTop.value = containerRef.value.scrollTop
  }
})
</script>

<style scoped>
.virtual-scroll-container {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
}

.virtual-scroll-content {
  position: relative;
}

.virtual-scroll-item {
  box-sizing: border-box;
}

.virtual-scroll-loading {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

/* 自定义滚动条样式 */
.virtual-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
