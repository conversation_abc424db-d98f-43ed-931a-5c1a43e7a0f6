<template>
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <transition-group name="notification" tag="div">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        class="notification-card"
        :class="notificationClasses(notification.type)"
      >
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <i :class="notificationIcon(notification.type)" class="text-xl"></i>
          </div>
          <div class="ml-3 flex-1">
            <h4 class="font-medium">{{ notification.title }}</h4>
            <p class="text-sm mt-1">{{ notification.message }}</p>
          </div>
          <button
            @click="removeNotification(notification.id)"
            class="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'

export default {
  name: 'NotificationContainer',
  setup() {
    const appStore = useAppStore()
    
    const notifications = computed(() => appStore.notifications)
    
    const notificationClasses = (type) => {
      const baseClasses = 'notification-card'
      const typeClasses = {
        success: 'bg-success-50 border-success-200 text-success-800',
        error: 'bg-danger-50 border-danger-200 text-danger-800',
        warning: 'bg-warning-50 border-warning-200 text-warning-800',
        info: 'bg-primary-50 border-primary-200 text-primary-800',
      }
      return `${baseClasses} ${typeClasses[type] || typeClasses.info}`
    }
    
    const notificationIcon = (type) => {
      const icons = {
        success: 'fas fa-check-circle text-success-500',
        error: 'fas fa-exclamation-circle text-danger-500',
        warning: 'fas fa-exclamation-triangle text-warning-500',
        info: 'fas fa-info-circle text-primary-500',
      }
      return icons[type] || icons.info
    }
    
    const removeNotification = (id) => {
      appStore.removeNotification(id)
    }
    
    return {
      notifications,
      notificationClasses,
      notificationIcon,
      removeNotification,
    }
  },
}
</script>

<style scoped>
.notification-card {
  @apply max-w-sm w-full bg-white shadow-lg rounded-lg border p-4;
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>
