<template>
  <div 
    ref="container"
    class="virtual-list-container"
    :style="containerStyle"
    @scroll="handleScroll"
  >
    <!-- 上方填充区域 -->
    <div 
      class="virtual-list-spacer"
      :style="{ height: `${offsetY}px` }"
    ></div>

    <!-- 可见项目 -->
    <div 
      v-for="(item, index) in visibleItems"
      :key="getItemKey(item, startIndex + index)"
      class="virtual-list-item"
      :style="getItemStyle(startIndex + index)"
    >
      <slot 
        :item="item"
        :index="startIndex + index"
        :isVisible="true"
      >
        <div class="default-item">
          {{ item }}
        </div>
      </slot>
    </div>

    <!-- 下方填充区域 -->
    <div 
      class="virtual-list-spacer"
      :style="{ height: `${bottomSpacerHeight}px` }"
    ></div>

    <!-- 加载更多指示器 -->
    <div 
      v-if="loading && hasMore"
      class="virtual-list-loading"
    >
      <slot name="loading">
        <div class="loading-indicator">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>
      </slot>
    </div>

    <!-- 无更多数据提示 -->
    <div 
      v-if="!hasMore && items.length > 0"
      class="virtual-list-end"
    >
      <slot name="end">
        <div class="end-indicator">
          没有更多数据了
        </div>
      </slot>
    </div>

    <!-- 空状态 -->
    <div 
      v-if="items.length === 0 && !loading"
      class="virtual-list-empty"
    >
      <slot name="empty">
        <div class="empty-indicator">
          <div class="empty-icon">📝</div>
          <div class="empty-text">暂无数据</div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
  // 数据列表
  items: {
    type: Array,
    required: true
  },
  // 每项的高度
  itemHeight: {
    type: Number,
    default: 50
  },
  // 容器高度
  height: {
    type: [Number, String],
    default: 400
  },
  // 缓冲区大小（额外渲染的项目数）
  buffer: {
    type: Number,
    default: 5
  },
  // 项目唯一键的字段名
  keyField: {
    type: String,
    default: 'id'
  },
  // 是否启用无限滚动
  infiniteScroll: {
    type: Boolean,
    default: false
  },
  // 触发加载更多的距离
  loadMoreThreshold: {
    type: Number,
    default: 100
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  },
  // 是否还有更多数据
  hasMore: {
    type: Boolean,
    default: true
  },
  // 容器样式类
  containerClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否启用动态高度
  dynamicHeight: {
    type: Boolean,
    default: false
  },
  // 预估项目高度（动态高度模式下使用）
  estimatedItemHeight: {
    type: Number,
    default: 50
  }
})

const emit = defineEmits(['scroll', 'loadMore', 'itemVisible', 'itemHidden'])

// 响应式状态
const container = ref(null)
const scrollTop = ref(0)
const containerHeight = ref(0)
const itemHeights = ref(new Map()) // 存储每个项目的实际高度

// 计算属性
const containerStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  overflow: 'auto',
  position: 'relative'
}))

const totalHeight = computed(() => {
  if (props.dynamicHeight) {
    let height = 0
    for (let i = 0; i < props.items.length; i++) {
      height += itemHeights.value.get(i) || props.estimatedItemHeight
    }
    return height
  }
  return props.items.length * props.itemHeight
})

const visibleCount = computed(() => {
  return Math.ceil(containerHeight.value / props.itemHeight) + props.buffer * 2
})

const startIndex = computed(() => {
  if (props.dynamicHeight) {
    return getDynamicStartIndex()
  }
  const index = Math.floor(scrollTop.value / props.itemHeight) - props.buffer
  return Math.max(0, index)
})

const endIndex = computed(() => {
  return Math.min(props.items.length - 1, startIndex.value + visibleCount.value)
})

const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value + 1)
})

const offsetY = computed(() => {
  if (props.dynamicHeight) {
    return getDynamicOffsetY()
  }
  return startIndex.value * props.itemHeight
})

const bottomSpacerHeight = computed(() => {
  const remainingItems = props.items.length - endIndex.value - 1
  if (props.dynamicHeight) {
    let height = 0
    for (let i = endIndex.value + 1; i < props.items.length; i++) {
      height += itemHeights.value.get(i) || props.estimatedItemHeight
    }
    return height
  }
  return remainingItems * props.itemHeight
})

// 监听器
watch(() => props.items, () => {
  if (props.dynamicHeight) {
    itemHeights.value.clear()
  }
  nextTick(() => {
    updateContainerHeight()
  })
})

// 生命周期
onMounted(() => {
  updateContainerHeight()
  if (props.dynamicHeight) {
    observeItemHeights()
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// ResizeObserver for dynamic heights
let resizeObserver = null

// 方法
const updateContainerHeight = () => {
  if (container.value) {
    containerHeight.value = container.value.clientHeight
  }
}

const handleScroll = (event) => {
  scrollTop.value = event.target.scrollTop
  
  emit('scroll', {
    scrollTop: scrollTop.value,
    scrollLeft: event.target.scrollLeft,
    scrollHeight: event.target.scrollHeight,
    clientHeight: event.target.clientHeight
  })

  // 无限滚动检查
  if (props.infiniteScroll && props.hasMore && !props.loading) {
    const { scrollTop, scrollHeight, clientHeight } = event.target
    const distanceToBottom = scrollHeight - scrollTop - clientHeight
    
    if (distanceToBottom <= props.loadMoreThreshold) {
      emit('loadMore')
    }
  }

  // 触发可见性事件
  emitVisibilityEvents()
}

const emitVisibilityEvents = () => {
  const currentVisible = new Set()
  
  for (let i = startIndex.value; i <= endIndex.value; i++) {
    currentVisible.add(i)
    emit('itemVisible', {
      index: i,
      item: props.items[i]
    })
  }
}

const getItemKey = (item, index) => {
  if (typeof item === 'object' && item !== null && props.keyField in item) {
    return item[props.keyField]
  }
  return index
}

const getItemStyle = (index) => {
  const style = {}
  
  if (props.dynamicHeight) {
    const height = itemHeights.value.get(index)
    if (height) {
      style.height = `${height}px`
    }
  } else {
    style.height = `${props.itemHeight}px`
  }
  
  return style
}

// 动态高度相关方法
const getDynamicStartIndex = () => {
  let accumulatedHeight = 0
  for (let i = 0; i < props.items.length; i++) {
    const itemHeight = itemHeights.value.get(i) || props.estimatedItemHeight
    if (accumulatedHeight + itemHeight > scrollTop.value) {
      return Math.max(0, i - props.buffer)
    }
    accumulatedHeight += itemHeight
  }
  return 0
}

const getDynamicOffsetY = () => {
  let height = 0
  for (let i = 0; i < startIndex.value; i++) {
    height += itemHeights.value.get(i) || props.estimatedItemHeight
  }
  return height
}

const observeItemHeights = () => {
  if (!('ResizeObserver' in window)) return

  resizeObserver = new ResizeObserver((entries) => {
    entries.forEach((entry) => {
      const index = parseInt(entry.target.dataset.index)
      if (!isNaN(index)) {
        itemHeights.value.set(index, entry.contentRect.height)
      }
    })
  })

  // 观察所有可见项目
  nextTick(() => {
    const items = container.value?.querySelectorAll('.virtual-list-item')
    items?.forEach((item, index) => {
      item.dataset.index = startIndex.value + index
      resizeObserver.observe(item)
    })
  })
}

// 公共方法
const scrollToIndex = (index, behavior = 'smooth') => {
  if (!container.value || index < 0 || index >= props.items.length) return

  let targetScrollTop
  if (props.dynamicHeight) {
    targetScrollTop = getDynamicOffsetForIndex(index)
  } else {
    targetScrollTop = index * props.itemHeight
  }

  container.value.scrollTo({
    top: targetScrollTop,
    behavior
  })
}

const scrollToTop = (behavior = 'smooth') => {
  if (container.value) {
    container.value.scrollTo({
      top: 0,
      behavior
    })
  }
}

const scrollToBottom = (behavior = 'smooth') => {
  if (container.value) {
    container.value.scrollTo({
      top: container.value.scrollHeight,
      behavior
    })
  }
}

const getDynamicOffsetForIndex = (index) => {
  let height = 0
  for (let i = 0; i < index; i++) {
    height += itemHeights.value.get(i) || props.estimatedItemHeight
  }
  return height
}

// 暴露方法给父组件
defineExpose({
  scrollToIndex,
  scrollToTop,
  scrollToBottom,
  container
})
</script>

<style scoped>
.virtual-list-container {
  position: relative;
  overflow: auto;
}

.virtual-list-spacer {
  width: 100%;
}

.virtual-list-item {
  width: 100%;
  box-sizing: border-box;
}

.default-item {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
}

.virtual-list-loading {
  padding: 16px;
  text-align: center;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #6b7280;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.virtual-list-end {
  padding: 16px;
  text-align: center;
}

.end-indicator {
  color: #9ca3af;
  font-size: 14px;
}

.virtual-list-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.empty-indicator {
  text-align: center;
  color: #9ca3af;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}
</style>
