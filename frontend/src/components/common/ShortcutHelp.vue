<template>
  <teleport to="body">
    <transition name="modal">
      <div v-if="showHelp" class="shortcut-help-overlay" @click="closeHelp">
        <div class="shortcut-help-modal" @click.stop>
          <!-- 模态框头部 -->
          <div class="modal-header">
            <h2 class="modal-title">
              <i class="fas fa-keyboard mr-3"></i>
              键盘快捷键
            </h2>
            <button @click="closeHelp" class="close-btn" aria-label="关闭">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- 快捷键内容 -->
          <div class="modal-content">
            <div class="shortcut-intro">
              <p class="intro-text">
                使用键盘快捷键可以更高效地操作质量大盘系统。
                以下是所有可用的快捷键组合：
              </p>
            </div>

            <!-- 快捷键分类 -->
            <div class="shortcut-categories">
              <div
                v-for="category in categorizedShortcuts"
                :key="category.title"
                class="shortcut-category"
              >
                <h3 class="category-title">
                  <i :class="getCategoryIcon(category.title)" class="category-icon"></i>
                  {{ category.title }}
                </h3>
                
                <div class="shortcuts-list">
                  <div
                    v-for="shortcut in category.shortcuts"
                    :key="shortcut.key"
                    class="shortcut-item"
                  >
                    <div class="shortcut-keys">
                      <kbd
                        v-for="key in parseShortcutKey(shortcut.key)"
                        :key="key"
                        class="key"
                        :class="getKeyClass(key)"
                      >
                        {{ formatKey(key) }}
                      </kbd>
                    </div>
                    <div class="shortcut-description">
                      {{ shortcut.description }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 提示信息 -->
            <div class="shortcut-tips">
              <div class="tip-section">
                <h4 class="tip-title">
                  <i class="fas fa-lightbulb mr-2"></i>
                  使用技巧
                </h4>
                <ul class="tips-list">
                  <li>在任何页面按 <kbd class="key">Ctrl</kbd> + <kbd class="key">K</kbd> 快速打开搜索</li>
                  <li>按 <kbd class="key">/</kbd> 键可以快速聚焦到搜索框</li>
                  <li>使用 <kbd class="key">Esc</kbd> 键可以关闭任何打开的模态框</li>
                  <li>数字键 <kbd class="key">1-4</kbd> 配合 <kbd class="key">Ctrl</kbd> 可以快速切换页面</li>
                </ul>
              </div>

              <div class="tip-section">
                <h4 class="tip-title">
                  <i class="fas fa-info-circle mr-2"></i>
                  注意事项
                </h4>
                <ul class="tips-list">
                  <li>在输入框中时，某些快捷键可能不会生效</li>
                  <li>Mac 用户请使用 <kbd class="key">Cmd</kbd> 替代 <kbd class="key">Ctrl</kbd></li>
                  <li>部分快捷键可能与浏览器快捷键冲突</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 模态框底部 -->
          <div class="modal-footer">
            <div class="footer-info">
              <span class="version-info">
                <i class="fas fa-code mr-2"></i>
                质量大盘 v1.0.0
              </span>
            </div>
            <div class="footer-actions">
              <button @click="printShortcuts" class="btn btn-secondary">
                <i class="fas fa-print mr-2"></i>
                打印
              </button>
              <button @click="closeHelp" class="btn btn-primary">
                <i class="fas fa-check mr-2"></i>
                知道了
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script setup>
import { computed } from 'vue'
import { useShortcutHelp } from '@/composables/useKeyboardShortcuts'

const { showHelp, shortcuts, closeHelp } = useShortcutHelp()

// 按分类组织快捷键
const categorizedShortcuts = computed(() => {
  const categories = {
    search: { title: '搜索功能', shortcuts: [] },
    navigation: { title: '页面导航', shortcuts: [] },
    actions: { title: '功能操作', shortcuts: [] },
    general: { title: '通用操作', shortcuts: [] }
  }

  shortcuts.value.forEach(shortcut => {
    if (shortcut.key.includes('ctrl+k') || shortcut.key.includes('cmd+k') || shortcut.key === '/') {
      categories.search.shortcuts.push(shortcut)
    } else if (shortcut.key.includes('ctrl+') && /\d/.test(shortcut.key)) {
      categories.navigation.shortcuts.push(shortcut)
    } else if (shortcut.key.includes('shift')) {
      categories.actions.shortcuts.push(shortcut)
    } else {
      categories.general.shortcuts.push(shortcut)
    }
  })

  return Object.values(categories).filter(category => category.shortcuts.length > 0)
})

// 获取分类图标
const getCategoryIcon = (categoryTitle) => {
  const iconMap = {
    '搜索功能': 'fas fa-search',
    '页面导航': 'fas fa-compass',
    '功能操作': 'fas fa-cogs',
    '通用操作': 'fas fa-keyboard'
  }
  return iconMap[categoryTitle] || 'fas fa-keyboard'
}

// 解析快捷键字符串
const parseShortcutKey = (keyString) => {
  return keyString.split('+').map(key => key.trim())
}

// 格式化按键显示
const formatKey = (key) => {
  const keyMap = {
    'ctrl': 'Ctrl',
    'cmd': 'Cmd',
    'shift': 'Shift',
    'alt': 'Alt',
    'space': 'Space',
    'escape': 'Esc',
    'enter': 'Enter',
    'tab': 'Tab',
    'backspace': 'Backspace',
    'delete': 'Delete',
    'arrowup': '↑',
    'arrowdown': '↓',
    'arrowleft': '←',
    'arrowright': '→'
  }
  
  return keyMap[key.toLowerCase()] || key.toUpperCase()
}

// 获取按键样式类
const getKeyClass = (key) => {
  const modifierKeys = ['ctrl', 'cmd', 'shift', 'alt']
  return {
    'modifier-key': modifierKeys.includes(key.toLowerCase()),
    'special-key': ['space', 'escape', 'enter', 'tab'].includes(key.toLowerCase()),
    'arrow-key': key.toLowerCase().startsWith('arrow')
  }
}

// 打印快捷键
const printShortcuts = () => {
  const printContent = generatePrintContent()
  const printWindow = window.open('', '_blank')
  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.print()
}

// 生成打印内容
const generatePrintContent = () => {
  let content = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>质量大盘 - 键盘快捷键</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #4f46e5; border-bottom: 2px solid #4f46e5; padding-bottom: 10px; }
        h2 { color: #374151; margin-top: 30px; }
        .shortcut { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb; }
        .keys { font-weight: bold; }
        kbd { background: #f3f4f6; padding: 2px 6px; border-radius: 4px; font-size: 12px; }
        .description { color: #6b7280; }
      </style>
    </head>
    <body>
      <h1>质量大盘 - 键盘快捷键</h1>
  `

  categorizedShortcuts.value.forEach(category => {
    content += `<h2>${category.title}</h2>`
    category.shortcuts.forEach(shortcut => {
      const keys = parseShortcutKey(shortcut.key).map(key => `<kbd>${formatKey(key)}</kbd>`).join(' + ')
      content += `
        <div class="shortcut">
          <div class="keys">${keys}</div>
          <div class="description">${shortcut.description}</div>
        </div>
      `
    })
  })

  content += `
      <p style="margin-top: 40px; color: #6b7280; font-size: 14px;">
        生成时间: ${new Date().toLocaleString('zh-CN')}
      </p>
    </body>
    </html>
  `

  return content
}
</script>

<style scoped>
.shortcut-help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.shortcut-help-modal {
  background: var(--surface, white);
  border-radius: var(--radius-lg, 12px);
  box-shadow: var(--shadow-xl, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-variant, #f9fafb);
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--on-surface, #1f2937);
  margin: 0;
  display: flex;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  color: var(--gray-500, #6b7280);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--radius, 6px);
  transition: all var(--transition-fast, 0.15s ease);
  font-size: 18px;
}

.close-btn:hover {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.shortcut-intro {
  margin-bottom: 32px;
}

.intro-text {
  font-size: 16px;
  color: var(--on-surface-variant, #6b7280);
  line-height: 1.6;
  margin: 0;
}

.shortcut-categories {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 32px;
}

.shortcut-category {
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius-lg, 12px);
  padding: 24px;
  border: 1px solid var(--border-color, #e5e7eb);
}

.category-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
}

.category-icon {
  width: 20px;
  margin-right: 12px;
  color: var(--primary-600, #4f46e5);
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--surface, white);
  border-radius: var(--radius, 6px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.shortcut-keys {
  display: flex;
  align-items: center;
  gap: 4px;
}

.key {
  background: var(--gray-100, #f3f4f6);
  color: var(--gray-700, #374151);
  padding: 4px 8px;
  border-radius: var(--radius-sm, 4px);
  font-size: 12px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  border: 1px solid var(--gray-300, #d1d5db);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  min-width: 24px;
  text-align: center;
}

.key.modifier-key {
  background: var(--primary-100, #e0e7ff);
  color: var(--primary-700, #4338ca);
  border-color: var(--primary-300, #a5b4fc);
}

.key.special-key {
  background: var(--green-100, #dcfce7);
  color: var(--green-700, #15803d);
  border-color: var(--green-300, #86efac);
}

.key.arrow-key {
  background: var(--yellow-100, #fef3c7);
  color: var(--yellow-700, #a16207);
  border-color: var(--yellow-300, #fcd34d);
}

.shortcut-description {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
  text-align: right;
  flex: 1;
  margin-left: 16px;
}

.shortcut-tips {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px;
  background: var(--surface-variant, #f9fafb);
  border-radius: var(--radius-lg, 12px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.tip-section {
  background: var(--surface, white);
  padding: 20px;
  border-radius: var(--radius, 6px);
  border: 1px solid var(--border-color, #e5e7eb);
}

.tip-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--on-surface, #1f2937);
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-list li {
  padding: 6px 0;
  color: var(--on-surface-variant, #6b7280);
  font-size: 14px;
  line-height: 1.5;
}

.tips-list li::before {
  content: '•';
  color: var(--primary-600, #4f46e5);
  margin-right: 8px;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background: var(--surface-variant, #f9fafb);
}

.footer-info {
  display: flex;
  align-items: center;
}

.version-info {
  font-size: 14px;
  color: var(--on-surface-variant, #6b7280);
  display: flex;
  align-items: center;
}

.footer-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--radius, 6px);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast, 0.15s ease);
  border: none;
  display: flex;
  align-items: center;
}

.btn-primary {
  background: var(--primary-600, #4f46e5);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-700, #4338ca);
}

.btn-secondary {
  background: var(--surface, white);
  color: var(--on-surface-variant, #6b7280);
  border: 1px solid var(--border-color, #e5e7eb);
}

.btn-secondary:hover {
  background: var(--gray-50, #f9fafb);
  color: var(--gray-700, #374151);
}

/* 动画效果 */
.modal-enter-active,
.modal-leave-active {
  transition: all var(--transition-normal, 0.25s ease);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-active .shortcut-help-modal,
.modal-leave-active .shortcut-help-modal {
  transition: all var(--transition-normal, 0.25s ease);
}

.modal-enter-from .shortcut-help-modal,
.modal-leave-to .shortcut-help-modal {
  transform: scale(0.95) translateY(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shortcut-help-overlay {
    padding: 10px;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .modal-content {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .shortcut-description {
    text-align: left;
    margin-left: 0;
  }
  
  .shortcut-tips {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .footer-actions {
    justify-content: center;
  }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .shortcut-help-modal:not([data-theme]) {
    --surface: #1f2937;
    --surface-variant: #374151;
    --on-surface: #f9fafb;
    --on-surface-variant: #d1d5db;
    --border-color: #4b5563;
  }
}
</style>
