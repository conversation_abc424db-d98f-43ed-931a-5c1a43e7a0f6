<template>
  <div 
    ref="imageContainer"
    class="lazy-image-container"
    :class="containerClass"
    :style="containerStyle"
  >
    <!-- 占位符 -->
    <div 
      v-if="!imageLoaded && !imageError"
      class="lazy-image-placeholder"
      :class="placeholderClass"
    >
      <div v-if="showPlaceholder" class="placeholder-content">
        <div class="placeholder-icon">
          <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
          </svg>
        </div>
        <div v-if="showLoadingText" class="placeholder-text">
          {{ loadingText }}
        </div>
      </div>
      
      <!-- 加载动画 -->
      <div v-if="loading && showLoadingAnimation" class="loading-animation">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- 实际图片 -->
    <img
      v-show="imageLoaded"
      ref="image"
      :src="currentSrc"
      :alt="alt"
      :class="imageClass"
      :style="imageStyle"
      @load="onImageLoad"
      @error="onImageError"
    />

    <!-- 错误状态 -->
    <div 
      v-if="imageError"
      class="lazy-image-error"
      :class="errorClass"
    >
      <div class="error-content">
        <div class="error-icon">
          <svg class="w-8 h-8 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="error-text">
          {{ errorText }}
        </div>
        <button 
          v-if="showRetryButton"
          @click="retryLoad"
          class="retry-button"
        >
          重试
        </button>
      </div>
    </div>

    <!-- 渐进式加载效果 -->
    <div 
      v-if="useProgressiveLoading && lowQualityLoaded && !imageLoaded"
      class="progressive-overlay"
    >
      <img
        :src="lowQualitySrc"
        :alt="alt"
        class="progressive-image"
        @load="onLowQualityLoad"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'

const props = defineProps({
  // 图片源
  src: {
    type: String,
    required: true
  },
  // 低质量图片源（用于渐进式加载）
  lowQualitySrc: {
    type: String,
    default: ''
  },
  // 图片描述
  alt: {
    type: String,
    default: ''
  },
  // 容器样式类
  containerClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 图片样式类
  imageClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 占位符样式类
  placeholderClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 错误状态样式类
  errorClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 容器样式
  containerStyle: {
    type: Object,
    default: () => ({})
  },
  // 图片样式
  imageStyle: {
    type: Object,
    default: () => ({})
  },
  // 懒加载根边距
  rootMargin: {
    type: String,
    default: '50px'
  },
  // 懒加载阈值
  threshold: {
    type: Number,
    default: 0.1
  },
  // 是否显示占位符
  showPlaceholder: {
    type: Boolean,
    default: true
  },
  // 是否显示加载文本
  showLoadingText: {
    type: Boolean,
    default: false
  },
  // 加载文本
  loadingText: {
    type: String,
    default: '加载中...'
  },
  // 错误文本
  errorText: {
    type: String,
    default: '图片加载失败'
  },
  // 是否显示重试按钮
  showRetryButton: {
    type: Boolean,
    default: true
  },
  // 是否显示加载动画
  showLoadingAnimation: {
    type: Boolean,
    default: true
  },
  // 是否使用渐进式加载
  useProgressiveLoading: {
    type: Boolean,
    default: false
  },
  // 最大重试次数
  maxRetries: {
    type: Number,
    default: 3
  },
  // 重试延迟（毫秒）
  retryDelay: {
    type: Number,
    default: 1000
  }
})

const emit = defineEmits(['load', 'error', 'retry'])

// 响应式状态
const imageContainer = ref(null)
const image = ref(null)
const imageLoaded = ref(false)
const imageError = ref(false)
const loading = ref(false)
const lowQualityLoaded = ref(false)
const retryCount = ref(0)
const currentSrc = ref('')

// Intersection Observer
let observer = null

// 计算属性
const shouldLoad = computed(() => {
  return currentSrc.value && !imageLoaded.value && !imageError.value
})

// 监听器
watch(() => props.src, (newSrc) => {
  if (newSrc !== currentSrc.value) {
    resetState()
    setupLazyLoading()
  }
})

// 生命周期
onMounted(() => {
  setupLazyLoading()
})

onUnmounted(() => {
  cleanup()
})

// 方法
const setupLazyLoading = () => {
  if (!imageContainer.value) return

  // 检查是否支持 Intersection Observer
  if ('IntersectionObserver' in window) {
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadImage()
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: props.rootMargin,
        threshold: props.threshold
      }
    )
    
    observer.observe(imageContainer.value)
  } else {
    // 降级处理：直接加载图片
    loadImage()
  }
}

const loadImage = async () => {
  if (loading.value || imageLoaded.value) return

  loading.value = true
  imageError.value = false

  try {
    // 如果启用渐进式加载，先加载低质量图片
    if (props.useProgressiveLoading && props.lowQualitySrc && !lowQualityLoaded.value) {
      await loadLowQualityImage()
    }

    // 预加载高质量图片
    await preloadImage(props.src)
    
    currentSrc.value = props.src
    
    // 等待下一个tick确保DOM更新
    await nextTick()
    
  } catch (error) {
    console.error('图片加载失败:', error)
    handleImageError()
  } finally {
    loading.value = false
  }
}

const loadLowQualityImage = () => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      lowQualityLoaded.value = true
      resolve()
    }
    img.onerror = reject
    img.src = props.lowQualitySrc
  })
}

const preloadImage = (src) => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = resolve
    img.onerror = reject
    img.src = src
  })
}

const onImageLoad = () => {
  imageLoaded.value = true
  loading.value = false
  emit('load', {
    src: currentSrc.value,
    element: image.value
  })
}

const onImageError = () => {
  handleImageError()
}

const onLowQualityLoad = () => {
  // 低质量图片加载完成的处理
}

const handleImageError = () => {
  imageError.value = true
  loading.value = false
  emit('error', {
    src: currentSrc.value,
    retryCount: retryCount.value
  })
}

const retryLoad = async () => {
  if (retryCount.value >= props.maxRetries) {
    console.warn('已达到最大重试次数')
    return
  }

  retryCount.value++
  emit('retry', {
    src: currentSrc.value,
    retryCount: retryCount.value
  })

  // 延迟重试
  await new Promise(resolve => setTimeout(resolve, props.retryDelay))
  
  resetState()
  loadImage()
}

const resetState = () => {
  imageLoaded.value = false
  imageError.value = false
  loading.value = false
  lowQualityLoaded.value = false
  currentSrc.value = ''
}

const cleanup = () => {
  if (observer) {
    observer.disconnect()
    observer = null
  }
}
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.lazy-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f3f4f6;
  border-radius: 4px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.placeholder-icon {
  opacity: 0.5;
}

.placeholder-text {
  font-size: 14px;
  color: #6b7280;
}

.loading-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.lazy-image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 4px;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  text-align: center;
}

.error-text {
  font-size: 14px;
  color: #dc2626;
}

.retry-button {
  padding: 4px 12px;
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #b91c1c;
}

.progressive-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.progressive-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(2px);
  opacity: 0.8;
}

/* 图片淡入效果 */
img {
  transition: opacity 0.3s ease-in-out;
}

img[v-show="false"] {
  opacity: 0;
}
</style>
