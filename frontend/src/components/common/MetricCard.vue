<template>
  <div class="metric-card">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium text-gray-700">{{ title }}</h3>
      <span class="text-2xl font-bold" :class="valueColorClass">{{ value }}</span>
    </div>
    
    <!-- 进度条 -->
    <div class="progress-bar mb-4">
      <div
        class="progress-bar-fill"
        :class="progressColorClass"
        :style="{ width: `${progress}%` }"
      ></div>
    </div>
    
    <!-- 底部信息 -->
    <div class="flex justify-between text-sm text-gray-500">
      <span>{{ target }}</span>
      <span :class="changeColorClass">
        <i :class="changeIcon" class="mr-1"></i>
        {{ change }}
      </span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'MetricCard',
  props: {
    title: {
      type: String,
      required: true,
    },
    value: {
      type: String,
      required: true,
    },
    progress: {
      type: Number,
      required: true,
      validator: (value) => value >= 0 && value <= 100,
    },
    target: {
      type: String,
      required: true,
    },
    change: {
      type: String,
      required: true,
    },
    changeType: {
      type: String,
      default: 'neutral',
      validator: (value) => ['positive', 'negative', 'neutral'].includes(value),
    },
    status: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'success', 'warning', 'danger'].includes(value),
    },
  },
  setup(props) {
    const valueColorClass = computed(() => {
      const colorMap = {
        primary: 'text-primary-600',
        success: 'text-success-600',
        warning: 'text-warning-600',
        danger: 'text-danger-600',
      }
      return colorMap[props.status] || colorMap.primary
    })
    
    const progressColorClass = computed(() => {
      const colorMap = {
        primary: 'primary',
        success: 'success',
        warning: 'warning',
        danger: 'danger',
      }
      return colorMap[props.status] || colorMap.primary
    })
    
    const changeColorClass = computed(() => {
      const colorMap = {
        positive: 'text-success-600',
        negative: 'text-danger-600',
        neutral: 'text-gray-600',
      }
      return colorMap[props.changeType] || colorMap.neutral
    })
    
    const changeIcon = computed(() => {
      const iconMap = {
        positive: 'fas fa-arrow-up',
        negative: 'fas fa-arrow-down',
        neutral: 'fas fa-minus',
      }
      return iconMap[props.changeType] || iconMap.neutral
    })
    
    return {
      valueColorClass,
      progressColorClass,
      changeColorClass,
      changeIcon,
    }
  },
}
</script>
