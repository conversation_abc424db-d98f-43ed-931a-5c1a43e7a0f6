<template>
  <div class="alert-dashboard">
    <!-- 预警统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">严重预警</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.level_stats?.critical || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <i class="fas fa-exclamation-circle text-yellow-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">警告预警</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.level_stats?.warning || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <i class="fas fa-check-circle text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">已解决</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.status_stats?.resolved || 0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <i class="fas fa-chart-line text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总预警数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ stats.total_alerts || 0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 预警趋势图表 -->
    <div class="bg-white rounded-lg shadow p-6 mb-8">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-900">预警趋势</h3>
        <div class="flex space-x-2">
          <select
            v-model="selectedDays"
            @change="fetchStats"
            class="form-select text-sm"
          >
            <option value="7">最近7天</option>
            <option value="30">最近30天</option>
            <option value="90">最近90天</option>
          </select>
        </div>
      </div>

      <div class="chart-container" style="height: 300px;">
        <Line
          v-if="chartData"
          :data="chartData"
          :options="chartOptions"
        />
        <div v-else-if="loading.stats" class="flex items-center justify-center h-full">
          <LoadingSpinner />
        </div>
        <div v-else class="flex items-center justify-center h-full text-gray-500">
          暂无数据
        </div>
      </div>
    </div>

    <!-- 预警列表 -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">最新预警</h3>
          <div class="flex space-x-2">
            <!-- 筛选器 -->
            <select
              v-model="filters.level"
              @change="fetchAlerts"
              class="form-select text-sm"
            >
              <option value="">所有级别</option>
              <option value="critical">严重</option>
              <option value="warning">警告</option>
              <option value="info">信息</option>
            </select>

            <select
              v-model="filters.status"
              @change="fetchAlerts"
              class="form-select text-sm"
            >
              <option value="">所有状态</option>
              <option value="active">活跃</option>
              <option value="acknowledged">已确认</option>
              <option value="resolved">已解决</option>
              <option value="dismissed">已忽略</option>
            </select>

            <button
              @click="triggerAlertCheck"
              :disabled="loading.check"
              class="btn btn-primary text-sm"
            >
              <i class="fas fa-sync-alt mr-1" :class="{ 'animate-spin': loading.check }"></i>
              检查预警
            </button>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                预警信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                级别
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                项目
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                触发时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="alert in alerts" :key="alert.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ alert.title }}</div>
                  <div class="text-sm text-gray-500">{{ alert.description }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="getLevelClass(alert.level)"
                >
                  {{ getLevelText(alert.level) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ alert.project_name || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="getStatusClass(alert.status)"
                >
                  {{ getStatusText(alert.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(alert.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    v-if="alert.status === 'active'"
                    @click="acknowledgeAlert(alert.id)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    确认
                  </button>
                  <button
                    v-if="alert.status !== 'resolved'"
                    @click="resolveAlert(alert.id)"
                    class="text-green-600 hover:text-green-900"
                  >
                    解决
                  </button>
                  <button
                    @click="viewAlertDetail(alert)"
                    class="text-gray-600 hover:text-gray-900"
                  >
                    详情
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            显示 {{ (pagination.page - 1) * pagination.page_size + 1 }} 到 
            {{ Math.min(pagination.page * pagination.page_size, pagination.total) }} 条，
            共 {{ pagination.total }} 条
          </div>
          <div class="flex space-x-2">
            <button
              @click="changePage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="btn btn-secondary text-sm"
            >
              上一页
            </button>
            <button
              @click="changePage(pagination.page + 1)"
              :disabled="pagination.page >= pagination.total_pages"
              class="btn btn-secondary text-sm"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 预警详情模态框 -->
    <AlertDetailModal
      v-if="selectedAlert"
      :alert="selectedAlert"
      @close="selectedAlert = null"
      @updated="handleAlertUpdated"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { Line } from 'vue-chartjs'
import { useAlertStore } from '@/stores/alert'
import { useNotificationStore } from '@/stores/notification'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import AlertDetailModal from './AlertDetailModal.vue'

const alertStore = useAlertStore()
const notificationStore = useNotificationStore()

const selectedDays = ref(30)
const selectedAlert = ref(null)
const loading = ref({
  stats: false,
  alerts: false,
  check: false
})

const filters = ref({
  level: '',
  status: '',
  project_id: null
})

const pagination = ref({
  page: 1,
  page_size: 20,
  total: 0,
  total_pages: 0
})

const stats = computed(() => alertStore.stats)
const alerts = computed(() => alertStore.alerts)

const chartData = computed(() => {
  if (!stats.value?.trend_data) return null

  const trendData = stats.value.trend_data
  const labels = trendData.map(item => item.date)
  const data = trendData.map(item => item.count)

  return {
    labels,
    datasets: [{
      label: '预警数量',
      data,
      borderColor: '#ef4444',
      backgroundColor: '#ef444420',
      tension: 0.4,
      fill: true
    }]
  }
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '日期'
      }
    },
    y: {
      display: true,
      title: {
        display: true,
        text: '预警数量'
      },
      beginAtZero: true
    }
  }
}

const fetchStats = async () => {
  loading.value.stats = true
  try {
    await alertStore.fetchStats({
      days: selectedDays.value,
      project_id: filters.value.project_id
    })
  } finally {
    loading.value.stats = false
  }
}

const fetchAlerts = async () => {
  loading.value.alerts = true
  try {
    const params = {
      page: pagination.value.page,
      page_size: pagination.value.page_size,
      ...filters.value
    }
    
    const result = await alertStore.fetchAlerts(params)
    pagination.value = {
      page: result.page,
      page_size: result.page_size,
      total: result.total,
      total_pages: result.total_pages
    }
  } finally {
    loading.value.alerts = false
  }
}

const triggerAlertCheck = async () => {
  loading.value.check = true
  try {
    await alertStore.triggerCheck()
    notificationStore.showSuccess('预警检查已启动')
    // 延迟刷新数据
    setTimeout(() => {
      fetchAlerts()
      fetchStats()
    }, 2000)
  } catch (error) {
    notificationStore.showError('触发预警检查失败')
  } finally {
    loading.value.check = false
  }
}

const acknowledgeAlert = async (alertId) => {
  try {
    await alertStore.acknowledgeAlert(alertId, 'current_user')
    notificationStore.showSuccess('预警确认成功')
    fetchAlerts()
  } catch (error) {
    notificationStore.showError('确认预警失败')
  }
}

const resolveAlert = async (alertId) => {
  try {
    await alertStore.resolveAlert(alertId, 'current_user')
    notificationStore.showSuccess('预警解决成功')
    fetchAlerts()
  } catch (error) {
    notificationStore.showError('解决预警失败')
  }
}

const viewAlertDetail = (alert) => {
  selectedAlert.value = alert
}

const handleAlertUpdated = () => {
  fetchAlerts()
  fetchStats()
}

const changePage = (page) => {
  pagination.value.page = page
  fetchAlerts()
}

const getLevelClass = (level) => {
  const classes = {
    critical: 'bg-red-100 text-red-800',
    warning: 'bg-yellow-100 text-yellow-800',
    info: 'bg-blue-100 text-blue-800'
  }
  return classes[level] || 'bg-gray-100 text-gray-800'
}

const getLevelText = (level) => {
  const texts = {
    critical: '严重',
    warning: '警告',
    info: '信息'
  }
  return texts[level] || level
}

const getStatusClass = (status) => {
  const classes = {
    active: 'bg-red-100 text-red-800',
    acknowledged: 'bg-yellow-100 text-yellow-800',
    resolved: 'bg-green-100 text-green-800',
    dismissed: 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    acknowledged: '已确认',
    resolved: '已解决',
    dismissed: '已忽略'
  }
  return texts[status] || status
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchStats()
  fetchAlerts()
})
</script>

<style scoped>
.alert-dashboard {
  padding: 24px;
}

.chart-container {
  position: relative;
}
</style>
