<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- 标题 -->
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900">
            预警详情
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 预警基本信息 -->
        <div class="bg-gray-50 rounded-lg p-6 mb-6">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h4 class="text-xl font-semibold text-gray-900 mb-2">
                {{ alert.title }}
              </h4>
              <p class="text-gray-600 mb-4">
                {{ alert.description }}
              </p>
              
              <div class="flex items-center space-x-4">
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                      :class="getLevelClass(alert.level)">
                  {{ getLevelText(alert.level) }}
                </span>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                      :class="getStatusClass(alert.status)">
                  {{ getStatusText(alert.status) }}
                </span>
                <span class="text-sm text-gray-500">
                  {{ formatDate(alert.created_at) }}
                </span>
              </div>
            </div>
            
            <div class="ml-6 text-right">
              <div class="text-sm text-gray-500 mb-1">预警类型</div>
              <div class="font-medium">{{ getAlertTypeText(alert.alert_type) }}</div>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- 数值信息 -->
          <div class="bg-white border rounded-lg p-4">
            <h5 class="font-medium text-gray-900 mb-3">数值信息</h5>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">当前值:</span>
                <span class="font-medium">{{ formatValue(alert.current_value) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">历史值:</span>
                <span class="font-medium">{{ formatValue(alert.previous_value) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">阈值:</span>
                <span class="font-medium">{{ formatValue(alert.threshold_value) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">变化率:</span>
                <span class="font-medium" :class="getChangeRateClass(alert.change_rate)">
                  {{ formatPercentage(alert.change_rate) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 项目信息 -->
          <div class="bg-white border rounded-lg p-4">
            <h5 class="font-medium text-gray-900 mb-3">项目信息</h5>
            <div class="space-y-2">
              <div class="flex justify-between">
                <span class="text-gray-600">项目:</span>
                <span class="font-medium">{{ alert.project?.name || '全局' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">规则:</span>
                <span class="font-medium">{{ alert.rule?.name || '-' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">创建时间:</span>
                <span class="font-medium">{{ formatDate(alert.created_at) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">更新时间:</span>
                <span class="font-medium">{{ formatDate(alert.updated_at) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 建议操作 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h5 class="font-medium text-blue-900 mb-3">
            <i class="fas fa-lightbulb mr-2"></i>
            建议操作
          </h5>
          <ul class="space-y-1">
            <li v-for="action in alert.suggested_actions" :key="action" class="text-blue-800">
              <i class="fas fa-check-circle mr-2 text-blue-600"></i>
              {{ action }}
            </li>
          </ul>
        </div>

        <!-- 处理记录 -->
        <div v-if="alert.acknowledged_at || alert.resolved_at" class="bg-gray-50 rounded-lg p-4 mb-6">
          <h5 class="font-medium text-gray-900 mb-3">处理记录</h5>
          <div class="space-y-2">
            <div v-if="alert.acknowledged_at" class="flex justify-between">
              <span class="text-gray-600">确认时间:</span>
              <span class="font-medium">{{ formatDate(alert.acknowledged_at) }}</span>
            </div>
            <div v-if="alert.acknowledged_by" class="flex justify-between">
              <span class="text-gray-600">确认人:</span>
              <span class="font-medium">{{ alert.acknowledged_by }}</span>
            </div>
            <div v-if="alert.resolved_at" class="flex justify-between">
              <span class="text-gray-600">解决时间:</span>
              <span class="font-medium">{{ formatDate(alert.resolved_at) }}</span>
            </div>
            <div v-if="alert.resolved_by" class="flex justify-between">
              <span class="text-gray-600">解决人:</span>
              <span class="font-medium">{{ alert.resolved_by }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3 pt-6 border-t">
          <button
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            关闭
          </button>
          
          <button
            v-if="alert.status === 'active'"
            @click="acknowledgeAlert"
            :disabled="loading.acknowledge"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <i v-if="loading.acknowledge" class="fas fa-spinner fa-spin mr-2"></i>
            确认预警
          </button>
          
          <button
            v-if="alert.status !== 'resolved'"
            @click="resolveAlert"
            :disabled="loading.resolve"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            <i v-if="loading.resolve" class="fas fa-spinner fa-spin mr-2"></i>
            解决预警
          </button>
          
          <button
            v-if="alert.status === 'active'"
            @click="dismissAlert"
            :disabled="loading.dismiss"
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
          >
            <i v-if="loading.dismiss" class="fas fa-spinner fa-spin mr-2"></i>
            忽略预警
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAlertStore } from '@/stores/alert'
import { useNotificationStore } from '@/stores/notification'

const props = defineProps({
  alert: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'updated'])

const alertStore = useAlertStore()
const notificationStore = useNotificationStore()

const loading = ref({
  acknowledge: false,
  resolve: false,
  dismiss: false
})

// 方法
const acknowledgeAlert = async () => {
  loading.value.acknowledge = true
  try {
    await alertStore.acknowledgeAlert(props.alert.id, 'current_user')
    notificationStore.showSuccess('预警确认成功')
    emit('updated')
    emit('close')
  } catch (error) {
    notificationStore.showError('确认预警失败')
  } finally {
    loading.value.acknowledge = false
  }
}

const resolveAlert = async () => {
  loading.value.resolve = true
  try {
    await alertStore.resolveAlert(props.alert.id, 'current_user')
    notificationStore.showSuccess('预警解决成功')
    emit('updated')
    emit('close')
  } catch (error) {
    notificationStore.showError('解决预警失败')
  } finally {
    loading.value.resolve = false
  }
}

const dismissAlert = async () => {
  loading.value.dismiss = true
  try {
    await alertStore.dismissAlert(props.alert.id, 'current_user')
    notificationStore.showSuccess('预警已忽略')
    emit('updated')
    emit('close')
  } catch (error) {
    notificationStore.showError('忽略预警失败')
  } finally {
    loading.value.dismiss = false
  }
}

// 工具方法
const getLevelClass = (level) => {
  const classes = {
    critical: 'bg-red-100 text-red-800',
    warning: 'bg-yellow-100 text-yellow-800',
    info: 'bg-blue-100 text-blue-800'
  }
  return classes[level] || 'bg-gray-100 text-gray-800'
}

const getLevelText = (level) => {
  const texts = {
    critical: '严重',
    warning: '警告',
    info: '信息'
  }
  return texts[level] || level
}

const getStatusClass = (status) => {
  const classes = {
    active: 'bg-red-100 text-red-800',
    acknowledged: 'bg-yellow-100 text-yellow-800',
    resolved: 'bg-green-100 text-green-800',
    dismissed: 'bg-gray-100 text-gray-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    active: '活跃',
    acknowledged: '已确认',
    resolved: '已解决',
    dismissed: '已忽略'
  }
  return texts[status] || status
}

const getAlertTypeText = (type) => {
  const types = {
    'defect_spike': '缺陷激增',
    'coverage_drop': '覆盖率下降',
    'quality_decline': '质量下降'
  }
  return types[type] || type
}

const getChangeRateClass = (rate) => {
  if (rate > 0) return 'text-red-600'
  if (rate < 0) return 'text-green-600'
  return 'text-gray-600'
}

const formatValue = (value) => {
  if (value === null || value === undefined) return '-'
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value
}

const formatPercentage = (value) => {
  if (value === null || value === undefined) return '-'
  return `${(value * 100).toFixed(1)}%`
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
/* 模态框样式已在模板中使用 Tailwind CSS */
</style>
