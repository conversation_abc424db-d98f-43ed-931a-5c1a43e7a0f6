<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- 标题 -->
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ rule ? '编辑预警规则' : '新建预警规则' }}
          </h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600"
          >
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>

        <!-- 表单 -->
        <form @submit.prevent="saveRule" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                规则名称 <span class="text-red-500">*</span>
              </label>
              <input
                v-model="formData.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入规则名称"
              >
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                预警类型 <span class="text-red-500">*</span>
              </label>
              <select
                v-model="formData.alert_type"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择预警类型</option>
                <option value="defect_spike">缺陷激增</option>
                <option value="coverage_drop">覆盖率下降</option>
                <option value="quality_decline">质量下降</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              规则描述
            </label>
            <textarea
              v-model="formData.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入规则描述"
            ></textarea>
          </div>

          <!-- 阈值配置 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">阈值配置</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  比较操作符 <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="formData.threshold_operator"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择</option>
                  <option value=">">大于 (&gt;)</option>
                  <option value=">=">大于等于 (&gt;=)</option>
                  <option value="<">小于 (&lt;)</option>
                  <option value="<=">小于等于 (&lt;=)</option>
                  <option value="==">等于 (==)</option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  阈值 <span class="text-red-500">*</span>
                </label>
                <input
                  v-model.number="formData.threshold_value"
                  type="number"
                  step="0.01"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入阈值"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  时间窗口（分钟）
                </label>
                <input
                  v-model.number="formData.window_minutes"
                  type="number"
                  min="1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="60"
                >
              </div>
            </div>
          </div>

          <!-- 目标配置 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">目标配置</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  项目
                </label>
                <select
                  v-model="formData.project_id"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">全局规则</option>
                  <option
                    v-for="project in projects"
                    :key="project.id"
                    :value="project.id"
                  >
                    {{ project.name }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  预警级别 <span class="text-red-500">*</span>
                </label>
                <select
                  v-model="formData.level"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">请选择级别</option>
                  <option value="info">信息</option>
                  <option value="warning">警告</option>
                  <option value="critical">严重</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 通知配置 -->
          <div class="border-t pt-6">
            <h4 class="text-md font-medium text-gray-900 mb-4">通知配置</h4>
            
            <div class="space-y-4">
              <div class="flex items-center space-x-4">
                <label class="flex items-center">
                  <input
                    v-model="notificationTypes"
                    value="email"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-sm text-gray-700">邮件通知</span>
                </label>

                <label class="flex items-center">
                  <input
                    v-model="notificationTypes"
                    value="webhook"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-sm text-gray-700">Webhook</span>
                </label>

                <label class="flex items-center">
                  <input
                    v-model="notificationTypes"
                    value="dingtalk"
                    type="checkbox"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  >
                  <span class="ml-2 text-sm text-gray-700">钉钉通知</span>
                </label>
              </div>

              <div v-if="notificationTypes.includes('email')">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  邮件地址
                </label>
                <input
                  v-model="emailConfig.recipients"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="多个邮箱用逗号分隔"
                >
              </div>

              <div v-if="notificationTypes.includes('webhook')">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Webhook URL
                </label>
                <input
                  v-model="webhookConfig.url"
                  type="url"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://example.com/webhook"
                >
              </div>

              <div v-if="notificationTypes.includes('dingtalk')">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  钉钉 Webhook URL
                </label>
                <input
                  v-model="dingtalkConfig.webhook_url"
                  type="url"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://oapi.dingtalk.com/robot/send?access_token=..."
                >
              </div>
            </div>
          </div>

          <!-- 规则状态 -->
          <div class="border-t pt-6">
            <label class="flex items-center">
              <input
                v-model="formData.is_enabled"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              >
              <span class="ml-2 text-sm text-gray-700">启用此规则</span>
            </label>
          </div>

          <!-- 按钮 -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
              {{ rule ? '更新' : '创建' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useProjectStore } from '@/stores/project'
import { useAlertStore } from '@/stores/alert'

const props = defineProps({
  rule: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['close', 'saved'])

const projectStore = useProjectStore()
const alertStore = useAlertStore()

// 响应式数据
const loading = ref(false)
const notificationTypes = ref([])
const emailConfig = ref({ recipients: '' })
const webhookConfig = ref({ url: '' })
const dingtalkConfig = ref({ webhook_url: '' })

const formData = ref({
  name: '',
  description: '',
  alert_type: '',
  level: '',
  threshold_value: null,
  threshold_operator: '',
  window_minutes: 60,
  project_id: null,
  is_enabled: true,
  notification_config: {}
})

// 计算属性
const projects = computed(() => projectStore.projects)

// 监听器
watch(() => props.rule, (newRule) => {
  if (newRule) {
    formData.value = { ...newRule }
    
    // 解析通知配置
    const config = newRule.notification_config || {}
    notificationTypes.value = config.types || []
    emailConfig.value = config.email || { recipients: '' }
    webhookConfig.value = config.webhook || { url: '' }
    dingtalkConfig.value = config.dingtalk || { webhook_url: '' }
  }
}, { immediate: true })

// 方法
const saveRule = async () => {
  loading.value = true
  try {
    // 构建通知配置
    const notification_config = {
      types: notificationTypes.value,
      email: emailConfig.value,
      webhook: webhookConfig.value,
      dingtalk: dingtalkConfig.value
    }

    const ruleData = {
      ...formData.value,
      notification_config
    }

    if (props.rule) {
      await alertStore.updateRule(props.rule.id, ruleData)
    } else {
      await alertStore.createRule(ruleData)
    }

    emit('saved')
  } catch (error) {
    console.error('保存规则失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  await projectStore.fetchProjects()
})
</script>

<style scoped>
/* 模态框样式已在模板中使用 Tailwind CSS */
</style>
