<template>
  <header class="fixed top-0 left-0 right-0 bg-white shadow-lg z-50">
    <div class="px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- 左侧：动态标题和下拉菜单 -->
        <div class="relative">
          <button
            @click="toggleDropdown"
            class="flex items-center px-4 py-2 text-xl font-bold text-gray-800 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg"
            :class="{ 'text-primary-600': dropdownOpen }"
          >
            <i :class="currentPageInfo.icon" class="text-primary-600 text-2xl mr-3"></i>
            <span>{{ currentPageInfo.label }}</span>
          </button>

          <!-- 下拉菜单 -->
          <transition
            enter-active-class="transition ease-out duration-200"
            enter-from-class="opacity-0 scale-95"
            enter-to-class="opacity-100 scale-100"
            leave-active-class="transition ease-in duration-150"
            leave-from-class="opacity-100 scale-100"
            leave-to-class="opacity-0 scale-95"
          >
            <div
              v-show="dropdownOpen"
              class="absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50"
              @click.stop
            >
              <router-link
                v-for="item in navigationItems"
                :key="item.name"
                :to="item.path"
                class="flex items-center px-4 py-3 text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200"
                :class="{ 'bg-primary-50 text-primary-600 font-medium': $route.name === item.name }"
                @click="closeDropdown"
              >
                <i :class="item.icon" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                <span>{{ item.label }}</span>
              </router-link>
            </div>
          </transition>
        </div>

        <!-- 右侧：可以添加其他功能按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 预留空间，可以添加搜索、通知等功能 -->
        </div>
      </div>
    </div>

    <!-- 点击遮罩层关闭下拉菜单 -->
    <div
      v-if="dropdownOpen"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </header>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

export default {
  name: 'Header',
  setup() {
    const route = useRoute()
    const dropdownOpen = ref(false)
    
    const navigationItems = [
      {
        name: 'Home',
        label: '首页',
        path: '/',
        icon: 'fas fa-home',
      },
      {
        name: 'Dashboard',
        label: '质量大盘',
        path: '/dashboard',
        icon: 'fas fa-chart-pie',
      },
      {
        name: 'Automation',
        label: '自动化测试',
        path: '/automation',
        icon: 'fas fa-robot',
      },
      {
        name: 'Performance',
        label: '性能监控',
        path: '/performance',
        icon: 'fas fa-tachometer-alt',
      },
      {
        name: 'QualityGate',
        label: '质量门禁',
        path: '/quality-gate',
        icon: 'fas fa-shield-alt',
      },
      {
        name: 'DefectManagement',
        label: '缺陷管理',
        path: '/defect-management',
        icon: 'fas fa-bug',
      },
      {
        name: 'CoverageManagement',
        label: '测试覆盖率',
        path: '/coverage-management',
        icon: 'fas fa-chart-line',
      },
      {
        name: 'AlertManagement',
        label: '质量预警',
        path: '/alert-management',
        icon: 'fas fa-exclamation-triangle',
      },
      {
        name: 'ReportManagement',
        label: '数据导出',
        path: '/report-management',
        icon: 'fas fa-file-export',
      },
      {
        name: 'IntegrationManagement',
        label: '集成管理',
        path: '/integration-management',
        icon: 'fas fa-plug',
      },
      {
        name: 'PerformanceMonitoring',
        label: '性能监控',
        path: '/performance-monitoring',
        icon: 'fas fa-chart-area',
      },
    ]

    // 计算当前页面信息
    const currentPageInfo = computed(() => {
      // 根据当前路由名称查找对应的导航项
      const currentItem = navigationItems.find(item => item.name === route.name)

      // 如果找到匹配的导航项，返回该项的信息
      if (currentItem) {
        return {
          label: currentItem.label,
          icon: currentItem.icon
        }
      }

      // 默认情况（首页或未匹配的路由）显示"质量大盘"
      return {
        label: '质量大盘',
        icon: 'fas fa-chart-line'
      }
    })
    
    const toggleDropdown = () => {
      dropdownOpen.value = !dropdownOpen.value
    }
    
    const closeDropdown = () => {
      dropdownOpen.value = false
    }
    
    // 键盘事件处理
    const handleKeydown = (event) => {
      if (event.key === 'Escape') {
        closeDropdown()
      }
    }
    
    onMounted(() => {
      document.addEventListener('keydown', handleKeydown)
    })
    
    onUnmounted(() => {
      document.removeEventListener('keydown', handleKeydown)
    })
    
    return {
      dropdownOpen,
      navigationItems,
      currentPageInfo,
      toggleDropdown,
      closeDropdown,
    }
  },
}
</script>

<style scoped>
/* 确保下拉菜单在最上层 */
.z-50 {
  z-index: 50;
}

/* 下拉菜单项悬停效果 */
.router-link-active {
  @apply bg-primary-50 text-primary-600 font-medium;
}
</style>
