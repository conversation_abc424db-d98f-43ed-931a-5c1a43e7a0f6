<template>
  <!-- 桌面端侧边栏 -->
  <nav class="hidden md:flex flex-col bg-white shadow-lg h-screen w-72 fixed left-0 top-0 z-50">
    <!-- Logo和标题 -->
    <div class="flex items-center px-6 py-4 border-b border-gray-200">
      <router-link to="/" class="flex items-center">
        <i class="fas fa-chart-line text-primary-600 text-2xl"></i>
        <span class="ml-3 text-xl font-bold text-gray-800">质量大盘</span>
      </router-link>
    </div>

    <!-- 导航菜单 -->
    <div class="flex-1 px-4 py-6 overflow-y-auto">
      <nav class="space-y-2">
        <router-link
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.path"
          class="sidebar-nav-btn"
          :class="{ 'active': $route.name === item.name }"
        >
          <i :class="item.icon" class="w-5 h-5"></i>
          <span>{{ item.label }}</span>
        </router-link>
      </nav>
    </div>
  </nav>

  <!-- 移动端导航 -->
  <nav class="md:hidden bg-white shadow-sm sticky top-0 z-50">
    <div class="px-4 sm:px-6">
      <div class="flex justify-between h-16">
        <!-- Logo和标题 -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center">
            <i class="fas fa-chart-line text-primary-600 text-2xl"></i>
            <span class="ml-2 text-xl font-bold text-gray-800">质量大盘</span>
          </router-link>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="flex items-center">
          <button
            @click="toggleMobileMenu"
            class="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <i class="fas fa-bars text-xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div
      v-show="mobileMenuOpen"
      class="md:hidden bg-white shadow-md"
      @click="closeMobileMenu"
    >
      <div class="px-2 pt-2 pb-3 space-y-1">
        <router-link
          v-for="item in navigationItems"
          :key="item.name"
          :to="item.path"
          class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-primary-50"
          :class="{ 'text-primary-600 bg-primary-50': $route.name === item.name }"
          @click="closeMobileMenu"
        >
          <i :class="item.icon" class="mr-2"></i>
          {{ item.label }}
        </router-link>
      </div>
    </div>
  </nav>

  <!-- 移动端菜单覆盖层 -->
  <div
    v-if="mobileMenuOpen"
    class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
    @click="closeMobileMenu"
  ></div>
</template>

<script>
import { ref } from 'vue'
import ThemeToggle from '@/components/common/ThemeToggle.vue'
import GlobalSearch from '@/components/search/GlobalSearch.vue'

export default {
  name: 'NavBar',
  components: {
    ThemeToggle,
    GlobalSearch
  },
  setup() {
    const mobileMenuOpen = ref(false)
    const showMobileSearch = ref(false)
    
    const navigationItems = [
      {
        name: 'Home',
        label: '首页',
        path: '/',
        icon: 'fas fa-home',
      },
      {
        name: 'Dashboard',
        label: '质量大盘',
        path: '/dashboard',
        icon: 'fas fa-chart-pie',
      },
      {
        name: 'Automation',
        label: '自动化测试',
        path: '/automation',
        icon: 'fas fa-robot',
      },
      {
        name: 'Performance',
        label: '性能监控',
        path: '/performance',
        icon: 'fas fa-tachometer-alt',
      },
      {
        name: 'QualityGate',
        label: '质量门禁',
        path: '/quality-gate',
        icon: 'fas fa-shield-alt',
      },
      {
        name: 'DefectManagement',
        label: '缺陷管理',
        path: '/defect-management',
        icon: 'fas fa-bug',
      },
      {
        name: 'CoverageManagement',
        label: '测试覆盖率',
        path: '/coverage-management',
        icon: 'fas fa-chart-line',
      },
      {
        name: 'AlertManagement',
        label: '质量预警',
        path: '/alert-management',
        icon: 'fas fa-exclamation-triangle',
      },
      {
        name: 'ReportManagement',
        label: '数据导出',
        path: '/report-management',
        icon: 'fas fa-file-export',
      },
    ]
    
    const toggleMobileMenu = () => {
      mobileMenuOpen.value = !mobileMenuOpen.value
    }
    
    const closeMobileMenu = () => {
      mobileMenuOpen.value = false
    }
    
    return {
      mobileMenuOpen,
      showMobileSearch,
      navigationItems,
      toggleMobileMenu,
      closeMobileMenu,
    }
  },
}
</script>

<style scoped>
/* 侧边栏导航按钮样式 */
.sidebar-nav-btn {
  @apply flex items-center px-4 py-3 rounded-lg transition-all duration-200 ease-in-out text-gray-700 hover:bg-primary-50 hover:text-primary-600;
}

.sidebar-nav-btn.active {
  @apply bg-primary-100 text-primary-700 font-medium border-r-2 border-primary-600;
}

.sidebar-nav-btn i {
  @apply mr-3 flex-shrink-0;
}

.sidebar-nav-btn span {
  @apply truncate;
}

/* 移动端导航按钮样式 */
.nav-btn {
  @apply px-4 py-2 rounded-lg transition-all duration-200 ease-in-out text-gray-700;
}

.nav-btn:hover {
  @apply bg-primary-50 text-primary-600;
}

.nav-btn.active {
  @apply bg-primary-100 text-primary-700 font-medium;
}

/* 侧边栏滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
