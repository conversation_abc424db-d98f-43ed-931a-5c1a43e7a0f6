<template>
  <footer class="bg-gray-800 text-white py-4">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 版权信息 -->
      <div class="text-center text-gray-400">
        <p>© {{ currentYear }} 测试部. 保留所有权利.</p>
        <p class="mt-1 text-sm">
          <!-- 基于 Vue.js 3 + FastAPI 构建 -->
        </p>
      </div>
    </div>
  </footer>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'AppFooter',
  setup() {
    const currentYear = computed(() => new Date().getFullYear())
    
    return {
      currentYear,
    }
  },
}
</script>
