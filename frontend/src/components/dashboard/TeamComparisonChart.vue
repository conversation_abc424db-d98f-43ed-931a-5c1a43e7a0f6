<template>
  <div class="team-comparison-chart">
    <div class="chart-header">
      <h3 class="chart-title">{{ config.title || '团队质量对比' }}</h3>
      <div class="chart-controls">
        <select v-model="selectedMetric" class="metric-select">
          <option value="coverage">覆盖率</option>
          <option value="defects">缺陷数量</option>
          <option value="performance">性能指标</option>
          <option value="quality_score">质量评分</option>
        </select>
        <button @click="refreshData" class="refresh-btn" title="刷新数据">
          🔄
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <span>加载团队数据中...</span>
    </div>

    <div v-else class="chart-content">
      <!-- 图表容器 -->
      <div ref="chartContainer" class="chart-container"></div>

      <!-- 详细信息面板 -->
      <div v-if="config.showDetails && selectedTeam" class="details-panel">
        <div class="details-header">
          <h4>{{ selectedTeam.name }} 详细信息</h4>
          <button @click="selectedTeam = null" class="close-details">×</button>
        </div>
        <div class="details-content">
          <div class="detail-item">
            <span class="detail-label">团队负责人:</span>
            <span class="detail-value">{{ selectedTeam.lead_name }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">团队成员:</span>
            <span class="detail-value">{{ selectedTeam.member_count }} 人</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">当前指标:</span>
            <span class="detail-value">{{ getMetricValue(selectedTeam) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">排名:</span>
            <span class="detail-value">第 {{ selectedTeam.rank }} 名</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">趋势:</span>
            <span :class="`trend-${selectedTeam.trend}`">
              {{ getTrendText(selectedTeam.trend) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 团队列表 -->
      <div class="teams-list">
        <div class="list-header">
          <span>团队排名</span>
          <span>{{ getMetricLabel(selectedMetric) }}</span>
        </div>
        <div
          v-for="(team, index) in sortedTeams"
          :key="team.id"
          class="team-item"
          :class="{ 'selected': selectedTeam?.id === team.id }"
          @click="selectTeam(team)"
        >
          <div class="team-rank">{{ index + 1 }}</div>
          <div class="team-info">
            <div class="team-name">{{ team.name }}</div>
            <div class="team-meta">{{ team.member_count }} 人</div>
          </div>
          <div class="team-metric">
            <div class="metric-value">{{ getMetricValue(team) }}</div>
            <div class="metric-trend" :class="`trend-${team.trend}`">
              {{ getTrendIcon(team.trend) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      showDetails: true,
      compareMetrics: ['coverage', 'defects', 'performance']
    })
  },
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['team-select', 'metric-change'])

const chartContainer = ref(null)
const selectedMetric = ref('coverage')
const selectedTeam = ref(null)
let chartInstance = null

// 计算属性
const teamsData = computed(() => {
  if (props.data && props.data.length > 0) {
    return props.data
  }
  return getDefaultTeamsData()
})

const sortedTeams = computed(() => {
  return [...teamsData.value]
    .sort((a, b) => {
      const aValue = getMetricNumericValue(a)
      const bValue = getMetricNumericValue(b)
      return bValue - aValue // 降序排列
    })
    .map((team, index) => ({
      ...team,
      rank: index + 1
    }))
})

// 方法
const getMetricLabel = (metric) => {
  const labels = {
    coverage: '覆盖率',
    defects: '缺陷数量',
    performance: '性能指标',
    quality_score: '质量评分'
  }
  return labels[metric] || metric
}

const getMetricValue = (team) => {
  const metric = selectedMetric.value
  switch (metric) {
    case 'coverage':
      return `${team.coverage || 0}%`
    case 'defects':
      return `${team.defects || 0} 个`
    case 'performance':
      return `${team.performance || 0} ms`
    case 'quality_score':
      return `${team.quality_score || 0} 分`
    default:
      return '0'
  }
}

const getMetricNumericValue = (team) => {
  const metric = selectedMetric.value
  switch (metric) {
    case 'coverage':
      return team.coverage || 0
    case 'defects':
      return -(team.defects || 0) // 缺陷数量越少越好，所以取负值
    case 'performance':
      return -(team.performance || 0) // 响应时间越短越好，所以取负值
    case 'quality_score':
      return team.quality_score || 0
    default:
      return 0
  }
}

const getTrendIcon = (trend) => {
  switch (trend) {
    case 'up': return '📈'
    case 'down': return '📉'
    default: return '➡️'
  }
}

const getTrendText = (trend) => {
  switch (trend) {
    case 'up': return '上升趋势'
    case 'down': return '下降趋势'
    default: return '保持稳定'
  }
}

const selectTeam = (team) => {
  selectedTeam.value = team
  emit('team-select', team)
}

const refreshData = () => {
  // 触发数据刷新
  emit('metric-change', selectedMetric.value)
}

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return

  const teams = sortedTeams.value
  const metric = selectedMetric.value

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params) => {
        const data = params[0]
        const team = teams[data.dataIndex]
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${team.name}</div>
            <div>负责人: ${team.lead_name}</div>
            <div>成员: ${team.member_count} 人</div>
            <div>${getMetricLabel(metric)}: ${getMetricValue(team)}</div>
          </div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: teams.map(team => team.name),
      axisLabel: {
        rotate: 45,
        fontSize: 12
      }
    },
    yAxis: {
      type: 'value',
      name: getMetricLabel(metric),
      nameTextStyle: {
        fontSize: 12
      }
    },
    series: [
      {
        name: getMetricLabel(metric),
        type: 'bar',
        data: teams.map(team => ({
          value: Math.abs(getMetricNumericValue(team)),
          itemStyle: {
            color: getBarColor(team, metric)
          }
        })),
        barWidth: '60%',
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)

  // 添加点击事件
  chartInstance.off('click')
  chartInstance.on('click', (params) => {
    const team = teams[params.dataIndex]
    selectTeam(team)
  })
}

const getBarColor = (team, metric) => {
  const value = getMetricNumericValue(team)
  const maxValue = Math.max(...teamsData.value.map(t => Math.abs(getMetricNumericValue(t))))
  const ratio = Math.abs(value) / maxValue

  if (ratio >= 0.8) return '#10b981' // 绿色 - 优秀
  if (ratio >= 0.6) return '#f59e0b' // 黄色 - 良好
  return '#ef4444' // 红色 - 需要改进
}

const getDefaultTeamsData = () => {
  return [
    {
      id: 1,
      name: '前端团队',
      lead_name: '张三',
      member_count: 8,
      coverage: 85,
      defects: 12,
      performance: 120,
      quality_score: 88,
      trend: 'up'
    },
    {
      id: 2,
      name: '后端团队',
      lead_name: '李四',
      member_count: 10,
      coverage: 78,
      defects: 18,
      performance: 95,
      quality_score: 82,
      trend: 'up'
    },
    {
      id: 3,
      name: '测试团队',
      lead_name: '王五',
      member_count: 6,
      coverage: 92,
      defects: 8,
      performance: 110,
      quality_score: 91,
      trend: 'stable'
    },
    {
      id: 4,
      name: '运维团队',
      lead_name: '赵六',
      member_count: 5,
      coverage: 70,
      defects: 25,
      performance: 85,
      quality_score: 75,
      trend: 'down'
    },
    {
      id: 5,
      name: '产品团队',
      lead_name: '钱七',
      member_count: 4,
      coverage: 65,
      defects: 30,
      performance: 140,
      quality_score: 68,
      trend: 'down'
    }
  ]
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

// 监听器
watch(selectedMetric, () => {
  updateChart()
  emit('metric-change', selectedMetric.value)
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 组件卸载时销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.team-comparison-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.refresh-btn {
  background: none;
  border: 1px solid #d1d5db;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 12px;
  color: #6b7280;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.chart-content {
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
}

.chart-container {
  flex: 2;
  min-height: 300px;
}

.details-panel {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  overflow: hidden;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #e5e7eb;
  border-bottom: 1px solid #d1d5db;
}

.details-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.close-details {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-details:hover {
  background: #d1d5db;
}

.details-content {
  padding: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.teams-list {
  flex: 1;
  background: #f9fafb;
  border-radius: 8px;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #e5e7eb;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.team-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
}

.team-item:hover {
  background: #f3f4f6;
}

.team-item.selected {
  background: #dbeafe;
  border-left: 3px solid #3b82f6;
}

.team-rank {
  width: 24px;
  height: 24px;
  background: #6b7280;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.team-item.selected .team-rank {
  background: #3b82f6;
}

.team-info {
  flex: 1;
}

.team-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.team-meta {
  font-size: 12px;
  color: #6b7280;
}

.team-metric {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.metric-trend {
  font-size: 16px;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-stable {
  color: #6b7280;
}
</style>
