<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">配置管理</h3>
        <button @click="$emit('close')" class="close-btn">×</button>
      </div>

      <div class="modal-body">
        <!-- 当前配置列表 -->
        <div class="config-section">
          <h4 class="section-title">我的配置</h4>
          <div v-if="loading" class="loading-state">
            <div class="spinner"></div>
            <span>加载中...</span>
          </div>
          <div v-else-if="configs.length === 0" class="empty-state">
            <div class="empty-icon">📋</div>
            <p>暂无保存的配置</p>
          </div>
          <div v-else class="config-list">
            <div
              v-for="config in configs"
              :key="config.id"
              class="config-item"
              :class="{ 'is-default': config.is_default }"
            >
              <div class="config-info">
                <div class="config-name">
                  {{ config.config_name }}
                  <span v-if="config.is_default" class="default-badge">默认</span>
                </div>
                <div class="config-meta">
                  创建时间: {{ formatDate(config.created_at) }}
                </div>
              </div>
              <div class="config-actions">
                <button
                  @click="loadConfig(config.id)"
                  class="action-btn load-btn"
                  title="加载配置"
                >
                  📂
                </button>
                <button
                  v-if="!config.is_default"
                  @click="setAsDefault(config.id)"
                  class="action-btn default-btn"
                  title="设为默认"
                >
                  ⭐
                </button>
                <button
                  @click="deleteConfig(config.id)"
                  class="action-btn delete-btn"
                  title="删除配置"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 保存新配置 -->
        <div class="config-section">
          <h4 class="section-title">保存当前配置</h4>
          <div class="save-config-form">
            <div class="form-group">
              <label for="configName" class="form-label">配置名称</label>
              <input
                id="configName"
                v-model="newConfigName"
                type="text"
                class="form-input"
                placeholder="输入配置名称"
                @keyup.enter="saveNewConfig"
              />
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="setAsDefaultConfig"
                  type="checkbox"
                  class="form-checkbox"
                />
                设为默认配置
              </label>
            </div>
            <button
              @click="saveNewConfig"
              :disabled="!newConfigName.trim() || saving"
              class="save-btn"
            >
              <span v-if="saving">保存中...</span>
              <span v-else>💾 保存配置</span>
            </button>
          </div>
        </div>

        <!-- 导入导出 -->
        <div class="config-section">
          <h4 class="section-title">导入/导出</h4>

          <!-- 导出配置 -->
          <div class="export-section">
            <h5 class="subsection-title">导出配置</h5>
            <p class="section-description">
              将当前布局配置导出为JSON文件，可以在其他设备或用户间共享
            </p>
            <div class="export-options">
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="exportOptions.includeLayout"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  包含布局配置
                </label>
              </div>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="exportOptions.includeWidgets"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  包含组件配置
                </label>
              </div>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="exportOptions.includeFocusedMetrics"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  包含关注指标
                </label>
              </div>
            </div>
            <button
              @click="exportConfig"
              :disabled="exporting"
              class="action-btn export-btn"
            >
              <span v-if="exporting">📤 导出中...</span>
              <span v-else>📤 导出配置</span>
            </button>
          </div>

          <!-- 导入配置 -->
          <div class="import-section">
            <h5 class="subsection-title">导入配置</h5>
            <p class="section-description">
              从JSON文件导入布局配置，将覆盖当前配置
            </p>
            <div class="import-area">
              <input
                ref="fileInput"
                type="file"
                accept=".json"
                @change="handleFileSelect"
                class="file-input"
              />
              <div
                @click="$refs.fileInput.click()"
                @dragover.prevent
                @drop.prevent="handleFileDrop"
                class="file-drop-zone"
                :class="{ 'drag-over': isDragOver }"
                @dragenter="isDragOver = true"
                @dragleave="isDragOver = false"
              >
                <div class="drop-zone-content">
                  <div class="drop-zone-icon">📁</div>
                  <p class="drop-zone-text">点击选择文件或拖拽文件到此处</p>
                  <p class="drop-zone-hint">支持 .json 格式</p>
                </div>
              </div>

              <div v-if="selectedFile" class="selected-file">
                <div class="file-info">
                  <span class="file-icon">📄</span>
                  <div class="file-details">
                    <span class="file-name">{{ selectedFile.name }}</span>
                    <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
                  </div>
                </div>
                <button
                  @click="clearSelectedFile"
                  class="clear-file-btn"
                  title="清除文件"
                >
                  ❌
                </button>
              </div>

              <div v-if="importPreview" class="import-preview">
                <h6 class="preview-title">配置预览</h6>
                <div class="preview-content">
                  <div class="preview-item">
                    <span class="preview-label">配置名称:</span>
                    <span class="preview-value">{{ importPreview.name || '未命名配置' }}</span>
                  </div>
                  <div class="preview-item">
                    <span class="preview-label">布局组件:</span>
                    <span class="preview-value">{{ importPreview.layout?.length || 0 }} 个</span>
                  </div>
                  <div class="preview-item">
                    <span class="preview-label">组件配置:</span>
                    <span class="preview-value">{{ importPreview.widgets?.length || 0 }} 个</span>
                  </div>
                  <div class="preview-item">
                    <span class="preview-label">导出时间:</span>
                    <span class="preview-value">{{ formatDate(importPreview.exportTime) }}</span>
                  </div>
                </div>
              </div>

              <button
                @click="importConfig"
                :disabled="!selectedFile || importing"
                class="action-btn import-btn"
              >
                <span v-if="importing">📥 导入中...</span>
                <span v-else>📥 导入配置</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'

const emit = defineEmits(['close', 'load-config', 'save-config', 'delete-config'])

const dashboardStore = useDashboardStore()
const configs = ref([])
const loading = ref(false)
const saving = ref(false)
const newConfigName = ref('')
const setAsDefaultConfig = ref(false)

// 导入导出相关状态
const exporting = ref(false)
const importing = ref(false)
const isDragOver = ref(false)
const selectedFile = ref(null)
const importPreview = ref(null)
const exportOptions = ref({
  includeLayout: true,
  includeWidgets: true,
  includeFocusedMetrics: true
})

// 方法
const loadConfigs = async () => {
  loading.value = true
  try {
    configs.value = await dashboardStore.getUserConfigs()
  } catch (error) {
    console.error('加载配置失败:', error)
  } finally {
    loading.value = false
  }
}

const loadConfig = (configId) => {
  emit('load-config', configId)
}

const saveNewConfig = async () => {
  if (!newConfigName.value.trim()) return
  
  saving.value = true
  try {
    await emit('save-config', {
      name: newConfigName.value.trim(),
      isDefault: setAsDefaultConfig.value
    })
    newConfigName.value = ''
    setAsDefaultConfig.value = false
    await loadConfigs()
  } catch (error) {
    console.error('保存配置失败:', error)
  } finally {
    saving.value = false
  }
}

const setAsDefault = async (configId) => {
  try {
    await dashboardStore.setDefaultConfig(configId)
    await loadConfigs()
  } catch (error) {
    console.error('设置默认配置失败:', error)
  }
}

const deleteConfig = async (configId) => {
  if (!confirm('确定要删除这个配置吗？')) return
  
  try {
    await emit('delete-config', configId)
    await loadConfigs()
  } catch (error) {
    console.error('删除配置失败:', error)
  }
}

const exportConfig = async () => {
  exporting.value = true
  try {
    const currentConfig = {
      name: `配置导出_${new Date().toLocaleDateString()}`,
      version: '1.0.0',
      exportTime: new Date().toISOString(),
      metadata: {
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      }
    }

    // 根据选项包含不同的配置
    if (exportOptions.value.includeLayout) {
      currentConfig.layout = dashboardStore.userLayout
    }

    if (exportOptions.value.includeWidgets) {
      currentConfig.widgets = dashboardStore.widgets
    }

    if (exportOptions.value.includeFocusedMetrics) {
      currentConfig.focusedMetricIds = dashboardStore.focusedMetricIds
    }

    const blob = new Blob([JSON.stringify(currentConfig, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `dashboard-config-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    // 显示成功消息
    console.log('配置导出成功')
  } catch (error) {
    console.error('导出配置失败:', error)
    alert('导出配置失败，请重试')
  } finally {
    exporting.value = false
  }
}

// 新的导入相关方法
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processSelectedFile(file)
  }
}

const handleFileDrop = (event) => {
  isDragOver.value = false
  const files = event.dataTransfer.files
  if (files.length > 0) {
    processSelectedFile(files[0])
  }
}

const processSelectedFile = (file) => {
  if (!file.name.endsWith('.json')) {
    alert('请选择JSON格式的配置文件')
    return
  }

  selectedFile.value = file

  // 读取文件内容进行预览
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)
      importPreview.value = config
    } catch (error) {
      console.error('文件解析失败:', error)
      alert('配置文件格式不正确')
      clearSelectedFile()
    }
  }
  reader.readAsText(file)
}

const clearSelectedFile = () => {
  selectedFile.value = null
  importPreview.value = null
  // 清空文件输入
  const fileInputElement = document.querySelector('input[type="file"]')
  if (fileInputElement) {
    fileInputElement.value = ''
  }
}

const importConfig = async () => {
  if (!selectedFile.value || !importPreview.value) return

  importing.value = true
  try {
    // 验证配置文件格式
    const config = importPreview.value
    if (!config.layout && !config.widgets && !config.focusedMetricIds) {
      alert('配置文件不包含有效的布局数据')
      return
    }

    // 确认导入
    const confirmed = confirm('导入配置将覆盖当前设置，确定要继续吗？')
    if (!confirmed) return

    // 执行导入
    dashboardStore.importConfig(config)

    alert('配置导入成功！')
    clearSelectedFile()
    emit('close')
  } catch (error) {
    console.error('导入配置失败:', error)
    alert('导入配置失败，请检查文件格式')
  } finally {
    importing.value = false
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadConfigs()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 24px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.config-section {
  margin-bottom: 32px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.loading-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 20px;
  color: #6b7280;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.config-list {
  space-y: 8px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: border-color 0.2s;
}

.config-item:hover {
  border-color: #d1d5db;
}

.config-item.is-default {
  border-color: #fbbf24;
  background: #fffbeb;
}

.config-info {
  flex: 1;
}

.config-name {
  font-weight: 500;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-badge {
  background: #fbbf24;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.config-meta {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.config-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: none;
  border: 1px solid #e5e7eb;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.action-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.delete-btn:hover {
  border-color: #fca5a5;
  background: #fef2f2;
}

.save-config-form {
  space-y: 16px;
}

.form-group {
  space-y: 8px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
}

.form-checkbox {
  width: 16px;
  height: 16px;
}

.save-btn {
  width: 100%;
  padding: 12px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-btn:hover:not(:disabled) {
  background: #2563eb;
}

.save-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 导入导出样式 */
.export-section, .import-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
}

.subsection-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.section-description {
  font-size: 13px;
  color: #6b7280;
  margin-bottom: 16px;
  line-height: 1.4;
}

.export-options {
  margin-bottom: 16px;
}

.checkbox-group {
  margin-bottom: 8px;
}

.file-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 32px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  margin-bottom: 16px;
}

.file-drop-zone:hover {
  border-color: #3b82f6;
  background: #f8faff;
}

.file-drop-zone.drag-over {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.drop-zone-content {
  pointer-events: none;
}

.drop-zone-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.drop-zone-text {
  font-size: 14px;
  color: #374151;
  margin: 0 0 4px 0;
}

.drop-zone-hint {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.selected-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  margin-bottom: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 16px;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-name {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #6b7280;
}

.clear-file-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.clear-file-btn:hover {
  background: #f3f4f6;
}

.import-preview {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.preview-label {
  color: #6b7280;
  font-weight: 500;
}

.preview-value {
  color: #374151;
}

.file-input {
  display: none;
}

.export-btn, .import-btn {
  width: 100%;
  padding: 12px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.export-btn:hover:not(:disabled), .import-btn:hover:not(:disabled) {
  background: #2563eb;
}

.export-btn:disabled, .import-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}
</style>
