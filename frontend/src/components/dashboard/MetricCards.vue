<template>
  <div class="metric-cards">
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <span>加载指标数据中...</span>
    </div>
    
    <div v-else class="cards-grid">
      <div
        v-for="(card, index) in filteredCards"
        :key="index"
        class="metric-card"
        :class="[
          `card-${card.change_type}`,
          { 'card-clickable': card.clickable }
        ]"
        @click="handleCardClick(card)"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="card-title">{{ card.title }}</div>
          <div v-if="config.showTrend && card.trend" class="card-trend">
            <span :class="`trend-${card.change_type}`">
              {{ getTrendIcon(card.change_type) }}
            </span>
          </div>
        </div>

        <!-- 主要数值 -->
        <div class="card-value">
          <span class="value-number">{{ card.value }}</span>
          <span v-if="card.unit" class="value-unit">{{ card.unit }}</span>
        </div>

        <!-- 进度条 -->
        <div v-if="card.progress !== undefined" class="card-progress">
          <div class="progress-bar">
            <div 
              class="progress-fill"
              :style="{ 
                width: `${Math.min(card.progress, 100)}%`,
                backgroundColor: getProgressColor(card.progress, card.change_type)
              }"
            ></div>
          </div>
          <span class="progress-text">{{ card.progress }}%</span>
        </div>

        <!-- 目标和变化 -->
        <div class="card-footer">
          <div v-if="config.showTarget && card.target" class="card-target">
            {{ card.target }}
          </div>
          <div v-if="card.change" class="card-change" :class="`change-${card.change_type}`">
            {{ card.change }}
          </div>
        </div>

        <!-- 详细信息按钮 -->
        <div v-if="card.hasDetails" class="card-actions">
          <button 
            @click.stop="showDetails(card)"
            class="details-btn"
            title="查看详情"
          >
            📊
          </button>
        </div>
      </div>
    </div>

    <!-- 个人关注指标设置 -->
    <div v-if="showSettings" class="settings-panel">
      <div class="settings-header">
        <h4>个人关注指标设置</h4>
        <button @click="showSettings = false" class="close-btn">×</button>
      </div>
      <div class="settings-content">
        <div class="metric-types">
          <label
            v-for="type in availableMetricTypes"
            :key="type.value"
            class="metric-type-option"
          >
            <input
              v-model="selectedMetricTypes"
              :value="type.value"
              type="checkbox"
              class="metric-checkbox"
            />
            <span class="metric-label">{{ type.label }}</span>
            <span class="metric-desc">{{ type.description }}</span>
          </label>
        </div>
        <div class="settings-actions">
          <button @click="saveSettings" class="save-btn">保存设置</button>
          <button @click="resetSettings" class="reset-btn">重置</button>
        </div>
      </div>
    </div>

    <!-- 快速访问面板 -->
    <div v-if="config.showQuickAccess" class="quick-access-panel">
      <h4 class="panel-title">快速访问</h4>
      <div class="quick-actions">
        <button
          v-for="action in quickActions"
          :key="action.id"
          @click="handleQuickAction(action)"
          class="quick-action-btn"
          :title="action.description"
        >
          <span class="action-icon">{{ action.icon }}</span>
          <span class="action-label">{{ action.label }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'

const props = defineProps({
  config: {
    type: Object,
    default: () => ({
      showTrend: true,
      showTarget: true,
      showQuickAccess: false,
      metricTypes: ['coverage', 'performance', 'quality_gate', 'efficiency']
    })
  },
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['card-click', 'settings-change'])

const dashboardStore = useDashboardStore()
const showSettings = ref(false)
const selectedMetricTypes = ref([...props.config.metricTypes])

// 可用的指标类型
const availableMetricTypes = [
  {
    value: 'coverage',
    label: '测试覆盖率',
    description: '代码覆盖率、用例覆盖率等指标'
  },
  {
    value: 'performance',
    label: '性能指标',
    description: '响应时间、吞吐量、资源使用率'
  },
  {
    value: 'quality_gate',
    label: '质量门禁',
    description: '质量门禁通过率、规则检查'
  },
  {
    value: 'efficiency',
    label: '效率指标',
    description: '自动化效能、开发效率提升'
  },
  {
    value: 'defects',
    label: '缺陷指标',
    description: '缺陷密度、修复时间、趋势分析'
  }
]

// 快速访问操作
const quickActions = [
  {
    id: 'refresh',
    icon: '🔄',
    label: '刷新数据',
    description: '刷新所有指标数据'
  },
  {
    id: 'export',
    icon: '📊',
    label: '导出报告',
    description: '导出当前指标报告'
  },
  {
    id: 'settings',
    icon: '⚙️',
    label: '个性化设置',
    description: '配置个人关注指标'
  },
  {
    id: 'alerts',
    icon: '🔔',
    label: '预警设置',
    description: '配置指标预警阈值'
  }
]

// 计算属性
const filteredCards = computed(() => {
  if (!props.data || props.data.length === 0) {
    return getDefaultCards()
  }
  
  return props.data.filter(card => 
    selectedMetricTypes.value.includes(card.type) || !card.type
  )
})

// 方法
const getTrendIcon = (changeType) => {
  switch (changeType) {
    case 'positive': return '📈'
    case 'negative': return '📉'
    default: return '➡️'
  }
}

const getProgressColor = (progress, changeType) => {
  if (changeType === 'negative') return '#ef4444'
  if (progress >= 80) return '#10b981'
  if (progress >= 60) return '#f59e0b'
  return '#ef4444'
}

const handleCardClick = (card) => {
  if (card.clickable) {
    emit('card-click', card)
  }
}

const showDetails = (card) => {
  // 显示卡片详细信息
  console.log('显示详情:', card)
}

const handleQuickAction = (action) => {
  switch (action.id) {
    case 'refresh':
      dashboardStore.refreshAll()
      break
    case 'export':
      exportReport()
      break
    case 'settings':
      showSettings.value = true
      break
    case 'alerts':
      // 打开预警设置
      break
  }
}

const saveSettings = () => {
  emit('settings-change', {
    metricTypes: [...selectedMetricTypes.value]
  })
  showSettings.value = false
}

const resetSettings = () => {
  selectedMetricTypes.value = ['coverage', 'performance', 'quality_gate', 'efficiency']
}

const exportReport = () => {
  const reportData = {
    title: '质量指标报告',
    date: new Date().toLocaleDateString(),
    metrics: filteredCards.value
  }
  
  const blob = new Blob([JSON.stringify(reportData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `quality-metrics-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const getDefaultCards = () => {
  return [
    {
      title: '接口自动化覆盖率',
      value: '78',
      unit: '%',
      progress: 78,
      target: '目标: 80%',
      change: '+5% 较上月',
      change_type: 'positive',
      type: 'coverage',
      clickable: true,
      hasDetails: true
    },
    {
      title: '质量门禁通过率',
      value: '92',
      unit: '%',
      progress: 92,
      target: '目标: 95%',
      change: '+4% 较上月',
      change_type: 'positive',
      type: 'quality_gate',
      clickable: true,
      hasDetails: true
    },
    {
      title: '平均响应时间',
      value: '125',
      unit: 'ms',
      progress: 75,
      target: '基线: 150ms',
      change: '-15ms 较上月',
      change_type: 'positive',
      type: 'performance',
      clickable: true,
      hasDetails: true
    },
    {
      title: '自动化效能提升',
      value: '42',
      unit: '%',
      progress: 42,
      target: '目标: 50%',
      change: '+8% 较上季度',
      change_type: 'positive',
      type: 'efficiency',
      clickable: true,
      hasDetails: true
    }
  ]
}

// 监听配置变化
watch(() => props.config.metricTypes, (newTypes) => {
  if (newTypes && Array.isArray(newTypes)) {
    selectedMetricTypes.value = [...newTypes]
  }
}, { immediate: true })
</script>

<style scoped>
.metric-cards {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 12px;
  color: #6b7280;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  flex: 1;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-clickable {
  cursor: pointer;
}

.card-positive {
  border-left: 4px solid #10b981;
}

.card-negative {
  border-left: 4px solid #ef4444;
}

.card-neutral {
  border-left: 4px solid #6b7280;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  line-height: 1.4;
}

.card-trend {
  font-size: 18px;
}

.card-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 16px;
}

.value-number {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.value-unit {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
}

.card-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 35px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.card-target {
  color: #6b7280;
}

.card-change {
  font-weight: 500;
}

.change-positive {
  color: #10b981;
}

.change-negative {
  color: #ef4444;
}

.change-neutral {
  color: #6b7280;
}

.card-actions {
  position: absolute;
  top: 16px;
  right: 16px;
}

.details-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.details-btn:hover {
  background: #f3f4f6;
}

.settings-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  z-index: 1000;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.settings-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.settings-content {
  padding: 24px;
}

.metric-types {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.metric-type-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.metric-type-option:hover {
  background: #f9fafb;
}

.metric-checkbox {
  margin-top: 2px;
}

.metric-label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.metric-desc {
  font-size: 14px;
  color: #6b7280;
}

.settings-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.save-btn, .reset-btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.save-btn {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.save-btn:hover {
  background: #2563eb;
}

.reset-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.reset-btn:hover {
  background: #e5e7eb;
}

.quick-access-panel {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
}

.quick-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-action-btn:hover {
  border-color: #d1d5db;
  background: #f3f4f6;
}

.action-icon {
  font-size: 14px;
}

.action-label {
  font-weight: 500;
  color: #374151;
}
</style>
