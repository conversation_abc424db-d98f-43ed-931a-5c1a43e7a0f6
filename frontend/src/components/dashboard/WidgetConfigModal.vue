<![CDATA[<template>
  <div class="modal-overlay" @click="$emit('close')">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">组件配置</h3>
        <button @click="$emit('close')" class="close-btn">×</button>
      </div>

      <div class="modal-body">
        <div v-if="widgetType" class="config-form">
          <!-- 通用配置 -->
          <div class="config-section">
            <h4 class="section-title">基础设置</h4>
            <div class="form-group">
              <label class="form-label">组件标题</label>
              <input
                v-model="localConfig.title"
                type="text"
                class="form-input"
                placeholder="输入组件标题"
              />
            </div>
          </div>

          <!-- 指标卡片配置 -->
          <div v-if="widgetType === 'metric-cards'" class="config-section">
            <h4 class="section-title">指标卡片设置</h4>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="localConfig.showTrend"
                  type="checkbox"
                  class="form-checkbox"
                />
                显示趋势变化
              </label>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="localConfig.showTarget"
                  type="checkbox"
                  class="form-checkbox"
                />
                显示目标值
              </label>
            </div>
            <div class="form-group">
              <label class="form-label">显示指标类型</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.metricTypes"
                    value="coverage"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  测试覆盖率
                </label>
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.metricTypes"
                    value="performance"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  性能指标
                </label>
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.metricTypes"
                    value="quality_gate"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  质量门禁
                </label>
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.metricTypes"
                    value="efficiency"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  效率指标
                </label>
              </div>
            </div>
          </div>

          <!-- 趋势图表配置 -->
          <div v-if="widgetType.includes('trend-chart')" class="config-section">
            <h4 class="section-title">趋势图表设置</h4>
            <div class="form-group">
              <label class="form-label">时间范围</label>
              <select v-model="localConfig.dateRange" class="form-select">
                <option value="7d">最近7天</option>
                <option value="30d">最近30天</option>
                <option value="90d">最近90天</option>
                <option value="1y">最近1年</option>
                <option value="custom">自定义范围</option>
              </select>
            </div>
            <div v-if="localConfig.dateRange === 'custom'" class="form-group grid grid-cols-2 gap-4">
              <div>
                <label class="form-label">开始日期</label>
                <input
                  v-model="localConfig.customDateStart"
                  type="date"
                  class="form-input"
                  placeholder="YYYY-MM-DD"
                />
              </div>
              <div>
                <label class="form-label">结束日期</label>
                <input
                  v-model="localConfig.customDateEnd"
                  type="date"
                  class="form-input"
                  placeholder="YYYY-MM-DD"
                />
              </div>
            </div>
            <div class="form-group">
              <label class="form-label">分组方式</label>
              <select v-model="localConfig.groupBy" class="form-select">
                <option value="day">按天</option>
                <option value="week">按周</option>
                <option value="month">按月</option>
              </select>
            </div>
            <div v-if="widgetType === 'coverage-trend-chart'" class="form-group">
              <label class="form-label">覆盖率类型</label>
              <select v-model="localConfig.metricType" class="form-select">
                <option value="line">行覆盖率</option>
                <option value="branch">分支覆盖率</option>
                <option value="function">函数覆盖率</option>
              </select>
            </div>
            <!-- 缺陷趋势图 - 数据系列显示/隐藏 -->
            <div v-if="widgetType === 'defect-trend-chart'" class="form-group">
              <label class="form-label">显示数据系列 (缺陷等级)</label>
              <div class="checkbox-group">
                <label v-for="level in ['致命', '严重', '一般', '提示']" :key="level" class="checkbox-label">
                  <input
                    v-model="localConfig.visibleDefectSeries"
                    :value="level"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  {{ level }}
                </label>
              </div>
            </div>
          </div>

          <!-- 团队对比图配置 -->
          <div v-if="widgetType === 'team-comparison-chart'" class="config-section">
            <h4 class="section-title">团队对比设置</h4>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="localConfig.showDetails"
                  type="checkbox"
                  class="form-checkbox"
                />
                显示详细信息
              </label>
            </div>
            <div class="form-group">
              <label class="form-label">对比指标</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.compareMetrics"
                    value="coverage"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  覆盖率
                </label>
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.compareMetrics"
                    value="defects"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  缺陷数量
                </label>
                <label class="checkbox-label">
                  <input
                    v-model="localConfig.compareMetrics"
                    value="performance"
                    type="checkbox"
                    class="form-checkbox"
                  />
                  性能指标
                </label>
              </div>
            </div>
          </div>

          <!-- 热力图配置 -->
          <div v-if="widgetType === 'coverage-heatmap-chart'" class="config-section">
            <h4 class="section-title">热力图设置</h4>
            <div class="form-group">
              <label class="form-label">显示模式</label>
              <select v-model="localConfig.displayMode" class="form-select">
                <option value="coverage">覆盖率</option>
                <option value="files">文件数量</option>
                <option value="changes">变更频率</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">网格大小</label>
              <select v-model="localConfig.gridSize" class="form-select">
                <option value="small">小</option>
                <option value="medium">中</option>
                <option value="large">大</option>
              </select>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="localConfig.showLabels"
                  type="checkbox"
                  class="form-checkbox"
                />
                显示标签
              </label>
            </div>
          </div>

          <!-- 样式配置 -->
          <div class="config-section">
            <h4 class="section-title">样式设置</h4>
            <div class="form-group">
              <label class="form-label">主题色彩</label>
              <div class="color-options">
                <div
                  v-for="color in colorOptions"
                  :key="color.name"
                  @click="localConfig.primaryColor = color.value"
                  :class="[
                    'color-option',
                    { 'selected': localConfig.primaryColor === color.value }
                  ]"
                  :style="{ backgroundColor: color.value }"
                  :title="color.name"
                ></div>
              </div>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="localConfig.showBorder"
                  type="checkbox"
                  class="form-checkbox"
                />
                显示边框
              </label>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  v-model="localConfig.showShadow"
                  type="checkbox"
                  class="form-checkbox"
                />
                显示阴影
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="cancel-btn">
          取消
        </button>
        <button @click="saveConfig" class="save-btn">
          保存配置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  widgetId: {
    type: String,
    required: true
  },
  config: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'save'])

// 根据组件ID推断组件类型
const widgetType = computed(() => {
  if (props.widgetId.includes('metric')) return 'metric-cards'
  if (props.widgetId.includes('defect-trend')) return 'defect-trend-chart'
  if (props.widgetId.includes('coverage-trend')) return 'coverage-trend-chart'
  if (props.widgetId.includes('team-comparison')) return 'team-comparison-chart'
  if (props.widgetId.includes('coverage-heatmap')) return 'coverage-heatmap-chart'
  return 'unknown'
})

// 本地配置状态
const localConfig = ref({
  title: '',
  showTrend: true,
  showTarget: true,
  showDetails: true,
  showBorder: true,
  showShadow: true,
  showLabels: true,
  dateRange: '30d',
  customDateStart: '',
  customDateEnd: '',
  groupBy: 'day',
  metricType: 'line',
  displayMode: 'coverage',
  gridSize: 'medium',
  primaryColor: '#3b82f6',
  metricTypes: ['coverage', 'performance'],
  compareMetrics: ['coverage', 'defects'],
  visibleDefectSeries: ['致命', '严重', '一般', '提示'], // 默认全选
  ...props.config
})

// 颜色选项
const colorOptions = [
  { name: '蓝色', value: '#3b82f6' },
  { name: '绿色', value: '#10b981' },
  { name: '紫色', value: '#8b5cf6' },
  { name: '红色', value: '#ef4444' },
  { name: '黄色', value: '#f59e0b' },
  { name: '青色', value: '#06b6d4' },
  { name: '粉色', value: '#ec4899' },
  { name: '灰色', value: '#6b7280' }
]

// 方法
const saveConfig = () => {
  emit('save', { ...localConfig.value })
}

// 监听props变化
watch(() => props.config, (newConfig) => {
  localConfig.value = {
    ...localConfig.value, // 保留现有的默认值或已修改的值
    ...newConfig // 应用传入的配置
  }
  // 确保新添加的配置项如果在新配置中不存在，则使用默认值
  if (newConfig.customDateStart === undefined) {
    localConfig.value.customDateStart = '';
  }
  if (newConfig.customDateEnd === undefined) {
    localConfig.value.customDateEnd = '';
  }
  if (newConfig.visibleDefectSeries === undefined) {
    localConfig.value.visibleDefectSeries = ['致命', '严重', '一般', '提示'];
  }

}, { immediate: true, deep: true }) // 使用 deep: true 来监听嵌套对象的更改
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
}

.modal-body {
  padding: 24px;
  max-height: calc(80vh - 140px); /* modal-header 和 modal-footer 的高度 */
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-input, .form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus, .form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  margin-bottom: 8px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-checkbox {
  width: 16px;
  height: 16px;
}

.color-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.color-option {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: #374151; /* 更明显的选中边框 */
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); /* 选中时的外发光效果 */
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
}

.cancel-btn, .save-btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.save-btn {
  background: #3b82f6;
  color: white;
  border: 1px solid #3b82f6;
}

.save-btn:hover {
  background: #2563eb;
}
</style>]]>
