<template>
  <div class="draggable-layout">
    <!-- 工具栏 -->
    <div class="layout-toolbar mb-4 flex justify-between items-center">
      <div class="flex items-center space-x-4">
        <button
          @click="toggleEditMode"
          :class="[
            'px-4 py-2 rounded-lg font-medium transition-colors',
            editMode 
              ? 'bg-green-600 hover:bg-green-700 text-white' 
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          ]"
        >
          {{ editMode ? '保存布局' : '编辑布局' }}
        </button>
        
        <button
          v-if="editMode"
          @click="resetLayout"
          class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
        >
          重置布局
        </button>
        
        <button
          v-if="!editMode"
          @click="showConfigModal = true"
          class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors"
        >
          配置管理
        </button>
      </div>
      
      <div v-if="editMode" class="text-sm text-gray-600">
        拖拽和调整组件大小来自定义布局
      </div>
    </div>

    <!-- 网格布局 -->
    <grid-layout
      v-model:layout="layout"
      :col-num="12"
      :row-height="60"
      :is-draggable="editMode"
      :is-resizable="editMode"
      :margin="[10, 10]"
      :use-css-transforms="true"
      @layout-updated="onLayoutUpdated"
    >
      <grid-item
        v-for="item in layout"
        :key="item.i"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
        class="grid-item"
      >
        <div class="widget-container h-full">
          <!-- 编辑模式下的组件头部 -->
          <div v-if="editMode" class="widget-header">
            <span class="widget-title">{{ getWidgetTitle(item.i) }}</span>
            <div class="widget-actions">
              <button
                @click="configureWidget(item.i)"
                class="config-btn"
                title="配置组件"
              >
                ⚙️
              </button>
              <button
                @click="removeWidget(item.i)"
                class="remove-btn"
                title="删除组件"
              >
                ×
              </button>
            </div>
          </div>

          <!-- 组件内容 -->
          <div class="widget-content" :class="{ 'with-header': editMode }">
            <component
              :is="getWidgetComponent(item.i)"
              :config="getWidgetConfig(item.i)"
              :data="getWidgetData(item.i)"
              :loading="loading[item.i] || false"
            />
          </div>
        </div>
      </grid-item>
    </grid-layout>

    <!-- 添加组件面板 -->
    <div v-if="editMode" class="add-widget-panel">
      <h4 class="text-lg font-semibold mb-4">添加组件</h4>
      <div class="widget-list space-y-2">
        <div
          v-for="widget in availableWidgets"
          :key="widget.type"
          @click="addWidget(widget)"
          class="widget-option"
        >
          <div class="widget-icon">{{ widget.icon }}</div>
          <div class="widget-info">
            <div class="widget-name">{{ widget.title }}</div>
            <div class="widget-desc">{{ widget.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置管理弹窗 -->
    <ConfigModal
      v-if="showConfigModal"
      @close="showConfigModal = false"
      @load-config="loadConfig"
      @save-config="saveConfig"
      @delete-config="deleteConfig"
    />

    <!-- 组件配置弹窗 -->
    <WidgetConfigModal
      v-if="showWidgetConfigModal"
      :widget-id="currentConfigWidgetId"
      :config="getCurrentWidgetConfig()"
      @close="showWidgetConfigModal = false"
      @save="saveWidgetConfig"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { GridLayout, GridItem } from 'vue3-grid-layout'
import { useDashboardStore } from '@/stores/dashboard'
import ConfigModal from './ConfigModal.vue'
import WidgetConfigModal from './WidgetConfigModal.vue'

// 导入所有可用的组件
import MetricCards from '@/components/dashboard/MetricCards.vue'
import DefectTrendChart from '@/components/defect/DefectTrendChart.vue'
import CoverageTrendChart from '@/components/coverage/CoverageTrendChart.vue'
import TeamComparisonChart from '@/components/dashboard/TeamComparisonChart.vue'
import CoverageHeatmapChart from '@/components/coverage/CoverageHeatmapChart.vue'

const props = defineProps({
  userId: {
    type: Number,
    default: 1
  }
})

const dashboardStore = useDashboardStore()
const editMode = ref(false)
const layout = ref([])
const loading = ref({})
const showConfigModal = ref(false)
const showWidgetConfigModal = ref(false)
const currentConfigWidgetId = ref(null)

// 可用组件定义
const availableWidgets = [
  { 
    type: 'metric-cards', 
    title: '指标卡片', 
    icon: '📊', 
    description: '显示关键质量指标',
    component: MetricCards,
    defaultSize: { w: 12, h: 4 }
  },
  { 
    type: 'defect-trend-chart', 
    title: '缺陷趋势图', 
    icon: '🐛', 
    description: '显示缺陷趋势变化',
    component: DefectTrendChart,
    defaultSize: { w: 6, h: 6 }
  },
  { 
    type: 'coverage-trend-chart', 
    title: '覆盖率趋势图', 
    icon: '📈', 
    description: '显示测试覆盖率趋势',
    component: CoverageTrendChart,
    defaultSize: { w: 6, h: 6 }
  },
  { 
    type: 'team-comparison-chart', 
    title: '团队对比图', 
    icon: '👥', 
    description: '团队质量指标对比',
    component: TeamComparisonChart,
    defaultSize: { w: 12, h: 6 }
  },
  { 
    type: 'coverage-heatmap-chart', 
    title: '覆盖率热力图', 
    icon: '🔥', 
    description: '文件覆盖率热力图',
    component: CoverageHeatmapChart,
    defaultSize: { w: 12, h: 8 }
  }
]

// 组件映射
const widgetComponents = {
  'metric-cards': MetricCards,
  'defect-trend-chart': DefectTrendChart,
  'coverage-trend-chart': CoverageTrendChart,
  'team-comparison-chart': TeamComparisonChart,
  'coverage-heatmap-chart': CoverageHeatmapChart
}

// 计算属性
const getWidgetComponent = (widgetId) => {
  const widget = dashboardStore.widgets.find(w => w.id === widgetId)
  return widgetComponents[widget?.type] || 'div'
}

const getWidgetTitle = (widgetId) => {
  const widget = dashboardStore.widgets.find(w => w.id === widgetId)
  return widget?.title || '未知组件'
}

const getWidgetConfig = (widgetId) => {
  const widget = dashboardStore.widgets.find(w => w.id === widgetId)
  return widget?.config || {}
}

const getWidgetData = (widgetId) => {
  const widget = dashboardStore.widgets.find(w => w.id === widgetId)
  return widget?.data || null
}

const getCurrentWidgetConfig = () => {
  if (!currentConfigWidgetId.value) return {}
  const widget = dashboardStore.widgets.find(w => w.id === currentConfigWidgetId.value)
  return widget?.config || {}
}

// 方法
const toggleEditMode = async () => {
  if (editMode.value) {
    // 保存布局
    await saveCurrentLayout()
  }
  editMode.value = !editMode.value
}

const onLayoutUpdated = (newLayout) => {
  layout.value = newLayout
}

const addWidget = (widgetType) => {
  const newWidgetId = `widget-${Date.now()}`
  const defaultSize = widgetType.defaultSize || { w: 4, h: 4 }
  
  // 找到合适的位置
  const position = findAvailablePosition(defaultSize.w, defaultSize.h)
  
  const newLayoutItem = {
    i: newWidgetId,
    x: position.x,
    y: position.y,
    w: defaultSize.w,
    h: defaultSize.h
  }

  layout.value.push(newLayoutItem)
  
  // 添加到store
  dashboardStore.addWidget({
    id: newWidgetId,
    type: widgetType.type,
    title: widgetType.title,
    config: {},
    data: null
  })
}

const removeWidget = (widgetId) => {
  layout.value = layout.value.filter(item => item.i !== widgetId)
  dashboardStore.removeWidget(widgetId)
}

const configureWidget = (widgetId) => {
  currentConfigWidgetId.value = widgetId
  showWidgetConfigModal.value = true
}

const saveWidgetConfig = (config) => {
  if (currentConfigWidgetId.value) {
    dashboardStore.updateWidgetConfig(currentConfigWidgetId.value, config)
  }
  showWidgetConfigModal.value = false
  currentConfigWidgetId.value = null
}

const resetLayout = async () => {
  await dashboardStore.resetLayout()
  layout.value = dashboardStore.defaultLayout
}

const saveCurrentLayout = async () => {
  try {
    await dashboardStore.saveLayout(layout.value)
  } catch (error) {
    console.error('保存布局失败:', error)
  }
}

const loadConfig = async (configId) => {
  try {
    await dashboardStore.loadConfig(configId)
    layout.value = dashboardStore.userLayout || dashboardStore.defaultLayout
    showConfigModal.value = false
  } catch (error) {
    console.error('加载配置失败:', error)
  }
}

const saveConfig = async (configName) => {
  try {
    await dashboardStore.saveConfig(configName, layout.value)
    showConfigModal.value = false
  } catch (error) {
    console.error('保存配置失败:', error)
  }
}

const deleteConfig = async (configId) => {
  try {
    await dashboardStore.deleteConfig(configId)
  } catch (error) {
    console.error('删除配置失败:', error)
  }
}

const findAvailablePosition = (width, height) => {
  // 简单的位置查找算法
  const maxCols = 12
  let y = 0
  
  while (true) {
    for (let x = 0; x <= maxCols - width; x++) {
      if (isPositionAvailable(x, y, width, height)) {
        return { x, y }
      }
    }
    y++
  }
}

const isPositionAvailable = (x, y, width, height) => {
  return !layout.value.some(item => {
    return !(
      x >= item.x + item.w ||
      x + width <= item.x ||
      y >= item.y + item.h ||
      y + height <= item.y
    )
  })
}

// 生命周期
onMounted(async () => {
  await dashboardStore.loadUserLayout(props.userId)
  layout.value = dashboardStore.userLayout || dashboardStore.defaultLayout
})
</script>

<style scoped>
.draggable-layout {
  padding: 20px;
}

.grid-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}

.grid-item:hover {
  border-color: #e5e7eb;
}

.widget-container {
  display: flex;
  flex-direction: column;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.widget-title {
  font-weight: 500;
  color: #374151;
}

.widget-actions {
  display: flex;
  gap: 4px;
}

.config-btn, .remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  transition: background-color 0.2s;
}

.config-btn:hover {
  background: #e5e7eb;
}

.remove-btn {
  color: #dc3545;
  font-weight: bold;
}

.remove-btn:hover {
  background: #fee2e2;
}

.widget-content {
  flex: 1;
  overflow: hidden;
}

.widget-content.with-header {
  height: calc(100% - 40px);
}

.add-widget-panel {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  padding: 16px;
  width: 250px;
  max-height: 70vh;
  overflow-y: auto;
  z-index: 1000;
}

.widget-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid #e5e7eb;
}

.widget-option:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.widget-icon {
  font-size: 20px;
  width: 32px;
  text-align: center;
}

.widget-info {
  flex: 1;
}

.widget-name {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.widget-desc {
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}
</style>
