<template>
  <div class="quick-access-panel">
    <div class="panel-header">
      <h3 class="panel-title">快速访问</h3>
      <div class="panel-controls">
        <button @click="showCustomize = true" class="customize-btn" title="自定义面板">
          ⚙️
        </button>
        <button @click="refreshPanel" class="refresh-btn" title="刷新面板">
          🔄
        </button>
      </div>
    </div>

    <div class="panel-content">
      <!-- 常用操作 -->
      <div class="action-section">
        <h4 class="section-title">常用操作</h4>
        <div class="action-grid">
          <button
            v-for="action in visibleActions"
            :key="action.id"
            @click="handleAction(action)"
            class="action-item"
            :class="{ 'action-loading': action.loading }"
            :disabled="action.loading"
          >
            <div class="action-icon">{{ action.icon }}</div>
            <div class="action-label">{{ action.label }}</div>
            <div v-if="action.badge" class="action-badge">{{ action.badge }}</div>
          </button>
        </div>
      </div>

      <!-- 快速导航 -->
      <div class="navigation-section">
        <h4 class="section-title">快速导航</h4>
        <div class="nav-list">
          <router-link
            v-for="nav in visibleNavigation"
            :key="nav.id"
            :to="nav.path"
            class="nav-item"
            :class="{ 'nav-active': $route.path === nav.path }"
          >
            <span class="nav-icon">{{ nav.icon }}</span>
            <span class="nav-label">{{ nav.label }}</span>
            <span v-if="nav.count" class="nav-count">{{ nav.count }}</span>
          </router-link>
        </div>
      </div>

      <!-- 最近访问 -->
      <div class="recent-section">
        <h4 class="section-title">最近访问</h4>
        <div class="recent-list">
          <div
            v-for="item in recentItems"
            :key="item.id"
            @click="goToItem(item)"
            class="recent-item"
          >
            <div class="recent-icon">{{ item.icon }}</div>
            <div class="recent-info">
              <div class="recent-title">{{ item.title }}</div>
              <div class="recent-time">{{ formatTime(item.lastAccess) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷统计 -->
      <div class="stats-section">
        <h4 class="section-title">快捷统计</h4>
        <div class="stats-grid">
          <div
            v-for="stat in quickStats"
            :key="stat.id"
            class="stat-item"
            @click="goToStat(stat)"
          >
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-change" :class="`change-${stat.changeType}`">
              {{ stat.change }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义面板弹窗 -->
    <div v-if="showCustomize" class="customize-modal" @click="showCustomize = false">
      <div class="customize-content" @click.stop>
        <div class="customize-header">
          <h3>自定义快速访问面板</h3>
          <button @click="showCustomize = false" class="close-btn">×</button>
        </div>
        
        <div class="customize-body">
          <div class="customize-section">
            <h4>显示操作</h4>
            <div class="option-list">
              <label
                v-for="action in allActions"
                :key="action.id"
                class="option-item"
              >
                <input
                  v-model="selectedActions"
                  :value="action.id"
                  type="checkbox"
                  class="option-checkbox"
                />
                <span class="option-icon">{{ action.icon }}</span>
                <span class="option-label">{{ action.label }}</span>
              </label>
            </div>
          </div>

          <div class="customize-section">
            <h4>显示导航</h4>
            <div class="option-list">
              <label
                v-for="nav in allNavigation"
                :key="nav.id"
                class="option-item"
              >
                <input
                  v-model="selectedNavigation"
                  :value="nav.id"
                  type="checkbox"
                  class="option-checkbox"
                />
                <span class="option-icon">{{ nav.icon }}</span>
                <span class="option-label">{{ nav.label }}</span>
              </label>
            </div>
          </div>
        </div>

        <div class="customize-footer">
          <button @click="resetCustomization" class="reset-btn">重置</button>
          <button @click="saveCustomization" class="save-btn">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDashboardStore } from '@/stores/dashboard'

const router = useRouter()
const route = useRoute()
const dashboardStore = useDashboardStore()

const showCustomize = ref(false)
const selectedActions = ref(['refresh', 'export', 'settings', 'alerts'])
const selectedNavigation = ref(['dashboard', 'defects', 'coverage', 'performance'])
const recentItems = ref([])

// 所有可用操作
const allActions = [
  { id: 'refresh', icon: '🔄', label: '刷新数据', loading: false },
  { id: 'export', icon: '📊', label: '导出报告', loading: false },
  { id: 'settings', icon: '⚙️', label: '系统设置', loading: false },
  { id: 'alerts', icon: '🔔', label: '预警管理', loading: false, badge: '3' },
  { id: 'backup', icon: '💾', label: '数据备份', loading: false },
  { id: 'import', icon: '📥', label: '数据导入', loading: false },
  { id: 'help', icon: '❓', label: '帮助文档', loading: false },
  { id: 'feedback', icon: '💬', label: '意见反馈', loading: false }
]

// 所有可用导航
const allNavigation = [
  { id: 'dashboard', icon: '📊', label: '质量大盘', path: '/dashboard', count: null },
  { id: 'defects', icon: '🐛', label: '缺陷管理', path: '/defects', count: '12' },
  { id: 'coverage', icon: '📈', label: '覆盖率分析', path: '/coverage', count: null },
  { id: 'performance', icon: '⚡', label: '性能监控', path: '/performance', count: '2' },
  { id: 'automation', icon: '🤖', label: '自动化测试', path: '/automation', count: null },
  { id: 'quality-gate', icon: '🚪', label: '质量门禁', path: '/quality-gate', count: null },
  { id: 'reports', icon: '📋', label: '报告中心', path: '/reports', count: null },
  { id: 'settings', icon: '⚙️', label: '系统设置', path: '/settings', count: null }
]

// 快捷统计数据
const quickStats = ref([
  {
    id: 'today-tests',
    value: '156',
    label: '今日测试',
    change: '+12',
    changeType: 'positive'
  },
  {
    id: 'active-defects',
    value: '8',
    label: '活跃缺陷',
    change: '-3',
    changeType: 'positive'
  },
  {
    id: 'coverage-rate',
    value: '78%',
    label: '覆盖率',
    change: '+2%',
    changeType: 'positive'
  },
  {
    id: 'quality-score',
    value: '92',
    label: '质量评分',
    change: '+5',
    changeType: 'positive'
  }
])

// 计算属性
const visibleActions = computed(() => {
  return allActions.filter(action => selectedActions.value.includes(action.id))
})

const visibleNavigation = computed(() => {
  return allNavigation.filter(nav => selectedNavigation.value.includes(nav.id))
})

// 方法
const handleAction = async (action) => {
  action.loading = true
  
  try {
    switch (action.id) {
      case 'refresh':
        await dashboardStore.refreshAll()
        break
      case 'export':
        exportReport()
        break
      case 'settings':
        router.push('/settings')
        break
      case 'alerts':
        router.push('/alerts')
        break
      case 'backup':
        await performBackup()
        break
      case 'import':
        triggerImport()
        break
      case 'help':
        window.open('/help', '_blank')
        break
      case 'feedback':
        router.push('/feedback')
        break
    }
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    action.loading = false
  }
}

const refreshPanel = () => {
  loadRecentItems()
  loadQuickStats()
}

const goToItem = (item) => {
  router.push(item.path)
  updateRecentAccess(item)
}

const goToStat = (stat) => {
  // 根据统计类型跳转到相应页面
  const routes = {
    'today-tests': '/automation',
    'active-defects': '/defects',
    'coverage-rate': '/coverage',
    'quality-score': '/dashboard'
  }
  
  if (routes[stat.id]) {
    router.push(routes[stat.id])
  }
}

const exportReport = () => {
  const reportData = {
    title: '快速访问面板报告',
    date: new Date().toLocaleDateString(),
    stats: quickStats.value,
    recentItems: recentItems.value
  }
  
  const blob = new Blob([JSON.stringify(reportData, null, 2)], {
    type: 'application/json'
  })
  
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `quick-access-report-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const performBackup = async () => {
  // 模拟备份操作
  await new Promise(resolve => setTimeout(resolve, 2000))
  alert('数据备份完成')
}

const triggerImport = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // 处理文件导入
      console.log('导入文件:', file.name)
    }
  }
  input.click()
}

const saveCustomization = () => {
  // 保存自定义设置到本地存储或服务器
  localStorage.setItem('quickAccessSettings', JSON.stringify({
    selectedActions: selectedActions.value,
    selectedNavigation: selectedNavigation.value
  }))
  showCustomize.value = false
}

const resetCustomization = () => {
  selectedActions.value = ['refresh', 'export', 'settings', 'alerts']
  selectedNavigation.value = ['dashboard', 'defects', 'coverage', 'performance']
}

const loadCustomization = () => {
  const saved = localStorage.getItem('quickAccessSettings')
  if (saved) {
    const settings = JSON.parse(saved)
    selectedActions.value = settings.selectedActions || selectedActions.value
    selectedNavigation.value = settings.selectedNavigation || selectedNavigation.value
  }
}

const loadRecentItems = () => {
  // 从本地存储或服务器加载最近访问项目
  const saved = localStorage.getItem('recentItems')
  if (saved) {
    recentItems.value = JSON.parse(saved).slice(0, 5) // 只显示最近5个
  } else {
    recentItems.value = [
      {
        id: 1,
        title: '前端项目覆盖率分析',
        icon: '📈',
        path: '/coverage/frontend',
        lastAccess: new Date(Date.now() - 1000 * 60 * 30) // 30分钟前
      },
      {
        id: 2,
        title: '缺陷 #1234 修复验证',
        icon: '🐛',
        path: '/defects/1234',
        lastAccess: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2小时前
      },
      {
        id: 3,
        title: '性能测试报告',
        icon: '⚡',
        path: '/performance/report/latest',
        lastAccess: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4小时前
      }
    ]
  }
}

const loadQuickStats = async () => {
  // 从API加载快捷统计数据
  try {
    // 这里应该调用实际的API
    // const stats = await api.getQuickStats()
    // quickStats.value = stats
  } catch (error) {
    console.error('加载快捷统计失败:', error)
  }
}

const updateRecentAccess = (item) => {
  const recent = [...recentItems.value]
  const existingIndex = recent.findIndex(r => r.id === item.id)
  
  if (existingIndex >= 0) {
    recent.splice(existingIndex, 1)
  }
  
  recent.unshift({
    ...item,
    lastAccess: new Date()
  })
  
  recentItems.value = recent.slice(0, 5)
  localStorage.setItem('recentItems', JSON.stringify(recentItems.value))
}

const formatTime = (date) => {
  const now = new Date()
  const diff = now - new Date(date)
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 生命周期
onMounted(() => {
  loadCustomization()
  loadRecentItems()
  loadQuickStats()
})
</script>

<style scoped>
.quick-access-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #212529;
  margin: 0;
}

.panel-controls {
  display: flex;
  gap: 8px;
}

.customize-btn, .refresh-btn {
  background: none;
  border: 1px solid #dee2e6;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.customize-btn:hover, .refresh-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.action-section, .navigation-section, .recent-section, .stats-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.action-item:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.action-item:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-loading {
  pointer-events: none;
}

.action-icon {
  font-size: 20px;
}

.action-label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  text-align: center;
}

.action-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #dc3545;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  text-decoration: none;
  color: #495057;
  transition: all 0.2s;
}

.nav-item:hover {
  background: #f8f9fa;
  color: #212529;
}

.nav-active {
  background: #e7f3ff;
  color: #0066cc;
  font-weight: 500;
}

.nav-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.nav-label {
  flex: 1;
  font-size: 14px;
}

.nav-count {
  background: #6c757d;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recent-item:hover {
  background: #f8f9fa;
}

.recent-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.recent-info {
  flex: 1;
}

.recent-title {
  font-size: 14px;
  font-weight: 500;
  color: #212529;
}

.recent-time {
  font-size: 12px;
  color: #6c757d;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.stat-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #212529;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  margin: 4px 0;
}

.stat-change {
  font-size: 11px;
  font-weight: 500;
}

.change-positive {
  color: #28a745;
}

.change-negative {
  color: #dc3545;
}

.customize-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.customize-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
}

.customize-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #dee2e6;
}

.customize-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #212529;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: #f8f9fa;
}

.customize-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.customize-section {
  margin-bottom: 24px;
}

.customize-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
}

.option-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.option-item:hover {
  background: #f8f9fa;
}

.option-checkbox {
  margin: 0;
}

.option-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.option-label {
  font-size: 14px;
  color: #495057;
}

.customize-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #dee2e6;
}

.reset-btn, .save-btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.reset-btn {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.reset-btn:hover {
  background: #e9ecef;
}

.save-btn {
  background: #007bff;
  color: white;
  border: 1px solid #007bff;
}

.save-btn:hover {
  background: #0056b3;
}
</style>
