import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 导入样式
import './style.css'

// 初始化Chart.js配置
import './utils/chartConfig'

// 初始化性能监控
import { startPerformanceMonitoring } from './utils/performance'

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)

// 启动性能监控（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  const performanceMonitor = startPerformanceMonitoring()

  // 在控制台提供性能报告访问
  window.__PERFORMANCE_MONITOR__ = performanceMonitor

  // 定期输出性能报告
  setInterval(() => {
    const report = performanceMonitor.getPerformanceReport()
    if (report.recommendations.length > 0) {
      console.group('🚀 性能监控报告')
      console.log('📊 性能指标:', report.metrics)
      console.log('💡 优化建议:', report.recommendations)
      console.groupEnd()
    }
  }, 30000) // 每30秒检查一次
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue应用错误:', err)
  console.error('错误信息:', info)

  // 记录错误到性能监控
  if (window.__PERFORMANCE_MONITOR__) {
    window.__PERFORMANCE_MONITOR__.recordMetric('vue_error', {
      error: err.message,
      info,
      timestamp: Date.now()
    })
  }
}

// 挂载应用
app.mount('#app')
