<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <Header />

    <!-- 主要内容区域 -->
    <div class="pt-16 flex flex-col min-h-screen">
      <main class="flex-1">
        <router-view />
      </main>

      <!-- 页脚 -->
      <AppFooter />
    </div>

    <!-- 全局加载指示器 -->
    <LoadingSpinner v-if="isLoading" />

    <!-- 全局通知组件 -->
    <NotificationContainer />
  </div>
</template>

<script>
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import Header from '@/components/layout/Header.vue'
import AppFooter from '@/components/layout/AppFooter.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import NotificationContainer from '@/components/common/NotificationContainer.vue'

export default {
  name: 'App',
  components: {
    Head<PERSON>,
    <PERSON><PERSON><PERSON>ooter,
    Loading<PERSON>pinner,
    NotificationContainer,
  },
  setup() {
    const appStore = useAppStore()

    const isLoading = computed(() => appStore.isLoading)

    return {
      isLoading,
    }
  },
}
</script>

<style scoped>
#app {
  min-height: 100vh;
}

main {
  flex: 1;
}
</style>
