import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useRouter } from 'vue-router'

/**
 * 键盘快捷键组合式函数
 * 提供全局键盘快捷键支持
 */
export function useKeyboardShortcuts() {
  const router = useRouter()
  const isSearchModalOpen = ref(false)
  const shortcuts = ref(new Map())

  // 快捷键配置
  const defaultShortcuts = {
    // 搜索相关
    'ctrl+k': {
      description: '打开全局搜索',
      action: () => openGlobalSearch(),
      preventDefault: true
    },
    'cmd+k': {
      description: '打开全局搜索 (Mac)',
      action: () => openGlobalSearch(),
      preventDefault: true
    },
    '/': {
      description: '快速搜索',
      action: (event) => {
        // 只在非输入元素上触发
        if (!isInputElement(event.target)) {
          openGlobalSearch()
          return true
        }
        return false
      },
      preventDefault: false
    },
    
    // 导航相关
    'ctrl+1': {
      description: '跳转到首页',
      action: () => router.push('/'),
      preventDefault: true
    },
    'ctrl+2': {
      description: '跳转到质量大盘',
      action: () => router.push('/dashboard'),
      preventDefault: true
    },
    'ctrl+3': {
      description: '跳转到缺陷管理',
      action: () => router.push('/defect-management'),
      preventDefault: true
    },
    'ctrl+4': {
      description: '跳转到测试覆盖率',
      action: () => router.push('/coverage-management'),
      preventDefault: true
    },
    
    // 功能相关
    'ctrl+r': {
      description: '刷新当前页面',
      action: () => window.location.reload(),
      preventDefault: true
    },
    'ctrl+shift+d': {
      description: '切换暗色模式',
      action: () => toggleDarkMode(),
      preventDefault: true
    },
    'ctrl+shift+h': {
      description: '显示快捷键帮助',
      action: () => showShortcutHelp(),
      preventDefault: true
    },
    
    // ESC 键
    'escape': {
      description: '关闭模态框/取消操作',
      action: () => handleEscape(),
      preventDefault: false
    }
  }

  // 初始化快捷键
  const initShortcuts = () => {
    Object.entries(defaultShortcuts).forEach(([key, config]) => {
      shortcuts.value.set(key, config)
    })
  }

  // 键盘事件处理
  const handleKeyDown = (event) => {
    const key = getKeyString(event)
    const shortcut = shortcuts.value.get(key)
    
    if (shortcut) {
      const shouldPreventDefault = shortcut.action(event)
      
      if (shortcut.preventDefault || shouldPreventDefault) {
        event.preventDefault()
        event.stopPropagation()
      }
    }
  }

  // 获取按键字符串
  const getKeyString = (event) => {
    const parts = []
    
    if (event.ctrlKey) parts.push('ctrl')
    if (event.metaKey) parts.push('cmd')
    if (event.shiftKey) parts.push('shift')
    if (event.altKey) parts.push('alt')
    
    const key = event.key.toLowerCase()
    
    // 特殊键处理
    if (key === ' ') {
      parts.push('space')
    } else if (key === 'escape') {
      parts.push('escape')
    } else if (key === 'enter') {
      parts.push('enter')
    } else if (key === 'tab') {
      parts.push('tab')
    } else if (key === 'backspace') {
      parts.push('backspace')
    } else if (key === 'delete') {
      parts.push('delete')
    } else if (key.startsWith('arrow')) {
      parts.push(key)
    } else if (key.startsWith('f') && key.length <= 3) {
      // F1-F12 键
      parts.push(key)
    } else if (key.length === 1) {
      parts.push(key)
    }
    
    return parts.join('+')
  }

  // 检查是否为输入元素
  const isInputElement = (element) => {
    if (!element) return false
    
    const tagName = element.tagName.toLowerCase()
    const inputTypes = ['input', 'textarea', 'select']
    
    if (inputTypes.includes(tagName)) return true
    
    // 检查 contenteditable
    if (element.contentEditable === 'true') return true
    
    // 检查是否在可编辑区域内
    let parent = element.parentElement
    while (parent) {
      if (parent.contentEditable === 'true') return true
      if (inputTypes.includes(parent.tagName.toLowerCase())) return true
      parent = parent.parentElement
    }
    
    return false
  }

  // 打开全局搜索
  const openGlobalSearch = () => {
    // 触发全局搜索事件
    const event = new CustomEvent('open-global-search', {
      detail: { source: 'keyboard-shortcut' }
    })
    window.dispatchEvent(event)
    isSearchModalOpen.value = true
  }

  // 切换暗色模式
  const toggleDarkMode = () => {
    const event = new CustomEvent('toggle-dark-mode', {
      detail: { source: 'keyboard-shortcut' }
    })
    window.dispatchEvent(event)
  }

  // 显示快捷键帮助
  const showShortcutHelp = () => {
    const event = new CustomEvent('show-shortcut-help', {
      detail: { shortcuts: Array.from(shortcuts.value.entries()) }
    })
    window.dispatchEvent(event)
  }

  // 处理 ESC 键
  const handleEscape = () => {
    const event = new CustomEvent('global-escape', {
      detail: { source: 'keyboard-shortcut' }
    })
    window.dispatchEvent(event)
    
    // 关闭搜索模态框
    if (isSearchModalOpen.value) {
      isSearchModalOpen.value = false
    }
  }

  // 注册快捷键
  const registerShortcut = (key, config) => {
    shortcuts.value.set(key, config)
  }

  // 注销快捷键
  const unregisterShortcut = (key) => {
    shortcuts.value.delete(key)
  }

  // 获取所有快捷键
  const getAllShortcuts = () => {
    return Array.from(shortcuts.value.entries()).map(([key, config]) => ({
      key,
      description: config.description
    }))
  }

  // 检查快捷键是否已注册
  const isShortcutRegistered = (key) => {
    return shortcuts.value.has(key)
  }

  // 临时禁用快捷键
  const disableShortcuts = () => {
    document.removeEventListener('keydown', handleKeyDown)
  }

  // 重新启用快捷键
  const enableShortcuts = () => {
    document.addEventListener('keydown', handleKeyDown)
  }

  // 生命周期管理
  onMounted(() => {
    initShortcuts()
    document.addEventListener('keydown', handleKeyDown)
    
    // 监听搜索模态框状态变化
    window.addEventListener('search-modal-opened', () => {
      isSearchModalOpen.value = true
    })
    
    window.addEventListener('search-modal-closed', () => {
      isSearchModalOpen.value = false
    })
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
    window.removeEventListener('search-modal-opened', () => {})
    window.removeEventListener('search-modal-closed', () => {})
  })

  return {
    // 状态
    isSearchModalOpen,
    shortcuts,
    
    // 方法
    registerShortcut,
    unregisterShortcut,
    getAllShortcuts,
    isShortcutRegistered,
    disableShortcuts,
    enableShortcuts,
    openGlobalSearch,
    toggleDarkMode,
    showShortcutHelp
  }
}

/**
 * 快捷键帮助组合式函数
 * 提供快捷键帮助界面相关功能
 */
export function useShortcutHelp() {
  const showHelp = ref(false)
  const shortcuts = ref([])

  const openHelp = () => {
    showHelp.value = true
  }

  const closeHelp = () => {
    showHelp.value = false
  }

  const loadShortcuts = (shortcutList) => {
    shortcuts.value = shortcutList
  }

  // 按分类组织快捷键
  const categorizedShortcuts = computed(() => {
    const categories = {
      search: { title: '搜索', shortcuts: [] },
      navigation: { title: '导航', shortcuts: [] },
      actions: { title: '操作', shortcuts: [] },
      general: { title: '通用', shortcuts: [] }
    }

    shortcuts.value.forEach(shortcut => {
      if (shortcut.key.includes('ctrl+k') || shortcut.key.includes('cmd+k') || shortcut.key === '/') {
        categories.search.shortcuts.push(shortcut)
      } else if (shortcut.key.includes('ctrl+') && /\d/.test(shortcut.key)) {
        categories.navigation.shortcuts.push(shortcut)
      } else if (shortcut.key.includes('shift')) {
        categories.actions.shortcuts.push(shortcut)
      } else {
        categories.general.shortcuts.push(shortcut)
      }
    })

    return Object.values(categories).filter(category => category.shortcuts.length > 0)
  })

  onMounted(() => {
    // 监听显示快捷键帮助事件
    window.addEventListener('show-shortcut-help', (event) => {
      loadShortcuts(event.detail.shortcuts.map(([key, config]) => ({
        key,
        description: config.description
      })))
      openHelp()
    })
  })

  return {
    showHelp,
    shortcuts,
    categorizedShortcuts,
    openHelp,
    closeHelp,
    loadShortcuts
  }
}

/**
 * 搜索快捷键组合式函数
 * 专门处理搜索相关的快捷键
 */
export function useSearchShortcuts() {
  const searchInputRef = ref(null)
  const isSearchFocused = ref(false)

  // 搜索框快捷键
  const searchShortcuts = {
    'enter': {
      description: '执行搜索',
      action: () => performSearch()
    },
    'escape': {
      description: '清除搜索/关闭搜索',
      action: () => clearOrCloseSearch()
    },
    'arrowdown': {
      description: '选择下一个建议',
      action: () => selectNextSuggestion()
    },
    'arrowup': {
      description: '选择上一个建议',
      action: () => selectPreviousSuggestion()
    },
    'tab': {
      description: '选择当前建议',
      action: () => selectCurrentSuggestion()
    }
  }

  const handleSearchKeyDown = (event) => {
    if (!isSearchFocused.value) return

    const key = event.key.toLowerCase()
    const shortcut = searchShortcuts[key]

    if (shortcut) {
      event.preventDefault()
      shortcut.action()
    }
  }

  const focusSearch = () => {
    if (searchInputRef.value) {
      searchInputRef.value.focus()
      isSearchFocused.value = true
    }
  }

  const blurSearch = () => {
    isSearchFocused.value = false
  }

  const performSearch = () => {
    // 触发搜索事件
    const event = new CustomEvent('perform-search')
    window.dispatchEvent(event)
  }

  const clearOrCloseSearch = () => {
    const event = new CustomEvent('clear-or-close-search')
    window.dispatchEvent(event)
  }

  const selectNextSuggestion = () => {
    const event = new CustomEvent('select-next-suggestion')
    window.dispatchEvent(event)
  }

  const selectPreviousSuggestion = () => {
    const event = new CustomEvent('select-previous-suggestion')
    window.dispatchEvent(event)
  }

  const selectCurrentSuggestion = () => {
    const event = new CustomEvent('select-current-suggestion')
    window.dispatchEvent(event)
  }

  onMounted(() => {
    document.addEventListener('keydown', handleSearchKeyDown)
    
    // 监听全局搜索打开事件
    window.addEventListener('open-global-search', () => {
      setTimeout(() => focusSearch(), 100)
    })
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleSearchKeyDown)
  })

  return {
    searchInputRef,
    isSearchFocused,
    focusSearch,
    blurSearch,
    performSearch,
    clearOrCloseSearch
  }
}
