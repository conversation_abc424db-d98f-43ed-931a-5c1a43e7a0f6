import { ref, computed } from 'vue'

/**
 * 搜索结果高亮组合式函数
 * 提供文本高亮、关键词匹配等功能
 */
export function useSearchHighlight() {
  const highlightOptions = ref({
    caseSensitive: false,
    wholeWord: false,
    maxHighlights: 10,
    highlightClass: 'search-highlight',
    contextLength: 100
  })

  /**
   * 高亮匹配的文本
   * @param {string} text - 原始文本
   * @param {string} query - 搜索查询
   * @param {object} options - 高亮选项
   * @returns {string} 高亮后的HTML
   */
  const highlightText = (text, query, options = {}) => {
    if (!text || !query) return text

    const opts = { ...highlightOptions.value, ...options }
    const searchTerms = parseSearchQuery(query)
    
    let highlightedText = text
    let highlightCount = 0

    searchTerms.forEach(term => {
      if (highlightCount >= opts.maxHighlights) return

      const flags = opts.caseSensitive ? 'g' : 'gi'
      const pattern = opts.wholeWord 
        ? new RegExp(`\\b(${escapeRegExp(term)})\\b`, flags)
        : new RegExp(`(${escapeRegExp(term)})`, flags)

      highlightedText = highlightedText.replace(pattern, (match) => {
        if (highlightCount >= opts.maxHighlights) return match
        highlightCount++
        return `<mark class="${opts.highlightClass}">${match}</mark>`
      })
    })

    return highlightedText
  }

  /**
   * 获取包含关键词的文本片段
   * @param {string} text - 原始文本
   * @param {string} query - 搜索查询
   * @param {number} contextLength - 上下文长度
   * @returns {Array} 文本片段数组
   */
  const getTextSnippets = (text, query, contextLength = 100) => {
    if (!text || !query) return [{ text, highlighted: false }]

    const searchTerms = parseSearchQuery(query)
    const snippets = []
    const matches = []

    // 找到所有匹配位置
    searchTerms.forEach(term => {
      const flags = highlightOptions.value.caseSensitive ? 'gi' : 'gi'
      const regex = new RegExp(escapeRegExp(term), flags)
      let match

      while ((match = regex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          term: match[0]
        })
      }
    })

    if (matches.length === 0) {
      return [{ text: text.substring(0, contextLength * 2), highlighted: false }]
    }

    // 按位置排序并合并重叠的匹配
    matches.sort((a, b) => a.start - b.start)
    const mergedMatches = mergeOverlappingMatches(matches)

    // 生成文本片段
    mergedMatches.forEach((match, index) => {
      const start = Math.max(0, match.start - contextLength)
      const end = Math.min(text.length, match.end + contextLength)
      
      let snippet = text.substring(start, end)
      
      // 添加省略号
      if (start > 0) snippet = '...' + snippet
      if (end < text.length) snippet = snippet + '...'

      // 高亮匹配的部分
      const highlightedSnippet = highlightText(snippet, query)
      
      snippets.push({
        text: highlightedSnippet,
        highlighted: true,
        position: index + 1,
        total: mergedMatches.length
      })
    })

    return snippets.slice(0, 3) // 最多返回3个片段
  }

  /**
   * 计算匹配分数
   * @param {string} text - 文本
   * @param {string} query - 查询
   * @returns {number} 匹配分数 (0-1)
   */
  const calculateMatchScore = (text, query) => {
    if (!text || !query) return 0

    const searchTerms = parseSearchQuery(query)
    let totalScore = 0
    let maxPossibleScore = searchTerms.length

    searchTerms.forEach(term => {
      const termLower = term.toLowerCase()
      const textLower = text.toLowerCase()

      // 完全匹配
      if (textLower === termLower) {
        totalScore += 1
      }
      // 开头匹配
      else if (textLower.startsWith(termLower)) {
        totalScore += 0.8
      }
      // 包含匹配
      else if (textLower.includes(termLower)) {
        totalScore += 0.6
      }
      // 模糊匹配
      else if (fuzzyMatch(textLower, termLower)) {
        totalScore += 0.3
      }
    })

    return Math.min(totalScore / maxPossibleScore, 1)
  }

  /**
   * 获取匹配统计信息
   * @param {string} text - 文本
   * @param {string} query - 查询
   * @returns {object} 统计信息
   */
  const getMatchStats = (text, query) => {
    if (!text || !query) return { matches: 0, terms: 0, coverage: 0 }

    const searchTerms = parseSearchQuery(query)
    let totalMatches = 0
    let matchedTerms = 0

    searchTerms.forEach(term => {
      const flags = highlightOptions.value.caseSensitive ? 'g' : 'gi'
      const regex = new RegExp(escapeRegExp(term), flags)
      const matches = (text.match(regex) || []).length
      
      if (matches > 0) {
        totalMatches += matches
        matchedTerms++
      }
    })

    const coverage = searchTerms.length > 0 ? matchedTerms / searchTerms.length : 0

    return {
      matches: totalMatches,
      terms: matchedTerms,
      totalTerms: searchTerms.length,
      coverage: Math.round(coverage * 100)
    }
  }

  // 辅助函数
  const parseSearchQuery = (query) => {
    // 解析搜索查询，支持引号、AND/OR等
    const terms = []
    const quotedTerms = query.match(/"([^"]+)"/g) || []
    
    // 提取引号内的短语
    quotedTerms.forEach(quoted => {
      terms.push(quoted.replace(/"/g, ''))
      query = query.replace(quoted, '')
    })

    // 提取其他单词
    const words = query.trim().split(/\s+/).filter(word => word.length > 0)
    terms.push(...words)

    return terms.filter(term => term.length > 0)
  }

  const escapeRegExp = (string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  const mergeOverlappingMatches = (matches) => {
    if (matches.length <= 1) return matches

    const merged = [matches[0]]
    
    for (let i = 1; i < matches.length; i++) {
      const current = matches[i]
      const last = merged[merged.length - 1]
      
      if (current.start <= last.end + 10) { // 允许10个字符的间隔
        last.end = Math.max(last.end, current.end)
      } else {
        merged.push(current)
      }
    }
    
    return merged
  }

  const fuzzyMatch = (text, pattern) => {
    const textLen = text.length
    const patternLen = pattern.length
    
    if (patternLen > textLen) return false
    if (patternLen === textLen) return text === pattern
    
    let textIndex = 0
    let patternIndex = 0
    
    while (textIndex < textLen && patternIndex < patternLen) {
      if (text[textIndex] === pattern[patternIndex]) {
        patternIndex++
      }
      textIndex++
    }
    
    return patternIndex === patternLen
  }

  // 预设高亮样式
  const highlightStyles = computed(() => ({
    default: {
      backgroundColor: '#fff3cd',
      color: '#856404',
      padding: '1px 2px',
      borderRadius: '2px',
      fontWeight: '500'
    },
    primary: {
      backgroundColor: '#e0e7ff',
      color: '#4338ca',
      padding: '1px 2px',
      borderRadius: '2px',
      fontWeight: '500'
    },
    success: {
      backgroundColor: '#d1fae5',
      color: '#065f46',
      padding: '1px 2px',
      borderRadius: '2px',
      fontWeight: '500'
    },
    warning: {
      backgroundColor: '#fef3c7',
      color: '#92400e',
      padding: '1px 2px',
      borderRadius: '2px',
      fontWeight: '500'
    }
  }))

  return {
    // 状态
    highlightOptions,
    highlightStyles,
    
    // 方法
    highlightText,
    getTextSnippets,
    calculateMatchScore,
    getMatchStats,
    parseSearchQuery
  }
}
