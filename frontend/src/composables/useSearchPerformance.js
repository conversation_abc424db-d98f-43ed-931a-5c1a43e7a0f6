import { ref, computed, watch } from 'vue'

/**
 * 搜索性能优化组合式函数
 * 提供防抖、缓存、预加载等性能优化功能
 */
export function useSearchPerformance() {
  // 性能配置
  const performanceConfig = ref({
    debounceTime: 300,
    cacheSize: 100,
    cacheExpiry: 5 * 60 * 1000, // 5分钟
    prefetchThreshold: 3, // 输入3个字符后开始预取
    maxConcurrentRequests: 3,
    requestTimeout: 10000
  })

  // 缓存管理
  const searchCache = ref(new Map())
  const requestQueue = ref([])
  const activeRequests = ref(new Set())
  const performanceMetrics = ref({
    totalSearches: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageResponseTime: 0,
    totalResponseTime: 0
  })

  // 防抖定时器
  let debounceTimer = null
  let prefetchTimer = null

  /**
   * 防抖搜索函数
   * @param {Function} searchFn - 搜索函数
   * @param {number} delay - 延迟时间
   * @returns {Function} 防抖后的搜索函数
   */
  const debounceSearch = (searchFn, delay = performanceConfig.value.debounceTime) => {
    return (...args) => {
      clearTimeout(debounceTimer)
      debounceTimer = setTimeout(() => {
        searchFn(...args)
      }, delay)
    }
  }

  /**
   * 缓存键生成
   * @param {object} searchParams - 搜索参数
   * @returns {string} 缓存键
   */
  const generateCacheKey = (searchParams) => {
    const { query, type, filters, page, pageSize, sortBy, sortOrder } = searchParams
    return JSON.stringify({
      query: query?.toLowerCase().trim(),
      type,
      filters,
      page,
      pageSize,
      sortBy,
      sortOrder
    })
  }

  /**
   * 从缓存获取搜索结果
   * @param {string} cacheKey - 缓存键
   * @returns {object|null} 缓存的结果
   */
  const getCachedResult = (cacheKey) => {
    const cached = searchCache.value.get(cacheKey)
    
    if (!cached) {
      performanceMetrics.value.cacheMisses++
      return null
    }

    // 检查缓存是否过期
    if (Date.now() - cached.timestamp > performanceConfig.value.cacheExpiry) {
      searchCache.value.delete(cacheKey)
      performanceMetrics.value.cacheMisses++
      return null
    }

    performanceMetrics.value.cacheHits++
    return cached.data
  }

  /**
   * 缓存搜索结果
   * @param {string} cacheKey - 缓存键
   * @param {object} result - 搜索结果
   */
  const setCachedResult = (cacheKey, result) => {
    // 清理过期缓存
    if (searchCache.value.size >= performanceConfig.value.cacheSize) {
      cleanExpiredCache()
    }

    // 如果仍然超出限制，删除最旧的缓存
    if (searchCache.value.size >= performanceConfig.value.cacheSize) {
      const oldestKey = searchCache.value.keys().next().value
      searchCache.value.delete(oldestKey)
    }

    searchCache.value.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    })
  }

  /**
   * 清理过期缓存
   */
  const cleanExpiredCache = () => {
    const now = Date.now()
    const expiry = performanceConfig.value.cacheExpiry

    for (const [key, value] of searchCache.value.entries()) {
      if (now - value.timestamp > expiry) {
        searchCache.value.delete(key)
      }
    }
  }

  /**
   * 优化的搜索函数
   * @param {Function} originalSearchFn - 原始搜索函数
   * @returns {Function} 优化后的搜索函数
   */
  const optimizedSearch = (originalSearchFn) => {
    return async (searchParams) => {
      const startTime = Date.now()
      const cacheKey = generateCacheKey(searchParams)

      // 尝试从缓存获取结果
      const cachedResult = getCachedResult(cacheKey)
      if (cachedResult) {
        return cachedResult
      }

      // 检查并发请求限制
      if (activeRequests.value.size >= performanceConfig.value.maxConcurrentRequests) {
        return new Promise((resolve, reject) => {
          requestQueue.value.push({ searchParams, resolve, reject })
        })
      }

      // 执行搜索
      const requestId = Date.now() + Math.random()
      activeRequests.value.add(requestId)

      try {
        const result = await Promise.race([
          originalSearchFn(searchParams),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Search timeout')), 
            performanceConfig.value.requestTimeout)
          )
        ])

        // 缓存结果
        setCachedResult(cacheKey, result)

        // 更新性能指标
        const responseTime = Date.now() - startTime
        updatePerformanceMetrics(responseTime)

        return result
      } catch (error) {
        console.error('搜索请求失败:', error)
        throw error
      } finally {
        activeRequests.value.delete(requestId)
        processRequestQueue()
      }
    }
  }

  /**
   * 处理请求队列
   */
  const processRequestQueue = () => {
    if (requestQueue.value.length === 0) return
    if (activeRequests.value.size >= performanceConfig.value.maxConcurrentRequests) return

    const { searchParams, resolve, reject } = requestQueue.value.shift()
    
    optimizedSearch(async (params) => {
      // 这里需要原始搜索函数的引用
      throw new Error('Original search function not available in queue processing')
    })(searchParams)
      .then(resolve)
      .catch(reject)
  }

  /**
   * 预取搜索建议
   * @param {string} query - 查询字符串
   * @param {Function} suggestionFn - 建议获取函数
   */
  const prefetchSuggestions = (query, suggestionFn) => {
    if (query.length < performanceConfig.value.prefetchThreshold) return

    clearTimeout(prefetchTimer)
    prefetchTimer = setTimeout(async () => {
      try {
        const suggestions = await suggestionFn(query)
        // 预缓存建议结果
        const cacheKey = `suggestions:${query.toLowerCase()}`
        setCachedResult(cacheKey, suggestions)
      } catch (error) {
        console.warn('预取建议失败:', error)
      }
    }, 100) // 较短的延迟用于预取
  }

  /**
   * 更新性能指标
   * @param {number} responseTime - 响应时间
   */
  const updatePerformanceMetrics = (responseTime) => {
    performanceMetrics.value.totalSearches++
    performanceMetrics.value.totalResponseTime += responseTime
    performanceMetrics.value.averageResponseTime = 
      performanceMetrics.value.totalResponseTime / performanceMetrics.value.totalSearches
  }

  /**
   * 获取缓存统计
   */
  const getCacheStats = computed(() => {
    const total = performanceMetrics.value.cacheHits + performanceMetrics.value.cacheMisses
    const hitRate = total > 0 ? (performanceMetrics.value.cacheHits / total * 100).toFixed(1) : 0

    return {
      size: searchCache.value.size,
      maxSize: performanceConfig.value.cacheSize,
      hitRate: `${hitRate}%`,
      hits: performanceMetrics.value.cacheHits,
      misses: performanceMetrics.value.cacheMisses
    }
  })

  /**
   * 获取性能统计
   */
  const getPerformanceStats = computed(() => ({
    totalSearches: performanceMetrics.value.totalSearches,
    averageResponseTime: Math.round(performanceMetrics.value.averageResponseTime),
    activeRequests: activeRequests.value.size,
    queuedRequests: requestQueue.value.length,
    cacheStats: getCacheStats.value
  }))

  /**
   * 清空缓存
   */
  const clearCache = () => {
    searchCache.value.clear()
    performanceMetrics.value.cacheHits = 0
    performanceMetrics.value.cacheMisses = 0
  }

  /**
   * 重置性能指标
   */
  const resetMetrics = () => {
    performanceMetrics.value = {
      totalSearches: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    }
  }

  /**
   * 智能预加载
   * @param {Array} searchHistory - 搜索历史
   * @param {Function} searchFn - 搜索函数
   */
  const intelligentPreload = async (searchHistory, searchFn) => {
    if (!searchHistory || searchHistory.length === 0) return

    // 分析搜索模式
    const patterns = analyzeSearchPatterns(searchHistory)
    
    // 预加载可能的搜索
    for (const pattern of patterns.slice(0, 3)) { // 最多预加载3个
      try {
        const cacheKey = generateCacheKey(pattern)
        if (!getCachedResult(cacheKey)) {
          const result = await searchFn(pattern)
          setCachedResult(cacheKey, result)
        }
      } catch (error) {
        console.warn('智能预加载失败:', error)
      }
    }
  }

  /**
   * 分析搜索模式
   * @param {Array} history - 搜索历史
   * @returns {Array} 搜索模式
   */
  const analyzeSearchPatterns = (history) => {
    const patterns = new Map()
    
    history.forEach(item => {
      const key = `${item.type}:${item.query.substring(0, 3)}`
      if (!patterns.has(key)) {
        patterns.set(key, { count: 0, lastParams: null })
      }
      
      const pattern = patterns.get(key)
      pattern.count++
      pattern.lastParams = {
        query: item.query,
        type: item.type,
        filters: item.filters || {}
      }
    })

    // 按频率排序
    return Array.from(patterns.values())
      .sort((a, b) => b.count - a.count)
      .map(p => p.lastParams)
      .filter(p => p !== null)
  }

  // 定期清理过期缓存
  setInterval(cleanExpiredCache, 60000) // 每分钟清理一次

  return {
    // 配置
    performanceConfig,
    
    // 状态
    searchCache,
    performanceMetrics,
    
    // 计算属性
    getCacheStats,
    getPerformanceStats,
    
    // 方法
    debounceSearch,
    optimizedSearch,
    prefetchSuggestions,
    intelligentPreload,
    getCachedResult,
    setCachedResult,
    clearCache,
    resetMetrics,
    cleanExpiredCache
  }
}
