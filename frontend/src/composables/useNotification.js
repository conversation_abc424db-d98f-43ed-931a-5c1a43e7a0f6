/**
 * 通知系统 Composable
 * 基于现有的 Pinia app store
 */

import { useAppStore } from '@/stores/app'

export function useNotification() {
  const appStore = useAppStore()

  /**
   * 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型: 'success', 'error', 'warning', 'info'
   * @param {string} title - 通知标题
   * @param {number} duration - 显示时长（毫秒），0表示不自动关闭
   */
  const showNotification = (message, type = 'info', title = null, duration = null) => {
    const notification = {
      message,
      type,
      title: title || getDefaultTitle(type),
      duration: duration || getDefaultDuration(type)
    }

    appStore.addNotification(notification)
  }

  /**
   * 获取默认标题
   */
  const getDefaultTitle = (type) => {
    const titles = {
      success: '成功',
      error: '错误',
      warning: '警告',
      info: '信息'
    }
    return titles[type] || '通知'
  }

  /**
   * 获取默认持续时间
   */
  const getDefaultDuration = (type) => {
    const durations = {
      success: 3000,
      error: 5000,
      warning: 4000,
      info: 3000
    }
    return durations[type] || 3000
  }

  /**
   * 显示成功通知
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   */
  const showSuccess = (message, title = '成功') => {
    appStore.showSuccess(message, title)
  }

  /**
   * 显示错误通知
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   */
  const showError = (message, title = '错误') => {
    appStore.showError(message, title)
  }

  /**
   * 显示警告通知
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   */
  const showWarning = (message, title = '警告') => {
    appStore.showWarning(message, title)
  }

  /**
   * 显示信息通知
   * @param {string} message - 消息内容
   * @param {string} title - 标题
   */
  const showInfo = (message, title = '信息') => {
    appStore.showInfo(message, title)
  }

  /**
   * 清除所有通知
   */
  const clearAllNotifications = () => {
    appStore.clearNotifications()
  }

  return {
    notifications: appStore.notifications,
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    clearAllNotifications
  }
}

// 全局通知方法，可以在任何地方使用
export const notify = {
  success: (message, title) => {
    const { showSuccess } = useNotification()
    showSuccess(message, title)
  },
  error: (message, title) => {
    const { showError } = useNotification()
    showError(message, title)
  },
  warning: (message, title) => {
    const { showWarning } = useNotification()
    showWarning(message, title)
  },
  info: (message, title) => {
    const { showInfo } = useNotification()
    showInfo(message, title)
  }
}
