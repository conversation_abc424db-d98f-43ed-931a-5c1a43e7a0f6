import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    
    // 添加认证token（如果有的话）
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
      params: config.params,
      data: config.data
    })
    
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      duration: `${duration}ms`,
      data: response.data
    })
    
    return response
  },
  (error) => {
    // 计算请求耗时
    if (error.config?.metadata?.startTime) {
      const endTime = new Date()
      const duration = endTime - error.config.metadata.startTime
      console.error(`❌ API Error: ${error.config.method?.toUpperCase()} ${error.config.url}`, {
        status: error.response?.status,
        duration: `${duration}ms`,
        message: error.message,
        data: error.response?.data
      })
    }
    
    // 统一错误处理
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('auth_token')
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
          
        case 403:
          // 禁止访问
          console.error('Access forbidden:', data.message || 'No permission')
          break
          
        case 404:
          // 资源不存在
          console.error('Resource not found:', error.config.url)
          break
          
        case 422:
          // 验证错误
          console.error('Validation error:', data.detail || data.message)
          break
          
        case 500:
          // 服务器内部错误
          console.error('Server error:', data.message || 'Internal server error')
          break
          
        default:
          console.error('HTTP error:', status, data.message || error.message)
      }
      
      // 返回格式化的错误信息
      const errorMessage = data?.message || data?.detail || error.message || 'Unknown error'
      error.message = errorMessage
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('Network error:', error.message)
      error.message = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      console.error('Request setup error:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// 便捷方法
export const apiMethods = {
  // GET请求
  get: (url, config = {}) => api.get(url, config),
  
  // POST请求
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  
  // PUT请求
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  
  // PATCH请求
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  
  // DELETE请求
  delete: (url, config = {}) => api.delete(url, config),
  
  // 文件上传
  upload: (url, formData, config = {}) => {
    return api.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config.headers
      }
    })
  },
  
  // 文件下载
  download: (url, config = {}) => {
    return api.get(url, {
      ...config,
      responseType: 'blob'
    })
  }
}

// 请求状态枚举
export const RequestStatus = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error'
}

// 创建请求状态管理器
export const createRequestState = () => {
  return {
    status: RequestStatus.IDLE,
    data: null,
    error: null,
    loading: false
  }
}

// 请求重试工具
export const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  let lastError
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error
      
      if (i < maxRetries) {
        console.warn(`Request failed, retrying in ${delay}ms... (${i + 1}/${maxRetries})`)
        await new Promise(resolve => setTimeout(resolve, delay))
        delay *= 2 // 指数退避
      }
    }
  }
  
  throw lastError
}

// 并发请求控制
export const createConcurrentRequestManager = (maxConcurrent = 5) => {
  let running = 0
  const queue = []
  
  const execute = async (requestFn) => {
    return new Promise((resolve, reject) => {
      queue.push({ requestFn, resolve, reject })
      processQueue()
    })
  }
  
  const processQueue = async () => {
    if (running >= maxConcurrent || queue.length === 0) {
      return
    }
    
    running++
    const { requestFn, resolve, reject } = queue.shift()
    
    try {
      const result = await requestFn()
      resolve(result)
    } catch (error) {
      reject(error)
    } finally {
      running--
      processQueue()
    }
  }
  
  return { execute }
}

// 请求缓存管理器
export const createRequestCache = (ttl = 5 * 60 * 1000) => { // 默认5分钟
  const cache = new Map()
  
  const get = (key) => {
    const item = cache.get(key)
    if (!item) return null
    
    if (Date.now() > item.expiry) {
      cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  const set = (key, data) => {
    cache.set(key, {
      data,
      expiry: Date.now() + ttl
    })
  }
  
  const clear = () => {
    cache.clear()
  }
  
  const remove = (key) => {
    cache.delete(key)
  }
  
  return { get, set, clear, remove }
}

// 默认导出axios实例
export default api
