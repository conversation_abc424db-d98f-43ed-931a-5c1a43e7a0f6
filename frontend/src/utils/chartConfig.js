/**
 * Chart.js 全局配置
 * 统一注册所有需要的Chart.js组件，避免重复注册和控制器未注册问题
 */

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  BarElement,
  BarController,
  ArcElement,
  DoughnutController,
  PieController,
  Title,
  Tooltip,
  Legend,
  Filler,
  SubTitle,
  RadialLinearScale,
  TimeScale,
  TimeSeriesScale
} from 'chart.js'

// 全局注册所有Chart.js组件（只注册一次）
let isRegistered = false

export function registerChartComponents() {
  if (isRegistered) {
    console.log('Chart.js components already registered')
    return
  }

  try {
    ChartJS.register(
      // 坐标轴
      CategoryScale,
      LinearScale,
      RadialLinearScale,
      TimeScale,
      TimeSeriesScale,
      
      // 元素
      PointElement,
      LineElement,
      BarElement,
      ArcElement,
      
      // 控制器
      LineController,
      BarController,
      DoughnutController,
      PieController,
      
      // 插件
      Title,
      SubTitle,
      <PERSON><PERSON><PERSON>,
      <PERSON>,
      Filler
    )
    
    isRegistered = true
    console.log('Chart.js components registered successfully')
  } catch (error) {
    console.error('Failed to register Chart.js components:', error)
  }
}

// 默认图表配置
export const defaultChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        usePointStyle: true,
        padding: 20
      }
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1
    }
  },
  scales: {
    x: {
      display: true,
      grid: {
        display: false
      },
      ticks: {
        color: '#6b7280'
      }
    },
    y: {
      display: true,
      beginAtZero: true,
      grid: {
        color: 'rgba(0, 0, 0, 0.1)'
      },
      ticks: {
        color: '#6b7280'
      }
    }
  },
  elements: {
    line: {
      tension: 0.3
    },
    point: {
      radius: 4,
      hoverRadius: 6
    }
  },
  interaction: {
    mode: 'nearest',
    axis: 'x',
    intersect: false
  },
  animation: {
    duration: 750,
    easing: 'easeInOutQuart'
  }
}

// 线图特定配置
export const lineChartOptions = {
  ...defaultChartOptions,
  scales: {
    ...defaultChartOptions.scales,
    y: {
      ...defaultChartOptions.scales.y,
      beginAtZero: true,
      max: 100,
      ticks: {
        ...defaultChartOptions.scales.y.ticks,
        callback: function(value) {
          return value + '%'
        }
      }
    }
  }
}

// 柱状图特定配置
export const barChartOptions = {
  ...defaultChartOptions,
  plugins: {
    ...defaultChartOptions.plugins,
    legend: {
      ...defaultChartOptions.plugins.legend,
      display: false
    }
  }
}

// 饼图特定配置
export const pieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'right',
      labels: {
        usePointStyle: true,
        padding: 20
      }
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff'
    }
  }
}

// 数据验证函数
export function validateChartData(data) {
  if (!data) {
    console.warn('Chart data is null or undefined')
    return false
  }
  
  if (!data.labels || !Array.isArray(data.labels)) {
    console.warn('Chart data missing valid labels array')
    return false
  }
  
  if (!data.datasets || !Array.isArray(data.datasets)) {
    console.warn('Chart data missing valid datasets array')
    return false
  }
  
  if (data.datasets.length === 0) {
    console.warn('Chart data has empty datasets')
    return false
  }
  
  // 验证每个数据集
  for (let i = 0; i < data.datasets.length; i++) {
    const dataset = data.datasets[i]
    if (!dataset.data || !Array.isArray(dataset.data)) {
      console.warn(`Dataset ${i} missing valid data array`)
      return false
    }
  }
  
  return true
}

// 创建安全的图表数据
export function createSafeChartData(rawData, fallbackData = null) {
  if (validateChartData(rawData)) {
    return rawData
  }
  
  if (fallbackData && validateChartData(fallbackData)) {
    console.warn('Using fallback chart data')
    return fallbackData
  }
  
  // 返回默认的空数据
  console.warn('Using default empty chart data')
  return {
    labels: ['暂无数据'],
    datasets: [{
      label: '暂无数据',
      data: [0],
      borderColor: '#e5e7eb',
      backgroundColor: 'rgba(229, 231, 235, 0.1)',
      tension: 0.3,
      fill: true
    }]
  }
}

// 获取默认图表配置
export function getDefaultChartOptions(chartType = 'line') {
  switch (chartType) {
    case 'line':
      return lineChartOptions
    case 'bar':
      return barChartOptions
    case 'pie':
    case 'doughnut':
      return pieChartOptions
    default:
      return defaultChartOptions
  }
}

// 导出Chart.js实例
export { ChartJS }

// 自动注册组件
registerChartComponents()
