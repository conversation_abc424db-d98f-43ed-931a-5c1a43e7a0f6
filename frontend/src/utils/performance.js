/**
 * 前端性能监控和优化工具
 * 提供性能指标收集、分析和优化建议
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = new Map()
    this.isEnabled = process.env.NODE_ENV === 'development'
    this.init()
  }

  init() {
    if (!this.isEnabled) return

    // 监听页面加载性能
    this.observePageLoad()
    
    // 监听长任务
    this.observeLongTasks()
    
    // 监听布局偏移
    this.observeLayoutShift()
    
    // 监听首次内容绘制
    this.observePaint()
    
    // 监听资源加载
    this.observeResources()
  }

  // 页面加载性能监控
  observePageLoad() {
    if (typeof window === 'undefined') return

    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0]
        if (navigation) {
          this.recordMetric('page_load', {
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
            domInteractive: navigation.domInteractive - navigation.fetchStart,
            firstByte: navigation.responseStart - navigation.requestStart,
            dnsLookup: navigation.domainLookupEnd - navigation.domainLookupStart,
            tcpConnect: navigation.connectEnd - navigation.connectStart,
            totalTime: navigation.loadEventEnd - navigation.fetchStart
          })
        }
      }, 0)
    })
  }

  // 长任务监控
  observeLongTasks() {
    if (!('PerformanceObserver' in window)) return

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('long_task', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name
          })
        }
      })
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.set('longtask', observer)
    } catch (error) {
      console.warn('Long task observer not supported:', error)
    }
  }

  // 布局偏移监控
  observeLayoutShift() {
    if (!('PerformanceObserver' in window)) return

    try {
      const observer = new PerformanceObserver((list) => {
        let clsValue = 0
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        }
        if (clsValue > 0) {
          this.recordMetric('layout_shift', { value: clsValue })
        }
      })
      observer.observe({ entryTypes: ['layout-shift'] })
      this.observers.set('layout-shift', observer)
    } catch (error) {
      console.warn('Layout shift observer not supported:', error)
    }
  }

  // 绘制性能监控
  observePaint() {
    if (!('PerformanceObserver' in window)) return

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('paint', {
            name: entry.name,
            startTime: entry.startTime
          })
        }
      })
      observer.observe({ entryTypes: ['paint'] })
      this.observers.set('paint', observer)
    } catch (error) {
      console.warn('Paint observer not supported:', error)
    }
  }

  // 资源加载监控
  observeResources() {
    if (!('PerformanceObserver' in window)) return

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.transferSize > 0) {
            this.recordMetric('resource', {
              name: entry.name,
              duration: entry.duration,
              transferSize: entry.transferSize,
              encodedBodySize: entry.encodedBodySize,
              decodedBodySize: entry.decodedBodySize,
              initiatorType: entry.initiatorType
            })
          }
        }
      })
      observer.observe({ entryTypes: ['resource'] })
      this.observers.set('resource', observer)
    } catch (error) {
      console.warn('Resource observer not supported:', error)
    }
  }

  // 记录性能指标
  recordMetric(type, data) {
    if (!this.metrics.has(type)) {
      this.metrics.set(type, [])
    }
    
    this.metrics.get(type).push({
      ...data,
      timestamp: Date.now()
    })

    // 限制存储的指标数量
    const maxEntries = 100
    const entries = this.metrics.get(type)
    if (entries.length > maxEntries) {
      entries.splice(0, entries.length - maxEntries)
    }
  }

  // 获取性能报告
  getPerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: {},
      recommendations: []
    }

    // 处理各类性能指标
    for (const [type, entries] of this.metrics) {
      report.metrics[type] = this.analyzeMetrics(type, entries)
    }

    // 生成优化建议
    report.recommendations = this.generateRecommendations(report.metrics)

    return report
  }

  // 分析性能指标
  analyzeMetrics(type, entries) {
    if (entries.length === 0) return null

    const analysis = {
      count: entries.length,
      latest: entries[entries.length - 1]
    }

    switch (type) {
      case 'page_load':
        const latest = entries[entries.length - 1]
        analysis.summary = {
          totalTime: latest.totalTime,
          domContentLoaded: latest.domContentLoaded,
          firstByte: latest.firstByte,
          domInteractive: latest.domInteractive
        }
        break

      case 'long_task':
        const durations = entries.map(e => e.duration)
        analysis.summary = {
          totalTasks: durations.length,
          averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
          maxDuration: Math.max(...durations)
        }
        break

      case 'layout_shift':
        const clsValues = entries.map(e => e.value)
        analysis.summary = {
          totalShifts: clsValues.length,
          cumulativeScore: clsValues.reduce((a, b) => a + b, 0)
        }
        break

      case 'resource':
        const sizes = entries.map(e => e.transferSize)
        const resourceDurations = entries.map(e => e.duration)
        analysis.summary = {
          totalResources: entries.length,
          totalSize: sizes.reduce((a, b) => a + b, 0),
          averageLoadTime: resourceDurations.reduce((a, b) => a + b, 0) / resourceDurations.length,
          largestResource: Math.max(...sizes)
        }
        break
    }

    return analysis
  }

  // 生成优化建议
  generateRecommendations(metrics) {
    const recommendations = []

    // 页面加载优化建议
    if (metrics.page_load) {
      const { totalTime, firstByte, domContentLoaded } = metrics.page_load.summary
      
      if (totalTime > 3000) {
        recommendations.push({
          type: 'performance',
          priority: 'high',
          message: `页面加载时间过长 (${Math.round(totalTime)}ms)，建议优化资源加载和代码分割`
        })
      }
      
      if (firstByte > 500) {
        recommendations.push({
          type: 'network',
          priority: 'medium',
          message: `首字节时间较长 (${Math.round(firstByte)}ms)，建议优化服务器响应时间`
        })
      }
    }

    // 长任务优化建议
    if (metrics.long_task) {
      const { totalTasks, maxDuration } = metrics.long_task.summary
      
      if (totalTasks > 5) {
        recommendations.push({
          type: 'javascript',
          priority: 'high',
          message: `检测到 ${totalTasks} 个长任务，建议使用 Web Workers 或代码分割优化`
        })
      }
      
      if (maxDuration > 100) {
        recommendations.push({
          type: 'javascript',
          priority: 'medium',
          message: `最长任务执行时间 ${Math.round(maxDuration)}ms，建议拆分大型计算任务`
        })
      }
    }

    // 布局偏移优化建议
    if (metrics.layout_shift) {
      const { cumulativeScore } = metrics.layout_shift.summary
      
      if (cumulativeScore > 0.1) {
        recommendations.push({
          type: 'layout',
          priority: 'medium',
          message: `累积布局偏移分数 ${cumulativeScore.toFixed(3)}，建议为图片和广告预留空间`
        })
      }
    }

    // 资源加载优化建议
    if (metrics.resource) {
      const { totalSize, largestResource } = metrics.resource.summary
      
      if (totalSize > 2 * 1024 * 1024) { // 2MB
        recommendations.push({
          type: 'resources',
          priority: 'medium',
          message: `总资源大小 ${Math.round(totalSize / 1024 / 1024)}MB，建议启用压缩和缓存`
        })
      }
      
      if (largestResource > 500 * 1024) { // 500KB
        recommendations.push({
          type: 'resources',
          priority: 'low',
          message: `最大资源 ${Math.round(largestResource / 1024)}KB，建议考虑懒加载或分割`
        })
      }
    }

    return recommendations
  }

  // 清理监控器
  cleanup() {
    for (const observer of this.observers.values()) {
      observer.disconnect()
    }
    this.observers.clear()
    this.metrics.clear()
  }

  // 导出性能数据
  exportData() {
    return {
      metrics: Object.fromEntries(this.metrics),
      report: this.getPerformanceReport()
    }
  }
}

// 创建全局性能监控实例
const performanceMonitor = new PerformanceMonitor()

// 导出工具函数
export const startPerformanceMonitoring = () => {
  return performanceMonitor
}

export const getPerformanceReport = () => {
  return performanceMonitor.getPerformanceReport()
}

export const recordCustomMetric = (name, data) => {
  performanceMonitor.recordMetric(`custom_${name}`, data)
}

// 组件性能监控装饰器
export const withPerformanceMonitoring = (componentName) => {
  return (component) => {
    const originalMounted = component.mounted
    const originalUnmounted = component.unmounted

    component.mounted = function(...args) {
      const startTime = performance.now()
      
      if (originalMounted) {
        originalMounted.apply(this, args)
      }
      
      const endTime = performance.now()
      recordCustomMetric('component_mount', {
        name: componentName,
        duration: endTime - startTime
      })
    }

    component.unmounted = function(...args) {
      const startTime = performance.now()
      
      if (originalUnmounted) {
        originalUnmounted.apply(this, args)
      }
      
      const endTime = performance.now()
      recordCustomMetric('component_unmount', {
        name: componentName,
        duration: endTime - startTime
      })
    }

    return component
  }
}

export default performanceMonitor
