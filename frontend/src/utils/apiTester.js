/**
 * API测试工具
 * 用于测试前后端API集成
 */

import apiService from '@/services/api'

class APITester {
  constructor() {
    this.testResults = []
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'
  }

  /**
   * 运行所有搜索API测试
   */
  async runAllSearchTests() {
    console.log('🚀 开始运行搜索API测试...')
    
    const tests = [
      this.testSearchSuggestions,
      this.testBasicSearch,
      this.testAdvancedSearch,
      this.testQuickSearches,
      this.testSearchStats,
      this.testSearchPagination,
      this.testSearchFilters,
      this.testSearchSorting,
      this.testErrorHandling
    ]

    for (const test of tests) {
      try {
        await test.call(this)
      } catch (error) {
        this.logError(test.name, error)
      }
    }

    this.printTestSummary()
    return this.testResults
  }

  /**
   * 测试搜索建议API
   */
  async testSearchSuggestions() {
    const testName = '搜索建议API'
    console.log(`📝 测试: ${testName}`)

    const testCases = [
      { query: '缺陷', type: 'all', limit: 5 },
      { query: '项目', type: 'projects', limit: 3 },
      { query: '用户', type: 'users', limit: 8 },
      { query: '', type: 'all', limit: 5 }, // 空查询测试
    ]

    for (const testCase of testCases) {
      try {
        const url = `/api/search/suggestions?query=${encodeURIComponent(testCase.query)}&type=${testCase.type}&limit=${testCase.limit}`
        const response = await this.makeRequest('GET', url)
        
        this.validateSuggestionsResponse(response, testCase)
        this.logSuccess(testName, `查询: "${testCase.query}", 类型: ${testCase.type}`)
      } catch (error) {
        this.logError(testName, error, testCase)
      }
    }
  }

  /**
   * 测试基础搜索API
   */
  async testBasicSearch() {
    const testName = '基础搜索API'
    console.log(`📝 测试: ${testName}`)

    const testCases = [
      { query: '登录问题', type: 'all' },
      { query: '缺陷', type: 'defects' },
      { query: '项目管理', type: 'projects' },
      { query: '测试', type: 'coverage' }
    ]

    for (const testCase of testCases) {
      try {
        const url = `/api/search?query=${encodeURIComponent(testCase.query)}&type=${testCase.type}`
        const response = await this.makeRequest('GET', url)
        
        this.validateSearchResponse(response, testCase)
        this.logSuccess(testName, `查询: "${testCase.query}", 类型: ${testCase.type}`)
      } catch (error) {
        this.logError(testName, error, testCase)
      }
    }
  }

  /**
   * 测试高级搜索API
   */
  async testAdvancedSearch() {
    const testName = '高级搜索API'
    console.log(`📝 测试: ${testName}`)

    const testCase = {
      query: '性能',
      type: 'all',
      page: 1,
      page_size: 10,
      sort_by: 'date',
      sort_order: 'desc'
    }

    try {
      const params = new URLSearchParams(testCase).toString()
      const url = `/api/search?${params}`
      const response = await this.makeRequest('GET', url)
      
      this.validateSearchResponse(response, testCase)
      this.logSuccess(testName, '高级搜索参数测试')
    } catch (error) {
      this.logError(testName, error, testCase)
    }
  }

  /**
   * 测试快捷搜索API
   */
  async testQuickSearches() {
    const testName = '快捷搜索API'
    console.log(`📝 测试: ${testName}`)

    try {
      const response = await this.makeRequest('GET', '/api/search/quick-searches')
      
      this.validateQuickSearchesResponse(response)
      this.logSuccess(testName, '快捷搜索列表获取')
    } catch (error) {
      this.logError(testName, error)
    }
  }

  /**
   * 测试搜索统计API
   */
  async testSearchStats() {
    const testName = '搜索统计API'
    console.log(`📝 测试: ${testName}`)

    try {
      const response = await this.makeRequest('GET', '/api/search/stats')
      
      this.validateStatsResponse(response)
      this.logSuccess(testName, '搜索统计数据获取')
    } catch (error) {
      this.logError(testName, error)
    }
  }

  /**
   * 测试搜索分页
   */
  async testSearchPagination() {
    const testName = '搜索分页测试'
    console.log(`📝 测试: ${testName}`)

    const testCases = [
      { query: '测试', page: 1, page_size: 5 },
      { query: '测试', page: 2, page_size: 10 },
      { query: '测试', page: 1, page_size: 20 }
    ]

    for (const testCase of testCases) {
      try {
        const params = new URLSearchParams(testCase).toString()
        const url = `/api/search?${params}`
        const response = await this.makeRequest('GET', url)
        
        this.validatePaginationResponse(response, testCase)
        this.logSuccess(testName, `页码: ${testCase.page}, 大小: ${testCase.page_size}`)
      } catch (error) {
        this.logError(testName, error, testCase)
      }
    }
  }

  /**
   * 测试搜索筛选
   */
  async testSearchFilters() {
    const testName = '搜索筛选测试'
    console.log(`📝 测试: ${testName}`)

    // 注意：这些参数可能需要根据后端实际支持的格式调整
    const testCases = [
      { query: '缺陷', type: 'defects' },
      { query: '项目', type: 'projects' }
    ]

    for (const testCase of testCases) {
      try {
        const params = new URLSearchParams(testCase).toString()
        const url = `/api/search?${params}`
        const response = await this.makeRequest('GET', url)
        
        this.validateSearchResponse(response, testCase)
        this.logSuccess(testName, `筛选类型: ${testCase.type}`)
      } catch (error) {
        this.logError(testName, error, testCase)
      }
    }
  }

  /**
   * 测试搜索排序
   */
  async testSearchSorting() {
    const testName = '搜索排序测试'
    console.log(`📝 测试: ${testName}`)

    const testCases = [
      { query: '测试', sort_by: 'date', sort_order: 'desc' },
      { query: '测试', sort_by: 'title', sort_order: 'asc' },
      { query: '测试', sort_by: 'relevance', sort_order: 'desc' }
    ]

    for (const testCase of testCases) {
      try {
        const params = new URLSearchParams(testCase).toString()
        const url = `/api/search?${params}`
        const response = await this.makeRequest('GET', url)
        
        this.validateSearchResponse(response, testCase)
        this.logSuccess(testName, `排序: ${testCase.sort_by} ${testCase.sort_order}`)
      } catch (error) {
        this.logError(testName, error, testCase)
      }
    }
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    const testName = '错误处理测试'
    console.log(`📝 测试: ${testName}`)

    const testCases = [
      { url: '/api/search/suggestions', expectedError: true }, // 缺少query参数
      { url: '/api/search', expectedError: true }, // 缺少query参数
      { url: '/api/search/nonexistent', expectedError: true } // 不存在的端点
    ]

    for (const testCase of testCases) {
      try {
        const response = await this.makeRequest('GET', testCase.url)
        
        if (testCase.expectedError) {
          this.logError(testName, new Error('期望错误但请求成功'), testCase)
        } else {
          this.logSuccess(testName, `URL: ${testCase.url}`)
        }
      } catch (error) {
        if (testCase.expectedError) {
          this.logSuccess(testName, `正确处理错误: ${testCase.url}`)
        } else {
          this.logError(testName, error, testCase)
        }
      }
    }
  }

  /**
   * 发起HTTP请求
   */
  async makeRequest(method, url, data = null) {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    }

    if (data) {
      options.body = JSON.stringify(data)
    }

    const response = await fetch(fullUrl, options)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * 验证搜索建议响应
   */
  validateSuggestionsResponse(response, testCase) {
    if (!Array.isArray(response)) {
      throw new Error('响应应该是数组')
    }

    if (testCase.query && response.length > testCase.limit) {
      throw new Error(`响应数量超过限制: ${response.length} > ${testCase.limit}`)
    }

    response.forEach((suggestion, index) => {
      if (!suggestion.text || !suggestion.type || !suggestion.category) {
        throw new Error(`建议项 ${index} 缺少必要字段`)
      }
      
      if (typeof suggestion.relevance !== 'number' || suggestion.relevance < 0 || suggestion.relevance > 1) {
        throw new Error(`建议项 ${index} 相关性分数无效`)
      }
    })
  }

  /**
   * 验证搜索响应
   */
  validateSearchResponse(response, testCase) {
    const requiredFields = ['results', 'total', 'page', 'page_size', 'query', 'search_time']
    
    requiredFields.forEach(field => {
      if (!(field in response)) {
        throw new Error(`响应缺少字段: ${field}`)
      }
    })

    if (!Array.isArray(response.results)) {
      throw new Error('results 应该是数组')
    }

    if (typeof response.total !== 'number' || response.total < 0) {
      throw new Error('total 应该是非负数')
    }

    response.results.forEach((result, index) => {
      const requiredResultFields = ['id', 'type', 'title', 'description', 'updated_at']
      requiredResultFields.forEach(field => {
        if (!(field in result)) {
          throw new Error(`结果项 ${index} 缺少字段: ${field}`)
        }
      })
    })
  }

  /**
   * 验证快捷搜索响应
   */
  validateQuickSearchesResponse(response) {
    if (!Array.isArray(response)) {
      throw new Error('响应应该是数组')
    }

    response.forEach((item, index) => {
      const requiredFields = ['id', 'label', 'query', 'type', 'icon', 'count']
      requiredFields.forEach(field => {
        if (!(field in item)) {
          throw new Error(`快捷搜索项 ${index} 缺少字段: ${field}`)
        }
      })
    })
  }

  /**
   * 验证统计响应
   */
  validateStatsResponse(response) {
    const requiredFields = ['total_searches', 'popular_queries', 'search_trends']
    
    requiredFields.forEach(field => {
      if (!(field in response)) {
        throw new Error(`统计响应缺少字段: ${field}`)
      }
    })

    if (!Array.isArray(response.popular_queries)) {
      throw new Error('popular_queries 应该是数组')
    }

    if (!Array.isArray(response.search_trends)) {
      throw new Error('search_trends 应该是数组')
    }
  }

  /**
   * 验证分页响应
   */
  validatePaginationResponse(response, testCase) {
    this.validateSearchResponse(response, testCase)
    
    if (response.page !== testCase.page) {
      throw new Error(`页码不匹配: 期望 ${testCase.page}, 实际 ${response.page}`)
    }

    if (response.page_size !== testCase.page_size) {
      throw new Error(`页面大小不匹配: 期望 ${testCase.page_size}, 实际 ${response.page_size}`)
    }

    if (response.results.length > testCase.page_size) {
      throw new Error(`结果数量超过页面大小: ${response.results.length} > ${testCase.page_size}`)
    }
  }

  /**
   * 记录成功
   */
  logSuccess(testName, details) {
    const result = {
      test: testName,
      status: 'success',
      details,
      timestamp: new Date().toISOString()
    }
    this.testResults.push(result)
    console.log(`✅ ${testName}: ${details}`)
  }

  /**
   * 记录错误
   */
  logError(testName, error, testCase = null) {
    const result = {
      test: testName,
      status: 'error',
      error: error.message,
      testCase,
      timestamp: new Date().toISOString()
    }
    this.testResults.push(result)
    console.error(`❌ ${testName}: ${error.message}`, testCase)
  }

  /**
   * 打印测试摘要
   */
  printTestSummary() {
    const total = this.testResults.length
    const passed = this.testResults.filter(r => r.status === 'success').length
    const failed = total - passed
    
    console.log('\n📊 测试摘要:')
    console.log(`总计: ${total}`)
    console.log(`✅ 通过: ${passed}`)
    console.log(`❌ 失败: ${failed}`)
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`)
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.testResults
        .filter(r => r.status === 'error')
        .forEach(r => console.log(`  - ${r.test}: ${r.error}`))
    }
  }

  /**
   * 获取测试结果
   */
  getTestResults() {
    return {
      results: this.testResults,
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.status === 'success').length,
        failed: this.testResults.filter(r => r.status === 'error').length
      }
    }
  }
}

export default APITester
