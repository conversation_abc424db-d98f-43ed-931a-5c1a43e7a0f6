#!/usr/bin/env node
/**
 * 前端覆盖率模块功能验证脚本
 * 验证前端组件渲染、交互功能和用户体验
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class FrontendVerificationTest {
    constructor(baseUrl = 'http://localhost:5173') {
        this.baseUrl = baseUrl;
        this.browser = null;
        this.page = null;
        this.testResults = [];
    }

    async init() {
        this.browser = await puppeteer.launch({
            headless: false, // 设置为false以便观察测试过程
            defaultViewport: { width: 1280, height: 720 },
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        
        // 设置页面事件监听
        this.page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('❌ 控制台错误:', msg.text());
            }
        });
        
        this.page.on('pageerror', error => {
            console.log('❌ 页面错误:', error.message);
        });
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    logTest(testName, success, message = '', data = null) {
        const result = {
            testName,
            success,
            message,
            timestamp: new Date().toISOString(),
            data
        };
        this.testResults.push(result);

        const status = success ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testName}: ${message}`);

        if (data && !success) {
            console.log(`   详细信息:`, data);
        }
    }

    async testPageLoad() {
        try {
            console.log('🔍 测试页面加载...');
            
            // 导航到覆盖率管理页面
            await this.page.goto(`${this.baseUrl}/coverage-management`, {
                waitUntil: 'networkidle0',
                timeout: 30000
            });

            // 检查页面标题
            const title = await this.page.title();
            if (title.includes('质量大盘')) {
                this.logTest('页面标题检查', true, `页面标题正确: ${title}`);
            } else {
                this.logTest('页面标题检查', false, `页面标题异常: ${title}`);
                return false;
            }

            // 检查主要元素是否存在
            const mainElements = [
                '.coverage-management',
                '.stats-section',
                '.filter-section',
                '.charts-section'
            ];

            for (const selector of mainElements) {
                const element = await this.page.$(selector);
                if (element) {
                    this.logTest(`元素存在检查 ${selector}`, true, '元素正常渲染');
                } else {
                    this.logTest(`元素存在检查 ${selector}`, false, '元素未找到');
                    return false;
                }
            }

            return true;
        } catch (error) {
            this.logTest('页面加载测试', false, `页面加载失败: ${error.message}`);
            return false;
        }
    }

    async testStatsCards() {
        try {
            console.log('🔍 测试统计卡片...');

            // 等待统计卡片加载
            await this.page.waitForSelector('.stat-card', { timeout: 10000 });

            // 检查统计卡片数量
            const statCards = await this.page.$$('.stat-card');
            if (statCards.length >= 3) {
                this.logTest('统计卡片数量', true, `找到 ${statCards.length} 个统计卡片`);
            } else {
                this.logTest('统计卡片数量', false, `统计卡片数量不足: ${statCards.length}`);
                return false;
            }

            // 检查统计卡片内容
            for (let i = 0; i < Math.min(statCards.length, 3); i++) {
                const card = statCards[i];
                const title = await card.$eval('.stat-title', el => el.textContent);
                const value = await card.$eval('.stat-value', el => el.textContent);
                
                if (title && value) {
                    this.logTest(`统计卡片内容 ${i + 1}`, true, `${title}: ${value}`);
                } else {
                    this.logTest(`统计卡片内容 ${i + 1}`, false, '卡片内容缺失');
                    return false;
                }
            }

            return true;
        } catch (error) {
            this.logTest('统计卡片测试', false, `测试失败: ${error.message}`);
            return false;
        }
    }

    async testChartRendering() {
        try {
            console.log('🔍 测试图表渲染...');

            // 等待图表容器加载
            await this.page.waitForSelector('.chart-container', { timeout: 15000 });

            // 检查图表容器数量
            const chartContainers = await this.page.$$('.chart-container');
            if (chartContainers.length >= 2) {
                this.logTest('图表容器数量', true, `找到 ${chartContainers.length} 个图表容器`);
            } else {
                this.logTest('图表容器数量', false, `图表容器数量不足: ${chartContainers.length}`);
                return false;
            }

            // 检查Canvas元素（Chart.js图表）
            const canvasElements = await this.page.$$('canvas');
            if (canvasElements.length >= 2) {
                this.logTest('图表Canvas元素', true, `找到 ${canvasElements.length} 个Canvas图表`);
            } else {
                this.logTest('图表Canvas元素', false, `Canvas图表数量不足: ${canvasElements.length}`);
                return false;
            }

            // 检查热力图
            const heatmapGrid = await this.page.$('.heatmap-grid');
            if (heatmapGrid) {
                const heatmapCells = await this.page.$$('.heatmap-cell');
                this.logTest('热力图渲染', true, `热力图包含 ${heatmapCells.length} 个单元格`);
            } else {
                this.logTest('热力图渲染', false, '热力图未找到');
                return false;
            }

            return true;
        } catch (error) {
            this.logTest('图表渲染测试', false, `测试失败: ${error.message}`);
            return false;
        }
    }

    async testInteractiveFeatures() {
        try {
            console.log('🔍 测试交互功能...');

            // 测试筛选功能
            const filterButton = await this.page.$('.filter-toggle');
            if (filterButton) {
                await filterButton.click();
                await this.page.waitForTimeout(500);
                
                const filterPanel = await this.page.$('.filter-panel');
                if (filterPanel) {
                    this.logTest('筛选面板切换', true, '筛选面板正常显示/隐藏');
                } else {
                    this.logTest('筛选面板切换', false, '筛选面板未正常显示');
                }
            }

            // 测试图表控制
            const chartControls = await this.page.$$('.chart-controls select');
            if (chartControls.length > 0) {
                // 测试第一个下拉选择
                await chartControls[0].select('30d');
                await this.page.waitForTimeout(1000);
                this.logTest('图表控制交互', true, '图表控制选择正常');
            } else {
                this.logTest('图表控制交互', false, '图表控制元素未找到');
            }

            // 测试表格排序
            const sortableHeaders = await this.page.$$('.sortable-header');
            if (sortableHeaders.length > 0) {
                await sortableHeaders[0].click();
                await this.page.waitForTimeout(500);
                this.logTest('表格排序功能', true, '表格排序交互正常');
            } else {
                this.logTest('表格排序功能', false, '可排序表头未找到');
            }

            return true;
        } catch (error) {
            this.logTest('交互功能测试', false, `测试失败: ${error.message}`);
            return false;
        }
    }

    async testResponsiveDesign() {
        try {
            console.log('🔍 测试响应式设计...');

            // 测试桌面视图
            await this.page.setViewport({ width: 1280, height: 720 });
            await this.page.waitForTimeout(500);
            
            const desktopLayout = await this.page.$('.chart-row');
            if (desktopLayout) {
                this.logTest('桌面视图布局', true, '桌面视图布局正常');
            } else {
                this.logTest('桌面视图布局', false, '桌面视图布局异常');
            }

            // 测试平板视图
            await this.page.setViewport({ width: 768, height: 1024 });
            await this.page.waitForTimeout(500);
            this.logTest('平板视图适配', true, '平板视图适配完成');

            // 测试移动视图
            await this.page.setViewport({ width: 375, height: 667 });
            await this.page.waitForTimeout(500);
            this.logTest('移动视图适配', true, '移动视图适配完成');

            // 恢复桌面视图
            await this.page.setViewport({ width: 1280, height: 720 });
            await this.page.waitForTimeout(500);

            return true;
        } catch (error) {
            this.logTest('响应式设计测试', false, `测试失败: ${error.message}`);
            return false;
        }
    }

    async testErrorHandling() {
        try {
            console.log('🔍 测试错误处理...');

            // 模拟网络错误
            await this.page.setOfflineMode(true);
            
            // 尝试刷新数据
            const refreshButton = await this.page.$('.refresh-btn');
            if (refreshButton) {
                await refreshButton.click();
                await this.page.waitForTimeout(2000);
                
                // 检查是否显示错误提示
                const errorMessage = await this.page.$('.error-message, .loading-error');
                if (errorMessage) {
                    this.logTest('网络错误处理', true, '正确显示网络错误提示');
                } else {
                    this.logTest('网络错误处理', false, '未显示网络错误提示');
                }
            }

            // 恢复网络连接
            await this.page.setOfflineMode(false);
            await this.page.waitForTimeout(1000);

            return true;
        } catch (error) {
            this.logTest('错误处理测试', false, `测试失败: ${error.message}`);
            return false;
        }
    }

    async testPerformance() {
        try {
            console.log('🔍 测试性能指标...');

            // 开始性能监控
            await this.page.tracing.start({ path: 'coverage_page_trace.json' });

            // 重新加载页面
            await this.page.reload({ waitUntil: 'networkidle0' });

            // 停止性能监控
            await this.page.tracing.stop();

            // 获取性能指标
            const performanceMetrics = await this.page.evaluate(() => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    loadTime: navigation.loadEventEnd - navigation.loadEventStart,
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0
                };
            });

            if (performanceMetrics.loadTime < 3000) {
                this.logTest('页面加载性能', true, `加载时间: ${performanceMetrics.loadTime.toFixed(2)}ms`);
            } else {
                this.logTest('页面加载性能', false, `加载时间过长: ${performanceMetrics.loadTime.toFixed(2)}ms`);
            }

            return true;
        } catch (error) {
            this.logTest('性能测试', false, `测试失败: ${error.message}`);
            return false;
        }
    }

    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.success).length;
        const failedTests = totalTests - passedTests;

        const report = {
            summary: {
                totalTests,
                passedTests,
                failedTests,
                successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0,
                testTime: new Date().toISOString()
            },
            testResults: this.testResults,
            recommendations: []
        };

        // 生成建议
        if (failedTests === 0) {
            report.recommendations.push('🎉 所有前端测试通过！用户界面功能正常。');
        } else {
            report.recommendations.push(`⚠️  发现 ${failedTests} 个前端问题，需要修复后再发布。`);
        }

        if (passedTests / totalTests >= 0.8) {
            report.recommendations.push('✅ 大部分前端功能正常，用户体验良好。');
        } else {
            report.recommendations.push('❌ 前端功能存在较多问题，建议全面检查。');
        }

        return report;
    }
}

async function main() {
    console.log('🚀 开始前端覆盖率模块功能验证测试...');
    console.log('=' * 60);

    const tester = new FrontendVerificationTest();

    try {
        await tester.init();

        // 1. 页面加载测试
        console.log('\n📋 1. 页面加载测试');
        const pageLoadOk = await tester.testPageLoad();

        if (!pageLoadOk) {
            console.log('❌ 页面加载失败，请确保前端服务正在运行');
            return false;
        }

        // 2. 统计卡片测试
        console.log('\n📋 2. 统计卡片测试');
        await tester.testStatsCards();

        // 3. 图表渲染测试
        console.log('\n📋 3. 图表渲染测试');
        await tester.testChartRendering();

        // 4. 交互功能测试
        console.log('\n📋 4. 交互功能测试');
        await tester.testInteractiveFeatures();

        // 5. 响应式设计测试
        console.log('\n📋 5. 响应式设计测试');
        await tester.testResponsiveDesign();

        // 6. 错误处理测试
        console.log('\n📋 6. 错误处理测试');
        await tester.testErrorHandling();

        // 7. 性能测试
        console.log('\n📋 7. 性能测试');
        await tester.testPerformance();

        // 生成测试报告
        console.log('\n' + '='.repeat(60));
        console.log('📊 前端测试报告生成中...');

        const report = tester.generateReport();

        console.log(`\n📈 前端测试总结:`);
        console.log(`   总测试数: ${report.summary.totalTests}`);
        console.log(`   通过测试: ${report.summary.passedTests}`);
        console.log(`   失败测试: ${report.summary.failedTests}`);
        console.log(`   成功率: ${report.summary.successRate}%`);

        console.log(`\n💡 建议:`);
        report.recommendations.forEach(rec => {
            console.log(`   ${rec}`);
        });

        // 保存详细报告
        const reportFile = `frontend_verification_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        console.log(`\n📄 详细报告已保存到: ${reportFile}`);

        return report.summary.successRate >= 80;

    } catch (error) {
        console.log(`\n❌ 前端测试执行异常: ${error.message}`);
        return false;
    } finally {
        await tester.cleanup();
    }
}

if (require.main === module) {
    main()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 测试执行失败:', error);
            process.exit(1);
        });
}

module.exports = { FrontendVerificationTest };
