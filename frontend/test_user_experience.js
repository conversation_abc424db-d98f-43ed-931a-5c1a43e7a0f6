/**
 * 用户体验测试脚本
 * 测试质量大盘系统的用户交互和体验
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class UserExperienceTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
    this.baseUrl = 'http://localhost:3000';
  }

  async init() {
    console.log('🚀 启动用户体验测试...');
    this.browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口以便观察
      slowMo: 100, // 减慢操作速度以便观察
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async runAllTests() {
    try {
      await this.init();
      
      // 基础功能测试
      await this.testPageLoad();
      await this.testNavigation();
      await this.testResponsiveDesign();
      
      // 个性化仪表板测试
      await this.testDashboardCustomization();
      await this.testConfigImportExport();
      await this.testMetricSelection();
      
      // 图表交互测试
      await this.testChartInteractions();
      await this.testDataFiltering();
      
      // 性能测试
      await this.testLoadingPerformance();
      await this.testMemoryUsage();
      
      // 生成测试报告
      await this.generateReport();
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      this.addResult('测试执行', false, error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async testPageLoad() {
    console.log('📄 测试页面加载...');
    const startTime = Date.now();
    
    try {
      await this.page.goto(this.baseUrl, { waitUntil: 'networkidle2' });
      const loadTime = Date.now() - startTime;
      
      // 检查页面标题
      const title = await this.page.title();
      const hasTitle = title && title.length > 0;
      
      // 检查主要元素是否存在
      const hasHeader = await this.page.$('.bg-primary-700') !== null;
      const hasContent = await this.page.$('.min-h-screen') !== null;
      
      this.addResult('页面加载', hasTitle && hasHeader && hasContent, 
        `加载时间: ${loadTime}ms, 标题: ${title}`);
      
      // 性能检查
      if (loadTime > 3000) {
        this.addResult('页面加载性能', false, `加载时间过长: ${loadTime}ms`);
      } else {
        this.addResult('页面加载性能', true, `加载时间: ${loadTime}ms`);
      }
      
    } catch (error) {
      this.addResult('页面加载', false, error.message);
    }
  }

  async testNavigation() {
    console.log('🧭 测试导航功能...');
    
    try {
      // 测试主导航
      const navLinks = await this.page.$$('nav a, .nav-link');
      const hasNavigation = navLinks.length > 0;
      
      this.addResult('导航菜单', hasNavigation, `找到 ${navLinks.length} 个导航链接`);
      
      // 测试页面切换
      if (navLinks.length > 1) {
        await navLinks[1].click();
        await this.page.waitForTimeout(1000);
        
        const currentUrl = this.page.url();
        const navigationWorks = currentUrl !== this.baseUrl;
        
        this.addResult('页面切换', navigationWorks, `当前URL: ${currentUrl}`);
      }
      
    } catch (error) {
      this.addResult('导航功能', false, error.message);
    }
  }

  async testResponsiveDesign() {
    console.log('📱 测试响应式设计...');
    
    const viewports = [
      { width: 375, height: 667, name: '移动端' },
      { width: 768, height: 1024, name: '平板端' },
      { width: 1920, height: 1080, name: '桌面端' }
    ];
    
    for (const viewport of viewports) {
      try {
        await this.page.setViewport(viewport);
        await this.page.waitForTimeout(500);
        
        // 检查布局是否适应
        const bodyWidth = await this.page.evaluate(() => document.body.scrollWidth);
        const isResponsive = bodyWidth <= viewport.width + 50; // 允许50px误差
        
        this.addResult(`响应式设计-${viewport.name}`, isResponsive, 
          `视口: ${viewport.width}x${viewport.height}, 内容宽度: ${bodyWidth}px`);
        
      } catch (error) {
        this.addResult(`响应式设计-${viewport.name}`, false, error.message);
      }
    }
    
    // 恢复桌面视口
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async testDashboardCustomization() {
    console.log('🎛️ 测试仪表板个性化...');
    
    try {
      // 导航到仪表板页面
      await this.page.goto(`${this.baseUrl}/dashboard`, { waitUntil: 'networkidle2' });
      
      // 查找配置按钮
      const configButton = await this.page.$('[title="配置管理"], .config-btn, button:contains("配置")');
      const hasConfigButton = configButton !== null;
      
      this.addResult('配置按钮', hasConfigButton, '个性化配置入口');
      
      if (hasConfigButton) {
        await configButton.click();
        await this.page.waitForTimeout(1000);
        
        // 检查配置模态框
        const modal = await this.page.$('.modal-overlay, .modal-content');
        const hasModal = modal !== null;
        
        this.addResult('配置模态框', hasModal, '配置界面显示');
        
        if (hasModal) {
          // 关闭模态框
          const closeBtn = await this.page.$('.close-btn, [aria-label="关闭"]');
          if (closeBtn) {
            await closeBtn.click();
            await this.page.waitForTimeout(500);
          }
        }
      }
      
    } catch (error) {
      this.addResult('仪表板个性化', false, error.message);
    }
  }

  async testConfigImportExport() {
    console.log('📁 测试配置导入导出...');
    
    try {
      // 打开配置模态框
      const configButton = await this.page.$('[title="配置管理"], .config-btn');
      if (configButton) {
        await configButton.click();
        await this.page.waitForTimeout(1000);
        
        // 查找导出按钮
        const exportButton = await this.page.$('.export-btn, button:contains("导出")');
        const hasExportButton = exportButton !== null;
        
        this.addResult('导出功能', hasExportButton, '导出按钮存在');
        
        // 查找导入区域
        const importArea = await this.page.$('.import-section, .file-drop-zone');
        const hasImportArea = importArea !== null;
        
        this.addResult('导入功能', hasImportArea, '导入区域存在');
        
        // 关闭模态框
        const closeBtn = await this.page.$('.close-btn');
        if (closeBtn) {
          await closeBtn.click();
          await this.page.waitForTimeout(500);
        }
      }
      
    } catch (error) {
      this.addResult('配置导入导出', false, error.message);
    }
  }

  async testMetricSelection() {
    console.log('📊 测试指标选择...');
    
    try {
      // 查找指标选择相关元素
      const metricCards = await this.page.$$('.metric-card, .card');
      const hasMetricCards = metricCards.length > 0;
      
      this.addResult('指标卡片', hasMetricCards, `找到 ${metricCards.length} 个指标卡片`);
      
      // 查找关注指标设置
      const metricButton = await this.page.$('[title="设置关注指标"], .metric-btn');
      const hasMetricButton = metricButton !== null;
      
      this.addResult('关注指标设置', hasMetricButton, '指标设置功能');
      
    } catch (error) {
      this.addResult('指标选择', false, error.message);
    }
  }

  async testChartInteractions() {
    console.log('📈 测试图表交互...');
    
    try {
      // 查找图表元素
      const charts = await this.page.$$('canvas, .chart-container, [class*="chart"]');
      const hasCharts = charts.length > 0;
      
      this.addResult('图表渲染', hasCharts, `找到 ${charts.length} 个图表`);
      
      if (hasCharts && charts.length > 0) {
        // 测试图表悬停
        await charts[0].hover();
        await this.page.waitForTimeout(500);
        
        // 检查是否有工具提示
        const tooltip = await this.page.$('.tooltip, [role="tooltip"]');
        const hasTooltip = tooltip !== null;
        
        this.addResult('图表交互', hasTooltip, '悬停工具提示');
      }
      
    } catch (error) {
      this.addResult('图表交互', false, error.message);
    }
  }

  async testDataFiltering() {
    console.log('🔍 测试数据筛选...');
    
    try {
      // 查找筛选控件
      const filters = await this.page.$$('select, input[type="search"], .filter-btn');
      const hasFilters = filters.length > 0;
      
      this.addResult('数据筛选', hasFilters, `找到 ${filters.length} 个筛选控件`);
      
      // 测试筛选功能
      if (hasFilters && filters.length > 0) {
        const filter = filters[0];
        const tagName = await filter.evaluate(el => el.tagName.toLowerCase());
        
        if (tagName === 'select') {
          await filter.click();
          await this.page.waitForTimeout(500);
          
          const options = await this.page.$$('option');
          const hasOptions = options.length > 1;
          
          this.addResult('筛选选项', hasOptions, `找到 ${options.length} 个选项`);
        }
      }
      
    } catch (error) {
      this.addResult('数据筛选', false, error.message);
    }
  }

  async testLoadingPerformance() {
    console.log('⚡ 测试加载性能...');
    
    try {
      const metrics = await this.page.metrics();
      
      const performanceData = {
        JSHeapUsedSize: Math.round(metrics.JSHeapUsedSize / 1024 / 1024), // MB
        JSHeapTotalSize: Math.round(metrics.JSHeapTotalSize / 1024 / 1024), // MB
        ScriptDuration: Math.round(metrics.ScriptDuration * 1000), // ms
        TaskDuration: Math.round(metrics.TaskDuration * 1000) // ms
      };
      
      // 性能基准检查
      const isPerformant = 
        performanceData.JSHeapUsedSize < 50 && // 内存使用 < 50MB
        performanceData.ScriptDuration < 1000; // 脚本执行 < 1s
      
      this.addResult('加载性能', isPerformant, 
        `内存: ${performanceData.JSHeapUsedSize}MB, 脚本: ${performanceData.ScriptDuration}ms`);
      
    } catch (error) {
      this.addResult('加载性能', false, error.message);
    }
  }

  async testMemoryUsage() {
    console.log('💾 测试内存使用...');
    
    try {
      // 执行一些操作后检查内存
      await this.page.reload({ waitUntil: 'networkidle2' });
      await this.page.waitForTimeout(2000);
      
      const metrics = await this.page.metrics();
      const memoryUsage = Math.round(metrics.JSHeapUsedSize / 1024 / 1024);
      
      const isMemoryEfficient = memoryUsage < 100; // 内存使用 < 100MB
      
      this.addResult('内存使用', isMemoryEfficient, `内存使用: ${memoryUsage}MB`);
      
    } catch (error) {
      this.addResult('内存使用', false, error.message);
    }
  }

  addResult(testName, passed, details) {
    this.testResults.push({
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${details}`);
  }

  async generateReport() {
    console.log('📋 生成测试报告...');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    const report = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: `${successRate}%`,
        timestamp: new Date().toISOString()
      },
      results: this.testResults,
      recommendations: this.generateRecommendations()
    };
    
    // 保存报告
    const reportPath = path.join(__dirname, 'user_experience_test_report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 测试结果汇总:');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
    
    return report;
  }

  generateRecommendations() {
    const recommendations = [];
    const failedTests = this.testResults.filter(r => !r.passed);
    
    if (failedTests.length === 0) {
      recommendations.push('🎉 所有测试都通过了！系统用户体验良好。');
    } else {
      recommendations.push('🔧 需要改进的方面:');
      
      failedTests.forEach(test => {
        switch (test.test) {
          case '页面加载性能':
            recommendations.push('- 优化页面加载速度，考虑代码分割和懒加载');
            break;
          case '响应式设计':
            recommendations.push('- 改进响应式布局，确保在所有设备上正常显示');
            break;
          case '图表交互':
            recommendations.push('- 增强图表交互功能，添加更多用户反馈');
            break;
          case '内存使用':
            recommendations.push('- 优化内存使用，检查是否有内存泄漏');
            break;
          default:
            recommendations.push(`- 修复 ${test.test} 相关问题`);
        }
      });
    }
    
    return recommendations;
  }
}

// 运行测试
if (require.main === module) {
  const test = new UserExperienceTest();
  test.runAllTests().catch(console.error);
}

module.exports = UserExperienceTest;
